{"name": "lifty-lite", "version": "0.1.0", "private": true, "description": "Kiosk app, Transit Tracker, and Fixed Route planning tool all in one", "type": "module", "dependencies": {"@cypress/browserify-preprocessor": "^3.0.2", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@harelpls/use-pusher": "^7.2.1", "@ionic/react": "6", "@ionic/react-router": "6", "@liftango/ops-client": "8.5.1", "@liftango/polygon-point": "4.12.3", "@liftango/service-toolkit": "4.13.21", "@react-google-maps/api": "^2.1.1", "@react-hook/window-size": "^3.0.7", "@reduxjs/toolkit": "^1.5.0", "@testing-library/jest-dom": "^5.11.9", "@testing-library/react": "^11.2.5", "@testing-library/user-event": "^12.6.3", "@xstate/react": "^1.3.2", "convert-units": "^2.3.4", "cypress-cucumber-preprocessor": "^4.3.1", "cypress-mochawesome-reporter": "^3.2.3", "cypress-multi-reporters": "^2.0.4", "date-fns": "^2.21.1", "i18next": "^20.2.1", "ionicons": "^5.4.0", "lottie-react": "^2.1.0", "multiple-cucumber-html-reporter": "1.21.6", "react": "^17.0.2", "react-dom": "^17.0.2", "react-i18next": "^11.8.13", "react-places-autocomplete": "^7.3.0", "react-redux": "^7.2.3", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-swipeable": "^6.2.0", "styled-components": "^5.2.3", "usehooks-ts": "^3.1.0", "web-vitals": "^0.2.4", "xstate": "^4.18.0"}, "scripts": {"start": "vite dev", "dev": "vite dev", "build": "vite build", "test": "vitest", "storybook": "start-storybook -p 6006 -s public", "build-storybook": "build-storybook -s public", "update-liftango-clients": "yarn remove @liftango/liftango-client @liftango/ops-client @liftango/service-toolkit @liftango/polygon-point && yarn add @liftango/ops-client @liftango/service-toolkit @liftango/polygon-point -E && yarn add @liftango/liftango-client -D -E", "cy:run": "cypress run", "cy:open": "cypress open"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "overrides": [{"files": ["**/*.stories.*"], "rules": {"import/no-anonymous-default-export": "off"}}]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@capacitor/cli": "2.4.7", "@liftango/liftango-client": "4.17.2", "@storybook/addon-actions": "^6.2.8", "@storybook/addon-essentials": "^6.2.8", "@storybook/addon-links": "^6.2.8", "@storybook/node-logger": "^6.2.8", "@storybook/preset-create-react-app": "^3.1.7", "@storybook/react": "^6.2.8", "@testing-library/jest-dom": "^5.11.9", "@testing-library/react": "^11.2.5", "@types/convert-units": "^2.3.3", "@types/googlemaps": "^3.43.3", "@types/jest": "^26.0.20", "@types/node": "^12.19.15", "@types/react": "^17.0.2", "@types/react-dom": "^17.0.2", "@types/react-places-autocomplete": "^7.2.8", "@types/react-router": "^5.1.11", "@types/react-router-dom": "^5.1.7", "@types/styled-components": "^5.1.9", "@vitejs/plugin-react": "^4.2.1", "babel-preset-vite": "^1.1.3", "cypress": "12.10.0", "lint-staged": ">=10", "mochawesome": "^7.1.3", "mochawesome-merge": "^4.3.0", "mochawesome-report-generator": "^6.2.0", "prettier": "^2.2.1", "react-scripts": "5.0.0", "redux-mock-store": "^1.5.4", "simple-git-hooks": ">=2.0.3", "typescript": "~4.2.4", "vite": "^5.2.11", "vite-plugin-dynamic-import": "^1.5.0", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.6.0"}, "cypress-cucumber-preprocessor": {"nonGlobalStepDefinitions": true, "cucumberJson": {"generate": true, "outputFolder": "cypress/cucumber-json", "filePrefix": "", "fileSuffix": ".cucumber"}}, "lint-staged": {"**/*": "prettier --write --ignore-unknown", "*.{js,css,md}": "prettier --write"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "resolutions": {"styled-components": "^5", "@storybook/react/babel-loader": "8.1.0"}, "packageManager": "yarn@4.3.0"}