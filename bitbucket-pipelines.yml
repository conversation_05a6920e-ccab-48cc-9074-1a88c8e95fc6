image:
  name: atlassian/default-image:4

definitions:
  caches:
    yarn: /usr/local/share/.cache/yarn
  scripts:
    - script: &install-dependencies corepack enable && yarn config set nodeLinker node-modules && yarn config set npmAuthToken ${NPM_TOKEN} && yarn config set npmRegistryServer ${NPM_REGISTRY_URL:-https://registry.npmjs.org} && yarn --version
    - script: &change-aws-sandbox export AWS_ACCESS_KEY_ID=$SANDBOX_AWS_ACCESS_KEY_ID && export AWS_SECRET_ACCESS_KEY=$SANDBOX_AWS_SECRET_ACCESS_KEY && export AWS_DEFAULT_REGION=us-east-2
    - script:
        - &git-merge-down-setup git remote set-<NAME_EMAIL>:liftango/lifty-lite.git;
          git config remote.origin.fetch "+refs/heads/*:refs/remotes/origin/*";
          git fetch --unshallow;
          git fetch origin
    - script:
        - &aws-context unset AWS_ACCESS_KEY_ID;
          unset AWS_SECRET_ACCESS_KEY;
          export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token;
          export AWS_ROLE_ARN=$VAR_AWS_ROLE_ARN;
          echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token

        - &aws-context-sandbox unset AWS_ACCESS_KEY_ID;
          unset AWS_SECRET_ACCESS_KEY;
          export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token;
          export AWS_ROLE_ARN=$VAR_AWS_SANDBOX_ROLE_ARN;
          echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token

  steps:
    - step: &build-test
        name: Build and test
        image: node:20.13.1
        caches:
          - node
          - yarn
        script:
          - *install-dependencies
          - if [ "$BITBUCKET_BRANCH" == "main" ]; then export NODE_ENV=production; elif [ "$BITBUCKET_BRANCH" == "uat" ]; then export NODE_ENV=production; else export NODE_ENV=development; fi
          - echo $NODE_ENV
          - touch .env
          - yarn install --frozen-lockfile
          - yarn build
          - yarn test
        artifacts:
          - build/**

pipelines:
  pull-requests:
    '**':
      - step: *build-test
  branches:
    develop:
      - step: *build-test
      - step:
          name: TF Plan Dev usea2
          oidc: true
          image: hashicorp/terraform:1.2.6
          script:
            - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601480580/g' ./build/index.html
            - cd terraform
            - *aws-context-sandbox
            - ./scripts/validate.sh terraform-tfstates-usea2 us-east-2 dev
            - ./scripts/plan.sh terraform-tfstates-usea2 us-east-2 dev
      - step:
          name: TF Apply Dev usea2
          oidc: true
          image: hashicorp/terraform:1.2.6
          deployment: dev-usea2
          script:
            - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601480580/g' ./build/index.html
            - cd terraform
            - *aws-context-sandbox
            - ./scripts/validate.sh terraform-tfstates-usea2 us-east-2 dev
            - ./scripts/apply.sh terraform-tfstates-usea2 us-east-2 dev
    qa:
      - step: *build-test
      - step:
          name: Merge qa into develop and releases
          script:
            - *git-merge-down-setup
            - ./pipelines/merge-down.sh qa develop
            - ./pipelines/merge-down.sh qa 'release/*'
      - step:
          name: TF Plan QA usea2
          oidc: true
          image: hashicorp/terraform:1.2.6
          script:
            - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601490308/g' ./build/index.html
            - cd terraform
            - *aws-context-sandbox
            - ./scripts/validate.sh terraform-tfstates-usea2 us-east-2 qa
            - ./scripts/plan.sh terraform-tfstates-usea2 us-east-2 qa
      - step:
          name: TF Apply QA usea2
          oidc: true
          image: hashicorp/terraform:1.2.6
          deployment: qa-usea2
          script:
            - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601490308/g' ./build/index.html
            - cd terraform
            - *aws-context-sandbox
            - ./scripts/validate.sh terraform-tfstates-usea2 us-east-2 qa
            - ./scripts/apply.sh terraform-tfstates-usea2 us-east-2 qa
    uat:
      - step: *build-test
      - step:
          name: Merge uat into qa
          script:
            - *git-merge-down-setup
            - ./pipelines/merge-down.sh uat qa
      - parallel:
          - step:
              name: TF Plan UAT apse2
              oidc: true
              image: hashicorp/terraform:1.2.6
              script:
                - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601473470/g' ./build/index.html
                - cd terraform
                - *aws-context
                - ./scripts/validate.sh terraform-tfstates-apse2 ap-southeast-2 uat
                - ./scripts/plan.sh terraform-tfstates-apse2 ap-southeast-2 uat
          - step:
              name: TF Plan UAT euwe2
              oidc: true
              image: hashicorp/terraform:1.2.6
              script:
                - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601473471/g' ./build/index.html
                - cd terraform
                - *aws-context
                - ./scripts/validate.sh terraform-tfstates-euwe2 eu-west-2 uat
                - ./scripts/plan.sh terraform-tfstates-euwe2 eu-west-2 uat
      - parallel:
          - step:
              name: TF Apply UAT apse2
              oidc: true
              image: hashicorp/terraform:1.2.6
              deployment: uat-apse2
              trigger: manual
              script:
                - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601473470/g' ./build/index.html
                - cd terraform
                - *aws-context
                - ./scripts/validate.sh terraform-tfstates-apse2 ap-southeast-2 uat
                - ./scripts/apply.sh terraform-tfstates-apse2 ap-southeast-2 uat
          - step:
              name: TF Apply UAT euwe2
              image: hashicorp/terraform:1.2.6
              deployment: uat-euwe2
              oidc: true
              trigger: manual
              script:
                - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601473471/g' ./build/index.html
                - cd terraform
                - *aws-context
                - ./scripts/validate.sh terraform-tfstates-euwe2 eu-west-2 uat
                - ./scripts/apply.sh terraform-tfstates-euwe2 eu-west-2 uat
    main:
      - step: *build-test
      - step:
          name: Merge main into uat
          script:
            - *git-merge-down-setup
            - ./pipelines/merge-down.sh main uat
      - parallel:
          - step:
              name: TF Plan Prod apse2
              oidc: true
              image: hashicorp/terraform:1.2.6
              script:
                - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601473472/g' ./build/index.html
                - cd terraform
                - *aws-context
                - ./scripts/validate.sh terraform-tfstates-apse2 ap-southeast-2 prod
                - ./scripts/plan.sh terraform-tfstates-apse2 ap-southeast-2 prod
          - step:
              name: TF Plan Prod euwe2
              oidc: true
              image: hashicorp/terraform:1.2.6
              script:
                - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601473473/g' ./build/index.html
                - cd terraform
                - *aws-context
                - ./scripts/validate.sh terraform-tfstates-euwe2 eu-west-2 prod
                - ./scripts/plan.sh terraform-tfstates-euwe2 eu-west-2 prod
      - parallel:
          - step:
              name: TF Apply Prod apse2
              image: hashicorp/terraform:1.2.6
              deployment: prod-apse2
              oidc: true
              trigger: manual
              script:
                - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601473472/g' ./build/index.html
                - cd terraform
                - *aws-context
                - ./scripts/validate.sh terraform-tfstates-apse2 ap-southeast-2 prod
                - ./scripts/apply.sh terraform-tfstates-apse2 ap-southeast-2 prod
          - step:
              name: TF Apply Prod euwe2
              image: hashicorp/terraform:1.2.6
              deployment: prod-euwe2
              oidc: true
              trigger: manual
              script:
                - sed -i 's/VITE_REACT_APP_NR_AGENT_ID/601473473/g' ./build/index.html
                - cd terraform
                - *aws-context
                - ./scripts/validate.sh terraform-tfstates-euwe2 eu-west-2 prod
                - ./scripts/apply.sh terraform-tfstates-euwe2 eu-west-2 prod

  custom:
    shared:
      - parallel:
          - step:
              name: TF Plan Shared apse2
              oidc: true
              image: hashicorp/terraform:1.2.6
              script:
                - cd terraform/shared
                - *aws-context
                - ./scripts/init.sh terraform-tfstates-apse2 ap-southeast-2
                - terraform plan -var-file="./environments/apse2.tfvars"
          - step:
              name: TF Plan Shared euwe2
              oidc: true
              image: hashicorp/terraform:1.2.6
              script:
                - cd terraform/shared
                - *aws-context
                - ./scripts/init.sh terraform-tfstates-euwe2 eu-west-2
                - terraform plan -var-file="./environments/euwe2.tfvars"
          - step:
              name: TF Plan Shared usea2
              oidc: true
              image: hashicorp/terraform:1.2.6
              script:
                - cd terraform/shared
                - *aws-context-sandbox
                - ./scripts/init.sh terraform-tfstates-usea2 us-east-2
                - terraform plan -var-file="./environments/usea2.tfvars"
      - parallel:
          - step:
              name: TF Apply Shared apse2
              oidc: true
              image: hashicorp/terraform:1.2.6
              trigger: manual
              deployment: shared-apse2
              script:
                - cd terraform/shared
                - *aws-context
                - ./scripts/init.sh terraform-tfstates-apse2 ap-southeast-2
                - terraform apply -var-file="./environments/apse2.tfvars" -input=false -auto-approve
          - step:
              name: TF Apply Shared euwe2
              oidc: true
              image: hashicorp/terraform:1.2.6
              trigger: manual
              deployment: shared-euwe2
              script:
                - cd terraform/shared
                - *aws-context
                - ./scripts/init.sh terraform-tfstates-euwe2 eu-west-2
                - terraform apply -var-file="./environments/euwe2.tfvars" -input=false -auto-approve
          - step:
              name: TF Apply Shared usea2
              oidc: true
              image: hashicorp/terraform:1.2.6
              trigger: manual
              deployment: shared-usea2
              script:
                - cd terraform/shared
                - *aws-context-sandbox
                - ./scripts/init.sh terraform-tfstates-usea2 us-east-2
                - terraform apply -var-file="./environments/usea2.tfvars" -input=false -auto-approve
