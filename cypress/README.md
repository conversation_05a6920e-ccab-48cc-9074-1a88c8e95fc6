# Cypress Test Suite Documentation

## Introduction

This document provides an overview of the Cypress test suite for Lifty Lite and highlights test maintainability and readability.

Our Cypress tests follow BDD principles to ensure that tests remain concise and focused on verifying expected behaviors. Complex tests can signal code design issues, so we aim to keep our tests clear and maintainable to provide reliable validation of our application's functionality.

## Behavior-Driven Development (BDD)

At the core of our Cypress test suite is the adoption of Behavior-Driven Development (BDD) principles. BDD is a software development approach that encourages collaboration between developers, testers, and non-technical stakeholders to define and test application behaviors. It focuses on describing how an application should behave from a user's perspective.

**Benefits of BDD:**

- Improved Collaboration: BDD promotes communication between team members, ensuring a shared understanding of application behavior.
- Readable Tests: Tests are written in plain language that non-technical stakeholders can understand, making them accessible to everyone involved.
- Maintainability: BDD encourages modular and organized test code, making it easier to maintain and update tests as the application evolves.
- Documentation: BDD tests serve as documentation of the expected behavior of the application, reducing the need for separate documentation.

## Recent Improvements

**Separation of Steps**

In our Cypress test suite we have a separation of test steps into reusable step definitions. This separation makes our tests more modular, readable, and maintainable.

**Common Steps for Reuse**

By making our steps more common, we enable them to be shared and reused across different features. This means that if a particular step is used in multiple test scenarios, we can define it once and use it wherever needed. This not only reduces duplication but also ensures consistency in our tests.

**Organized Step Definition Files**

To keep our step definitions organized, we've created separate files for different types of steps:

- **when.ts:** This file contains step definitions for actions or events that occur in the application (e.g., clicking buttons, entering data).

- **then.ts:** Here, we define step definitions for assertions and verifications. These steps help ensure that the application behaves as expected.

- **given.ts:** In this file, we define step definitions for setting up the initial state or context of a scenario. This can include navigating to a specific page or preparing data.

This separation of steps into different files makes it easier to locate and maintain step definitions, especially as our test suite continues to grow.

This approach not only improves test organization but also facilitates easy sharing and reuse of steps across different scenarios and features.

## Dynamic Environment Configuration

To enhance test flexibility, we have dynamic environment configuration using fixtures. This allows us to define different environment variables and switch between environments easily.

**Fixture Data:**

```json
{
  "local": {
    "homePageUrl": "http://localhost:3006/home",
    "pusherUrl": "http://localhost:3000/"
  },
  "dev": {
    "homePageUrl": "http://dev.com/home"
  },
  "prod": {
    "homePageUrl": "http://prod.com/home"
  }
}
```

**Usage:**

```javascript
beforeEach(() => {
  cy.fixture < EnvironmentVars > 'environmentVars'.as('envVars');

  cy.get <
    EnvironmentVars >
    '@envVars'.then((envVars) => {
      // Access properties using dynamic keys, e.g., envVars['local'].homePageUrl
      const baseUrl = envVars['local'].pusherUrl; // Change 'local' to the desired environment's key

      cy.intercept('GET', `${baseUrl}/context/fixed-route/q`, (req) => {
        if (req.query.type === 'getrunningshiftsolutions') {
          req.reply({
            statusCode: 200,
            body: {
              shiftSolutions: [],
            },
          });
        }
      }).as('getRunningShiftSolutions');
    });
});
```

These improvements to our Cypress test suite enhance test readability, maintainability, and flexibility. By following BDD principles and adopting modular step definitions, we aim to ensure that our tests remain a valuable asset in validating the functionality of our application.
