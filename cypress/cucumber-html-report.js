const multipleCucumberHtmlReporter = require('multiple-cucumber-html-reporter');

multipleCucumberHtmlReporter.generate({
  jsonDir: 'cypress/cucumber-json', // Directory where your JSON cucumber reports are located
  reportPath: 'cypress/reports/cucumber-html-report', // Directory to save the HTML report
  openReportInBrowser: true, // Open the HTML report in a browser
  metaData: {
    browser: {
      name: 'chrome',
      version: '80',
    },
    device: 'Local test machine',
    platform: {
      name: 'macOS',
      version: '10.15.3',
    },
  },
  customData: {
    title: 'Run info',
    data: [
      { label: 'Project', value: 'Lifty Lite' },
      { label: 'Release', value: '1.2.3' },
      { label: 'Cycle', value: 'B11221.34321' },
      { label: 'Execution Start Time', value: 'Nov 27th 2023, 14:31 PM EST' },
      { label: 'Execution End Time', value: 'Nov 27th 2023, 14:32 PM EST' },
    ],
  },
});
