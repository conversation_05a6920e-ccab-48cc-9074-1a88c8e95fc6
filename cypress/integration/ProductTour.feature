Feature: Product Tour Functionality

    Scenario: Interacting with the Produxt Tour
        Given I am on the home page
        Then I should see the element with the selector 'route-filtering-menu-icon'
        When I click on the element with data-testid 'route-filtering-menu-icon'
        Then I wait for the element with the selector 'product-tour' to appear

        Then the title of the slide at index 0 should be 'Routes'
        Then the text of the element with data-testid 'button-product-tour' should be 'Next'

        When I click on the element with data-testid 'button-product-tour'
        Then the title of the slide at index 1 should be 'Shuttles and stops'

        When I click on the element with data-testid 'button-product-tour'
        Then the title of the slide at index 2 should be 'Plan your journey'
        Then the text of the element with data-testid 'button-product-tour' should be 'Close'

        When I click on the element with data-testid 'button-product-tour'
        Then the element with data-testid 'product-tour' should not exist
