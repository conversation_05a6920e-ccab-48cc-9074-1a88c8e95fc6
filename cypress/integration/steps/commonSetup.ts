import { IEnvironmentVars } from './IEnvironments';

beforeEach(() => {
  cy.fixture<IEnvironmentVars>('environmentVars').as('envVars');

  cy.get<IEnvironmentVars>('@envVars').then((envVars) => {
    const baseUrl = envVars['local'].pusherUrl;

    cy.intercept('GET', `${baseUrl}/context/fixed-route/q`, (req) => {
      if (req.query.type === 'getrunningshiftsolutions') {
        req.reply({
          statusCode: 200,
          body: {
            shiftSolutions: [],
          },
        });
      }
    }).as('getRunningShiftSolutions');
  });
});
