import { Then } from 'cypress-cucumber-preprocessor/steps';

Then('the {string} field should be visible', (fieldId) => {
  cy.get(`[data-testid="${fieldId}"]`).should('be.visible');
});

Then('the {string} field value should be {string}', (fieldId, expectedValue) => {
  cy.get(`[data-testid="${fieldId}"]`).invoke('val').should('eq', expectedValue);
});

Then('I should see the element with the selector {string}', (selector) => {
  const finalSelector = selector.startsWith('[') ? selector : `[data-testid="${selector}"]`;
  cy.get(finalSelector).should('be.visible');
});

Then('I wait for the element with the selector {string} to appear', (selector) => {
  const finalSelector = selector.startsWith('[') ? selector : `[data-testid="${selector}"]`;
  cy.get(finalSelector, { timeout: 10000 }).should('be.visible');
});

Then('the title of the slide at index {int} should be {string}', (index, expectedTitle) => {
  cy.get('[data-testid="title-product-tour"]').eq(index).should('have.text', expectedTitle);
});

Then('the text of the element with data-testid {string} should be {string}', (dataTestId, expectedText) => {
  cy.get(`[data-testid="${dataTestId}"]`).should('have.text', expectedText);
});

Then('the element with data-testid {string} should not be visible', (dataTestId) => {
  cy.get(`[data-testid="${dataTestId}"]`).should('not.be.visible');
});

Then('the element with data-testid {string} should not exist', (dataTestId) => {
  cy.get(`[data-testid="${dataTestId}"]`).should('not.exist');
});
