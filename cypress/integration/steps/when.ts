import { When } from 'cypress-cucumber-preprocessor/steps';

When('I type {string} into the {string} field', (text: string, fieldId: string) => {
  cy.get(`[data-testid="${fieldId}"]`).type(text);
});

When('I click the first suggestion in the {string} suggestion box', (boxIndex: string) => {
  cy.get(`div[data-testid="autocomplete-desktop"] > div:nth-child(${boxIndex === 'first' ? 1 : 2}) .sc-avfBU:first-child`).click();
});

When('I click on the element with data-testid {string}', (dataTestId) => {
  cy.get(`[data-testid="${dataTestId}"]`).click();
});
