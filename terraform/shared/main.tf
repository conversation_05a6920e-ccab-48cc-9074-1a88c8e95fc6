data "aws_iam_policy_document" "s3_bucket_policy" {
  dynamic "statement" {
    for_each = { for idx, access in var.cf_access_list : idx => access }

    content {
      sid    = "AllowCloudFrontServicePrincipal-${statement.value.path}"
      effect = "Allow"

      principals {
        type        = "Service"
        identifiers = ["cloudfront.amazonaws.com"]
      }

      actions   = ["s3:GetObject"]
      resources = ["arn:aws:s3:::${var.bucket_name}/${statement.value.path}/*"]

      condition {
        test     = "StringEquals"
        variable = "AWS:SourceArn"
        values   = [statement.value.cfarn]
      }
    }
  }

  statement {
    sid = "EnforceHTTPS"

    effect = "Deny"

    actions = ["s3:*"]

    resources = [
      "${aws_s3_bucket.default.arn}/*",
      "${aws_s3_bucket.default.arn}"
    ]

    condition {
      test     = "Bool"
      variable = "aws:SecureTransport"
      values = [
        "false",
      ]
    }

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

  }
}

resource "aws_s3_bucket" "default" {
  bucket = var.bucket_name
}

resource "aws_s3_bucket_policy" "s3_bucket_policy" {
  bucket = aws_s3_bucket.default.id
  policy = data.aws_iam_policy_document.s3_bucket_policy.json
}
