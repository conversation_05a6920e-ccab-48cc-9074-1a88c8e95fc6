locals {
  mime_types = {
    "css"  = "text/css"
    "html" = "text/html"
    "ico"  = "image/vnd.microsoft.icon"
    "js"   = "application/javascript"
    "json" = "application/json"
    "map"  = "application/json"
    "png"  = "image/png"
    "jpg"  = "image/jpeg"
    "svg"  = "image/svg+xml"
    "txt"  = "text/plain"
    "otf"  = "font/otf"
    "ttf"  = "font/ttf"
  }
}

data "aws_route53_zone" "public" {
  name         = var.external_zone_name
  private_zone = false
}

data "aws_s3_bucket" "root" {
  bucket = var.bucket_name
}

resource "aws_s3_object" "objects" {
  for_each = fileset("../build/", "**/*.*")

  bucket       = data.aws_s3_bucket.root.id
  key          = "${var.env}/${each.value}"
  source       = "../build/${each.value}"
  content_type = lookup(tomap(local.mime_types), element(split(".", each.key), length(split(".", each.key)) - 1))
  etag         = filemd5("../build/${each.value}")
}

resource "aws_route53_record" "records" {
  for_each = { for url in var.site_urls : url => url }

  depends_on = [
    aws_cloudfront_distribution.default
  ]

  zone_id = data.aws_route53_zone.public.zone_id
  name    = each.key
  ttl     = 300
  type    = "CNAME"

  records = [aws_cloudfront_distribution.default.domain_name]
}

resource "aws_cloudfront_origin_access_control" "oac" {
  name                              = "${var.site_name}-oac"
  description                       = "${var.site_name} Origin Access Control"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

/**
* When a new cloudfront distribution is created
* We need to update the bucket policy in /terraform/shared/main.tf
* Adding a new statement to allow the CF distro to access a specific bucket path
* The changes can be applied by runing a 'shared' custom pipeline in lifty-lite bitbucket
*/
resource "aws_cloudfront_distribution" "default" {
  origin {
    domain_name              = data.aws_s3_bucket.root.bucket_regional_domain_name
    origin_id                = data.aws_s3_bucket.root.bucket_regional_domain_name
    origin_path              = "/${var.env}"
    origin_access_control_id = aws_cloudfront_origin_access_control.oac.id
  }

  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"

  aliases = [
    for url in var.site_urls :
    (var.is_sandbox ? "${url}.sandbox.liftango.com" : "${url}.liftango.com")
  ]

  default_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
    ]

    cached_methods = [
      "GET",
      "HEAD",
    ]

    forwarded_values {
      query_string = true

      cookies {
        forward = "all"
      }
    }

    target_origin_id       = data.aws_s3_bucket.root.bucket_regional_domain_name
    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    min_ttl                = 2
    default_ttl            = 2
    max_ttl                = 600
  }

  custom_error_response {
    error_caching_min_ttl = 10
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
  }

  custom_error_response {
    error_caching_min_ttl = 10
    error_code            = 404
    response_code         = 200
    response_page_path    = "/index.html"
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
      locations        = []
    }
  }

  viewer_certificate {
    acm_certificate_arn            = var.aws_cf_acm_arn
    cloudfront_default_certificate = false
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }

  wait_for_deployment = false
}
