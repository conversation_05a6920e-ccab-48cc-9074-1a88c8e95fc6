variable "region" {
  description = "region to deploy the resources"
}

variable "env" {
  description = "environment name"
}

variable "bucket_name" {
  description = "s3 bucket name"
}

variable "site_name" {
  description = "url website name"
}

variable "site_urls" {
  description = "list of urls for the website"
  type        = list(string)
  default     = []
}

variable "aws_cf_acm_arn" {
  description = "Certificate for CloudFront (requires to be us-east-1)"
  default     = "arn:aws:acm:us-east-1:************:certificate/09c4d4ac-116c-4d45-9986-f72564ee20ff"
}

variable "external_zone_name" {
  description = "External Public Zone Name"
  default     = "liftango.com."
}

variable "is_sandbox" {
  description = "Whether or not this applies to the Sandbox account"
  default     = false
}