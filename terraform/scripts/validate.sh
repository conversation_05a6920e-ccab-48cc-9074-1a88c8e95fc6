#!/bin/sh
# USAGE: ./validate.sh <bucket-name> <region> <env-name>
BUCKET_NAME=${1}
REGION=${2}
ENV=${3}

if [ $# -ne 3 ]
  then
    echo "Not enough arguments supplied. Expected arguments: Bucket, Region, Environment"
    exit
fi

terraform init -backend=true -backend-config="bucket=${BUCKET_NAME}" -backend-config="key=lifty-lite/${ENV}/terraform.tfstate" -backend-config="region=${REGION}" -backend-config="encrypt=true"

terraform validate