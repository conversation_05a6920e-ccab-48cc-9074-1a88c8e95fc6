env         = "uat"
region      = "ap-southeast-2"
bucket_name = "lifty-lite-apse2"
site_name   = "lifty-lite-uat-apse2"
site_urls = [
  "lifty-lite-uat",
  "a1parkingshuttle-uat",
  "abbotsford-airport-uat",
  "at-local-fixed-route-uat",
  "bna9-uat",
  "bwi2-uat",
  "csumb-uat",
  "chop-uat",
  "dvc4-uat",
  "dvc6-uat",
  "dyt3-uat",
  "fulton-labs-uat",
  "lax-105-uat",
  "lax-shuttle-uat",
  "lucketts-shuttle-uat",
  "mount-sinai-uat",
  "nike-shuttle-uat",
  "perrone-robotics-uat",
  "sutter-health-uat",
  "tvp-shuttle-uat",
  "walmart-shuttle-uat",
  "wcsu-uat",
  "woodlands-uat",
  "ygk1-uat",
  "yvr4-uat",
  "yxx2-uat",
  "360-green-uat",
  "forsyth-uat",
  "netflix-shuttle-uat",
  "uca1-uat",
  "rchmb-uat",
  "beverly-hilton-uat",
  "netflix-shuttle-lg-uat",
  "steamboat-shuttle-uat",
  "mit-shuttle-uat",
  "mercy-general-uat",
  "tesla-uat",
  "prudential-uat",
  "johannesburg-shuttle-uat",
  "dvv2-uat",
  "odot-uat",
  "summit-county-uat",
  "sdap-uat"
]
