import { assign, TransitionConfig, createMachine } from 'xstate';
import { MachineConfig, StateMachine } from 'xstate/lib/types';
import { FixedRoute } from '@liftango/ops-client';
import { OnDemandTripJourneyLeg } from '@liftango/liftango-client/src/rides/ride-payload';
import { ActiveTimeType } from '../../components/features/DateTimeSelector/DateTimeSelector';

export type FixedRouteMatchLeg = OnDemandTripJourneyLeg &
  (
    | {
        travelType: 'bus';
        availableCapacities: {
          amount: number;
          capacityName: string;
          capacityTypeId: string;
          displayOrder: number;
        }[];
        vehicle: {
          number: string;
          displayName: string;
          image: string;
          capacity: FixedRoute.AvailableCapacity[];
          route: {
            id: string;
            label: string;
            colour: string;
          };
        };
      }
    | { travelType: 'foot' }
  );

export type FixedRouteMatch = {
  journeyData: {
    journey: {
      journeyLegs: FixedRouteMatchLeg[];
    };
  };
  startAddress: {
    label: string;
    placeId: string;
    lat: number;
    lng: number;
    hub?: {
      label: string;
    };
  };
  endAddress: {
    label: string;
    placeId: string;
    lat: number;
    lng: number;
    hub?: {
      name: string;
    };
  };
  startTime: string;
  endTime: string;
};

interface FIXED_ROUTE_MACHINE_SCHEMA {
  states: {
    ready: {};
    find_match: {};
    results: {};
    view_trip: {};
    start_trip: {};
  };
}

type FIXED_ROUTE_MACHINE_STATE =
  | { value: 'ready'; context: FIXED_ROUTE_MACHINE_CONTEXT }
  | {
      value: 'find_match';
      context: FIXED_ROUTE_MACHINE_CONTEXT & {
        arrivalLocation: Location;
        departureLocation: Location;
        departureDateTime: string;
      };
    }
  | {
      value: 'results';
      context: FIXED_ROUTE_MACHINE_CONTEXT & {
        match: FixedRouteMatch;
      };
    }
  | {
      value: 'view_trip';
      context: FIXED_ROUTE_MACHINE_CONTEXT & {
        trip: FixedRouteMatch;
      };
    }
  | {
      value: 'start_trip';
      context: FIXED_ROUTE_MACHINE_CONTEXT & {
        trip: FixedRouteMatch;
      };
    };

export type Location = {
  label: string;
  latitude: number;
  longitude: number;
  placeId: string;
};

export type TimeType = 'leaveAt' | 'arriveBy';

export interface FIXED_ROUTE_MACHINE_CONTEXT {
  resetKey: number;
  arrivalLocation: Location | null;
  departureLocation: Location | null;
  departureDateTime: string | null;
  timeType: ActiveTimeType;
  passengers: number;
  match: { recommended: FixedRouteMatch; alternate: FixedRouteMatch[] } | null;
  trip: FixedRouteMatch | null;
  error: string;
}

type FIXED_ROUTE_MACHINE_EVENTS =
  | { type: 'SET_ARRIVAL'; arrivalLocation: Location | null }
  | { type: 'SET_DEPARTURE'; departureLocation: Location | null }
  | {
      type: 'SET_DEPARTURE_TIME';
      departureDateTime: string;
      timeType: TimeType;
    }
  | {
      type: 'SET_PASSENGERS';
      passengers: number;
    }
  | { type: 'SWAP_ADDRESSES'; arrivalLocation: Location; departureLocation: Location }
  | { type: 'NEXT' }
  | { type: 'BACK'; trip: FixedRouteMatch | null; match: FixedRouteMatch | null }
  | { type: 'RETRY' }
  | { type: 'VIEW_TRIP'; trip: FixedRouteMatch }
  | { type: 'START_TRIP' }
  | { type: 'SET_CONFIRMATION'; arrivalLocation?: Location; departureLocation?: Location }
  | { type: 'START_AGAIN' }
  | { type: 'FIND_MATCH_START' }
  | { type: 'FIND_MATCH_SUCCESSFUL'; match: { recommended: FixedRouteMatch; alternate: FixedRouteMatch[] } }
  | { type: 'FIND_MATCH_ERROR'; error: string };

export type FixedRouteMachineConfig = MachineConfig<FIXED_ROUTE_MACHINE_CONTEXT, FIXED_ROUTE_MACHINE_SCHEMA, FIXED_ROUTE_MACHINE_EVENTS>;

export type FixedRouteMachine = StateMachine<
  FIXED_ROUTE_MACHINE_CONTEXT,
  FIXED_ROUTE_MACHINE_SCHEMA,
  FIXED_ROUTE_MACHINE_EVENTS,
  FIXED_ROUTE_MACHINE_STATE
>;

/**
 * This is our XState machine which controls at which stage the Ride Creator in.
 * It includes controls for assigning and validating specific properties into a context when we
 * jump between different stages. This helps ensure that a stage is always valid and that
 * the data in the context for each specific linear step is valid.
 *
 * It can be loaded into a visualizer here for an interactive flow: https://xstate.js.org/viz/
 */
const initialState: FIXED_ROUTE_MACHINE_CONTEXT = {
  resetKey: new Date().getTime(),
  arrivalLocation: null,
  departureLocation: null,
  departureDateTime: null,
  timeType: 'leaveNow',
  passengers: 1,
  match: null,
  trip: null,
  error: '',
};

const startAgainEvent = (
  target: '#root.ready' | 'ready' = 'ready',
): TransitionConfig<FIXED_ROUTE_MACHINE_CONTEXT, { type: 'START_AGAIN' }> => ({
  target,
  actions: assign({
    departureLocation: (context, event) => initialState.departureLocation,
    arrivalLocation: (context, event) => initialState.arrivalLocation,
    departureDateTime: (context, event) => initialState.departureDateTime,
    passengers: (context, event) => initialState.passengers,
    timeType: (context, event) => initialState.timeType,
    match: (context, event) => initialState.match,
    trip: (context, event) => initialState.trip,
    error: (context, event) => initialState.error,
    resetKey: (context, event) => new Date().getTime(),
  }),
});

const FixedRouteConfig: FixedRouteMachineConfig = {
  id: 'booking_machine',
  key: 'root',
  initial: 'ready',
  context: initialState,
  states: {
    ready: {
      on: {
        SET_DEPARTURE: [
          {
            actions: assign({
              departureLocation: (context, event) => event.departureLocation,
            }),
          },
        ],
        SET_ARRIVAL: [
          {
            actions: assign({
              arrivalLocation: (context, event) => event.arrivalLocation,
            }),
          },
        ],
        SET_PASSENGERS: [
          {
            actions: assign({
              passengers: (context, event) => event.passengers,
            }),
          },
        ],
        SET_CONFIRMATION: [
          {
            cond: (context) => !!context.arrivalLocation && !!context.departureLocation,
            actions: assign({
              arrivalLocation: (context, event) => event.arrivalLocation ?? context.arrivalLocation,
              departureLocation: (context, event) => event.departureLocation ?? context.departureLocation,
            }),
          },
        ],
        SET_DEPARTURE_TIME: {
          actions: assign({
            departureDateTime: (context, event) => event.departureDateTime,
            timeType: (context, event) => event.timeType,
          }),
        },
        SWAP_ADDRESSES: {
          actions: assign({
            arrivalLocation: (context) => context.departureLocation,
            departureLocation: (context) => context.arrivalLocation,
          }),
        },
        START_AGAIN: startAgainEvent(),
        FIND_MATCH_START: {
          target: 'find_match',
          actions: assign({
            error: (context) => '',
          }),
        },
      },
    },
    find_match: {
      on: {
        FIND_MATCH_SUCCESSFUL: [
          {
            target: '#root.results',
            cond: (context, event) => !!event.match,
            actions: assign({
              match: (context, event) => event.match,
              error: (context) => '',
            }),
          },
        ],
        FIND_MATCH_ERROR: {
          target: '#root.ready',
          actions: assign({
            error: (context, event) => event.error,
          }),
        },
      },
    },
    results: {
      on: {
        BACK: {
          target: 'ready',
          actions: assign(initialState),
        },
        START_AGAIN: startAgainEvent(),
        VIEW_TRIP: {
          target: '#root.view_trip',
          cond: (context, event) => !!event.trip,
          actions: assign({
            trip: (context, event) => event.trip,
          }),
        },
        SET_DEPARTURE: [
          {
            actions: assign({
              departureLocation: (context, event) => event.departureLocation,
            }),
          },
        ],
        SET_ARRIVAL: [
          {
            actions: assign({
              arrivalLocation: (context, event) => event.arrivalLocation,
            }),
          },
        ],
        SET_PASSENGERS: [
          {
            actions: assign({
              passengers: (context, event) => event.passengers,
            }),
          },
        ],
        SET_DEPARTURE_TIME: {
          actions: assign({
            departureDateTime: (context, event) => event.departureDateTime,
            timeType: (context, event) => event.timeType,
          }),
        },
        SWAP_ADDRESSES: {
          actions: assign({
            arrivalLocation: (context) => context.departureLocation,
            departureLocation: (context) => context.arrivalLocation,
          }),
        },
        FIND_MATCH_START: {
          target: 'find_match',
          actions: assign({
            error: (context) => '',
          }),
        },
      },
    },
    view_trip: {
      on: {
        START_TRIP: '#root.start_trip',
        START_AGAIN: startAgainEvent(),
        BACK: {
          target: '#root.results',
          actions: assign({
            match: (context, event) => context.match,
            trip: (context, event) => null,
          }),
        },
      },
    },
    start_trip: {
      on: {
        START_AGAIN: startAgainEvent(),
        BACK: '#root.view_trip',
      },
    },
  },
};

export default createMachine<FIXED_ROUTE_MACHINE_CONTEXT, FIXED_ROUTE_MACHINE_EVENTS, FIXED_ROUTE_MACHINE_STATE>(FixedRouteConfig);
