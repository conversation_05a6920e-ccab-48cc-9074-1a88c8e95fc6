import { Kios<PERSON> } from '@liftango/ops-client';
import { assign, TransitionConfig, createMachine } from 'xstate';
import { MachineConfig, StateMachine } from 'xstate/lib/types';
import { ActiveTimeType } from '../../components/features/DateTimeSelector/DateTimeSelector';

interface KIOSK_BOOKING_MACHINE_SCHEMA {
  states: {
    ready: {};
    match: {
      states: {
        creating: {};
        finding: {};
      };
    };
    summary: {
      states: {
        edit: {};
        booking: {};
      };
    };
    no_booking: {};
    confirmed: {
      states: {
        edit: {};
        sending: {};
      };
    };
    summary_sent: {};
  };
}

type KIOSK_BOOKING_MACHINE_STATE =
  | { value: 'ready'; context: KIOSK_BOOKING_MACHINE_CONTEXT }
  | {
      value: 'match' | { match: 'creating' } | { match: 'finding' };
      context: KIOSK_BOOKING_MACHINE_CONTEXT & {
        arrivalLocation: Location;
        departureLocation: Location;
        departureDateTime: string;
      };
    }
  | {
      value: 'summary' | { summary: 'edit' } | { summary: 'booking' };
      context: KIOSK_BOOKING_MACHINE_CONTEXT & {
        match: Kiosk.RideMatch;
      };
    }
  | { value: 'no_booking'; context: KIOSK_BOOKING_MACHINE_CONTEXT & { booking: null } }
  | {
      value: 'confirmed' | { confirmed: 'edit' } | { confirmed: 'sending' };
      context: KIOSK_BOOKING_MACHINE_CONTEXT & {
        booking: Kiosk.BookingDetailed;
      };
    }
  | {
      value: 'summary_sent';
      context: KIOSK_BOOKING_MACHINE_CONTEXT & {
        booking: Kiosk.BookingDetailed;
      };
    };

type Location = {
  label: string;
  latitude: number;
  longitude: number;
  placeId: string;
};

type PassengerLuggage = {
  type: string;
  label: string;
  description: string;
  count: number;
};

export type TimeType = 'leaveAt' | 'arriveBy';

export type Companion = {
  id: string;
  seatType: 'standard' | 'wheelchair' | 'child' | null;
  concessionType: string | null;
  luggage: PassengerLuggage[];
};

export interface KIOSK_BOOKING_MACHINE_CONTEXT {
  resetKey: number;
  arrivalLocation: Location | null;
  departureLocation: Location | null;
  departureDateTime: string | null;
  timeType: ActiveTimeType;
  companions: Companion[];
  details: {
    firstName: string;
    lastName: string;
    mobile: string;
  };
  booking: Kiosk.BookingDetailed | null;
  match: Kiosk.RideMatch | null;
  error: string;
}

type KIOSK_BOOKING_MACHINE_EVENTS =
  | { type: 'SET_ARRIVAL'; arrivalLocation: Location | null }
  | { type: 'SET_DEPARTURE'; departureLocation: Location | null }
  | {
      type: 'SET_COMPANIONS';
      companions: Companion[];
    }
  | {
      type: 'SET_DEPARTURE_TIME';
      departureDateTime: string;
      timeType: ActiveTimeType;
    }
  | {
      type: 'SET_FIRSTNAME';
      firstName: string;
    }
  | {
      type: 'SET_LASTNAME';
      lastName: string;
    }
  | { type: 'SWAP_ADDRESSES'; arrivalLocation: Location; departureLocation: Location }
  | { type: 'SET_SUMMARY' }
  | { type: 'NEXT' }
  | { type: 'BACK'; match: null }
  | { type: 'RETRY' }
  | { type: 'SET_CONFIRMATION'; arrivalLocation?: Location; departureLocation?: Location }
  | { type: 'START_AGAIN' }
  | { type: 'CONFIRMED' }
  | { type: 'NO_BOOKING'; booking: null }
  | { type: 'SET_MOBILE'; mobile: string }
  | { type: 'CREATE_BOOKING_START' }
  | { type: 'CREATE_BOOKING_SUCCESSFUL' }
  | { type: 'CREATE_BOOKING_ERROR'; error: string }
  | { type: 'FIND_MATCH_START' }
  | { type: 'FIND_MATCH_SUCCESSFUL'; match: Kiosk.RideMatch }
  | { type: 'FIND_MATCH_ERROR'; error: string }
  | { type: 'CONFIRM_BOOKING_START' }
  | { type: 'CONFIRM_BOOKING_SUCCESSFUL'; booking: Kiosk.BookingDetailed }
  | { type: 'UPDATE_BOOKING'; booking: Kiosk.BookingDetailed }
  | { type: 'CONFIRM_BOOKING_ERROR' }
  | { type: 'SUMMARY_SEND_START' }
  | { type: 'SUMMARY_SEND_SUCCESSFUL' }
  | { type: 'SUMMARY_SEND_ERROR'; error: string };

export type KioskBookingMachineConfig = MachineConfig<
  KIOSK_BOOKING_MACHINE_CONTEXT,
  KIOSK_BOOKING_MACHINE_SCHEMA,
  KIOSK_BOOKING_MACHINE_EVENTS
>;

export type KioskBookingMachine = StateMachine<
  KIOSK_BOOKING_MACHINE_CONTEXT,
  KIOSK_BOOKING_MACHINE_SCHEMA,
  KIOSK_BOOKING_MACHINE_EVENTS,
  KIOSK_BOOKING_MACHINE_STATE
>;

/**
 * This is our XState machine which controls at which stage the Ride Creator in.
 * It includes controls for assigning and validating specific properties into a context when we
 * jump between different stages. This helps ensure that a stage is always valid and that
 * the data in the context for each specific linear step is valid.
 *
 * It can be loaded into a visualizer here for an interactive flow: https://xstate.js.org/viz/
 */
const initialState: KIOSK_BOOKING_MACHINE_CONTEXT = {
  resetKey: new Date().getTime(),
  arrivalLocation: null,
  departureLocation: null,
  departureDateTime: null,
  timeType: 'leaveAt',
  companions: [
    {
      id: 'Me',
      seatType: 'standard',
      concessionType: null,
      luggage: [],
    },
  ],
  details: {
    firstName: '',
    lastName: '',
    mobile: '',
  },
  booking: null,
  match: null,
  error: '',
};

const startAgainEvent = (
  target: '#root.ready' | 'ready' = 'ready',
): TransitionConfig<KIOSK_BOOKING_MACHINE_CONTEXT, { type: 'START_AGAIN' }> => ({
  target,
  actions: assign({
    departureLocation: (context, event) => initialState.departureLocation,
    arrivalLocation: (context, event) => initialState.arrivalLocation,
    companions: (context, event) => initialState.companions,
    departureDateTime: (context, event) => initialState.departureDateTime,
    timeType: (context, event) => initialState.timeType,
    details: (context, event) => initialState.details,
    booking: (context, event) => initialState.booking,
    match: (context, event) => initialState.match,
    error: (context, event) => initialState.error,
    resetKey: (context, event) => new Date().getTime(),
  }),
});

const kioskBookingConfig: KioskBookingMachineConfig = {
  id: 'booking_machine',
  key: 'root',
  initial: 'ready',
  context: initialState,
  states: {
    ready: {
      on: {
        SET_DEPARTURE: [
          {
            actions: assign({
              departureLocation: (context, event) => event.departureLocation,
            }),
          },
        ],
        SET_ARRIVAL: [
          {
            actions: assign({
              arrivalLocation: (context, event) => event.arrivalLocation,
            }),
          },
        ],
        SET_CONFIRMATION: [
          {
            cond: (context) => !!context.arrivalLocation && !!context.departureLocation,
            actions: assign({
              arrivalLocation: (context, event) => event.arrivalLocation ?? context.arrivalLocation,
              departureLocation: (context, event) => event.departureLocation ?? context.departureLocation,
            }),
          },
        ],
        SET_COMPANIONS: {
          actions: assign({
            companions: (context, event) => event.companions,
          }),
        },
        SET_DEPARTURE_TIME: {
          actions: assign({
            departureDateTime: (context, event) => event.departureDateTime,
            timeType: (context, event) => event.timeType,
          }),
        },
        SWAP_ADDRESSES: {
          actions: assign({
            arrivalLocation: (context) => context.departureLocation,
            departureLocation: (context) => context.arrivalLocation,
          }),
        },
        START_AGAIN: startAgainEvent(),
        CREATE_BOOKING_START: {
          target: 'match',
          actions: assign({
            error: (context) => '',
          }),
        },
      },
    },
    match: {
      initial: 'creating',
      states: {
        creating: {
          on: {
            CREATE_BOOKING_SUCCESSFUL: 'finding',
            CREATE_BOOKING_ERROR: {
              target: '#root.ready',
              actions: assign({
                error: (context, event) => event.error,
              }),
            },
          },
        },
        finding: {
          on: {
            FIND_MATCH_SUCCESSFUL: [
              {
                target: '#root.summary',
                cond: (context, event) => !!event.match,
                actions: assign({
                  match: (context, event) => event.match,
                  error: (context) => '',
                }),
              },
            ],
            FIND_MATCH_ERROR: {
              target: '#root.ready',
              actions: assign({
                error: (context, event) => event.error,
              }),
            },
          },
        },
      },
    },
    summary: {
      initial: 'edit',
      states: {
        edit: {
          on: {
            CONFIRM_BOOKING_START: 'booking',
          },
        },
        booking: {
          on: {
            CONFIRM_BOOKING_SUCCESSFUL: {
              target: '#root.confirmed',
              cond: (context, event) => !!event.booking,
              actions: assign({
                booking: (context, event) => event.booking,

                error: (context) => '',
              }),
            },
            CONFIRM_BOOKING_ERROR: '#root.no_booking',
          },
        },
      },
      on: {
        BACK: {
          target: 'ready',
          actions: assign({
            match: (context, event) => null,
            details: (context, event) => ({
              firstName: '',
              lastName: '',
              mobile: '',
            }),
          }),
        },
        START_AGAIN: startAgainEvent(),
        SET_FIRSTNAME: {
          actions: assign({
            details: (context, event) => {
              return {
                ...context.details,
                firstName: event.firstName,
              };
            },
          }),
        },
        SET_LASTNAME: {
          actions: assign({
            details: (context, event) => {
              return {
                ...context.details,
                lastName: event.lastName,
              };
            },
          }),
        },
      },
    },
    no_booking: {
      on: {
        START_AGAIN: startAgainEvent(),
      },
    },
    confirmed: {
      initial: 'edit',
      states: {
        edit: {
          on: {
            UPDATE_BOOKING: {
              cond: (context, event) => !!event.booking,
              actions: assign({
                booking: (context, event) => event.booking,
              }),
            },
            SUMMARY_SEND_START: {
              target: 'sending',
              actions: assign({
                error: (context) => '',
              }),
            },
          },
        },
        sending: {
          on: {
            SUMMARY_SEND_SUCCESSFUL: {
              target: '#root.summary_sent',
              actions: assign({
                error: (context) => '',
              }),
            },
            SUMMARY_SEND_ERROR: {
              target: 'edit',
              actions: assign({
                error: (context, event) => event.error,
              }),
            },
          },
        },
      },
      on: {
        START_AGAIN: startAgainEvent(),
        SET_MOBILE: {
          actions: assign({
            details: (context, event) => {
              return {
                ...context.details,
                mobile: event.mobile,
              };
            },
          }),
        },
      },
    },
    summary_sent: {
      on: {
        START_AGAIN: startAgainEvent(),
      },
    },
  },
};

export default createMachine<KIOSK_BOOKING_MACHINE_CONTEXT, KIOSK_BOOKING_MACHINE_EVENTS, KIOSK_BOOKING_MACHINE_STATE>(kioskBookingConfig);
