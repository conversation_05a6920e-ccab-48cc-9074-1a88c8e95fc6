import React from 'react';
import { Redirect, Route } from 'react-router-dom';
import { IonRouterOutlet } from '@ionic/react';
import FixedHome from '../../pages/FixedRoute/FixedRouteHome';

const FixedRoutes: React.FC = () => {
  return (
    <IonRouterOutlet>
      <Route exact path="/home">
        <FixedHome />
      </Route>

      <Route
        exact
        path="/"
        render={({ location }) => (
          <Redirect
            to={{
              pathname: '/home',
              search: location.search,
            }}
          />
        )}
      />
    </IonRouterOutlet>
  );
};

export default FixedRoutes;
