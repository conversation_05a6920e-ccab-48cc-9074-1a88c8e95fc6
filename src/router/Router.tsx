import React, { lazy, Suspense } from 'react';
import { useAppSelector } from '../hooks/storeHooks';
import { selectApplicationMode } from '../store/reducers/applicationReducer';
import Loading from '../containers/Loading/Loading';
import { IonReactRouter } from '@ionic/react-router';
import history from './history';

export const ROUTE_URL_PARAM = 'displayedRoutes';

const KioskRoutes = lazy(() => import('./Kiosk/KioskRoutes'));
const FixedRoutes = lazy(() => import('./FixedRoute/FixedRoutes'));

const Router: React.FC = () => {
  const application = useAppSelector(selectApplicationMode);

  return (
    <Suspense fallback={<Loading />}>
      {application === 'kiosk' ? (
        <IonReactRouter history={history}>
          <KioskRoutes />
        </IonReactRouter>
      ) : application === 'fixed_route' ? (
        <IonReactRouter history={history}>
          <FixedRoutes />
        </IonReactRouter>
      ) : null}
    </Suspense>
  );
};

export default Router;
