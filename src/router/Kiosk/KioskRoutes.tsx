import React from 'react';
import { Redirect, Route } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import { IonIcon, IonLabel, IonRouterOutlet, IonTabBar, IonTabButton, IonTabs } from '@ionic/react';
import { mapOutline, carOutline } from 'ionicons/icons';
import KioskHome from '../../pages/Kiosk/KioskHome';
import KioskMapHome from '../../pages/Kiosk/KioskMapHome';
import { useWindowWidth } from '@react-hook/window-size';
import { BREAKPOINT_MD } from '../../constants/breakpoints';

const KioskRoutes: React.FC = () => {
  const windowWidth: number = useWindowWidth();
  const isMobileMode: boolean = windowWidth <= BREAKPOINT_MD;

  const location = useLocation();

  return (
    <IonTabs>
      <IonRouterOutlet>
        <Route exact path="/home">
          <KioskHome />
        </Route>
        <Route path="/map">
          <KioskMapHome />
        </Route>
        <Route exact path="/">
          <Redirect to="/home" />
        </Route>
      </IonRouterOutlet>

      <IonTabBar slot="bottom" style={isMobileMode || location.pathname !== '/home' ? undefined : { display: 'none' }}>
        <IonTabButton tab="home" href="/home">
          <IonIcon icon={carOutline} />
          <IonLabel>Trip</IonLabel>
        </IonTabButton>
        <IonTabButton tab="map" href="/map">
          <IonIcon icon={mapOutline} />
          <IonLabel>Map</IonLabel>
        </IonTabButton>
      </IonTabBar>
    </IonTabs>
  );
};

export default KioskRoutes;
