import React from 'react';
import ReactDOM from 'react-dom';
import { Provider } from 'react-redux';
import App from './App';

import './i18n';

import store from './store/global';
import HailerContextProvider from './context/HailerProvider';
import PusherProvider from './context/PusherProvider';
import ConfigProvider from './context/ConfigProvider';

ReactDOM.render(
  <React.StrictMode>
    <ConfigProvider hostname={new URL(window.location.href).hostname}>
      <PusherProvider>
        <Provider store={store}>
          <HailerContextProvider>
            <App />
          </HailerContextProvider>
        </Provider>
      </PusherProvider>
    </ConfigProvider>
  </React.StrictMode>,
  document.getElementById('root'),
);
