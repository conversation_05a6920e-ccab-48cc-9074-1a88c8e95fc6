import React, { createContext, ReactNode, useMemo } from 'react';
import { AppConfig, getNetworkConfig } from './NetworkConfigs';

type ConfigProviderProps = {
  hostname: string;
  children: ReactNode;
};

export const ConfigContext = createContext<AppConfig>({
  environment: 'dev',
  region: 'apse2',
  networkId: '',
  apiUrl: '',
  appTheme: 'liftango',
  appMode: 'fixed_route',
  appTitle: '',
  googleMapsApiKey: '',
  useGoogleMapsAddresses: true,
  pusherCluster: undefined,
  pusherKey: undefined,
});

const ConfigProvider = (props: ConfigProviderProps) => {
  const config: AppConfig = useMemo(() => {
    const parts: string[] = props.hostname.split('.');
    const subdomain: string = parts.length >= 3 ? parts[0] : 'localhost';

    if (subdomain === 'localhost') {
      console.warn('Sudbdomain Config resolved to localhost');
    }

    return getNetworkConfig(subdomain);
  }, [props.hostname]);

  return <ConfigContext.Provider value={config}>{props.children}</ConfigContext.Provider>;
};

export default ConfigProvider;
