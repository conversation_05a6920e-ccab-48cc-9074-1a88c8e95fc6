import { AppConfig, buildAppConfig, getNetworkConfig, PUSHER_KEYS } from './NetworkConfigs';

describe('NetworkConfigs', () => {
  it('dev network, fallback title', async () => {
    const config: Partial<AppConfig> = {
      environment: 'dev',
      region: 'usea2', // shouldn't matter
    };
    const result = buildAppConfig(config);
    expect(result.apiUrl).toEqual('https://hailer-dev.sandbox.liftango.com');
    expect(result.pusherKey).toEqual(PUSHER_KEYS.dev);
    expect(result.appTitle).toEqual('Liftango On-Demand');
  });

  it('qa network', async () => {
    const config: Partial<AppConfig> = {
      environment: 'qa',
    };
    const result = buildAppConfig(config);
    expect(result.apiUrl).toEqual('https://hailer-qa.sandbox.liftango.com');
    expect(result.pusherKey).toEqual(PUSHER_KEYS.qa);
  });

  it('uat apse2 network', async () => {
    const config: Partial<AppConfig> = {
      environment: 'uat',
      region: 'apse2',
      appTitle: 'testtitle',
    };
    const result = buildAppConfig(config);
    expect(result.apiUrl).toEqual('https://hailer-odb-uat.liftango.com');
    expect(result.pusherKey).toEqual(PUSHER_KEYS.uat);
    expect(result.appTitle).toEqual(config.appTitle);
  });

  it('prod apse2 network', async () => {
    const config: Partial<AppConfig> = {
      environment: 'prod',
      region: 'apse2',
      appTitle: 'testtitle',
    };
    const result = buildAppConfig(config);
    expect(result.apiUrl).toEqual('https://hailer-odb-prod.liftango.com');
    expect(result.pusherKey).toEqual(PUSHER_KEYS.prod);
    expect(result.appTitle).toEqual(config.appTitle);
  });

  it('uat euwe2 network', async () => {
    const config: Partial<AppConfig> = {
      environment: 'uat',
      region: 'euwe2',
      appTitle: 'testtitle',
    };
    const result = buildAppConfig(config);
    expect(result.apiUrl).toEqual('https://hailer-odb-uat-euwe2.liftango.com');
    expect(result.pusherKey).toEqual(PUSHER_KEYS.uat);
    expect(result.appTitle).toEqual(config.appTitle);
  });

  it('prod euwe2 api network', async () => {
    const config: Partial<AppConfig> = {
      environment: 'prod',
      region: 'euwe2',
      appTitle: 'testtitle',
    };
    const result = buildAppConfig(config);
    expect(result.apiUrl).toEqual('https://hailer-odb-prod-euwe2.liftango.com');
    expect(result.pusherKey).toEqual(PUSHER_KEYS.prod);
  });

  it('apse2 apiurl & pusherkey override network', async () => {
    const config: Partial<AppConfig> = {
      environment: 'prod',
      region: 'euwe2',
      apiUrl: 'https://overriddenurl.test',
      pusherKey: 'overriddenkey',
    };
    const result = buildAppConfig(config);
    expect(result.apiUrl).toEqual(config.apiUrl);
    expect(result.pusherKey).toEqual(config.pusherKey);
  });
});
