import { createContext } from 'react';
import { ApplicationMode } from '../store/reducers/applicationReducer';
import { ThemeName } from '../store/reducers/themeReducer';
import { ALLOWED_ENV } from '@liftango/ops-client';

type AWS_REGION = 'apse2' | 'euwe2' | 'usea2';

export type AppConfig = {
  environment: ALLOWED_ENV;
  region: AWS_REGION;
  apiUrl: string;
  networkId: string;
  appTheme: ThemeName;
  appMode: ApplicationMode;
  appTitle: string;
  googleMapsApiKey: string;
  useGoogleMapsAddresses: boolean;
  pusherCluster: string | undefined;
  pusherKey: string | undefined;
};

export const PUSHER_KEYS: { [key in ALLOWED_ENV]?: string } = {
  dev: '40669126bc4c9c25caf1',
  qa: 'de14beaab6cdacfa2cb6',
  uat: '6d3c620d2ad03f3fc815',
  prod: 'a3dd7ec3dba6f67900f3',
};

const getPusherKey = (networkConfig: Partial<AppConfig>): string | undefined => {
  if (networkConfig.pusherKey) {
    return networkConfig.pusherKey;
  }
  if (networkConfig.environment && networkConfig.environment in PUSHER_KEYS) {
    return PUSHER_KEYS[networkConfig.environment];
  }
  return defaultAppConfig.pusherKey;
};

const getApiUrl = (networkConfig: Partial<AppConfig>): string => {
  // if (networkConfig.apiUrl) {
  //   return networkConfig.apiUrl;
  // } else {
  //   let env = '';
  //   switch (networkConfig.environment) {
  //     case 'dev':
  //       env = 'dev.sandbox';
  //       break;
  //     case 'qa':
  //       env = 'qa.sandbox';
  //       break;
  //     case 'uat':
  //       env = 'odb-uat';
  //       break;
  //     case 'prod':
  //       env = 'odb-prod';
  //       break;
  //   }
  //   // apse2 is default/no-suffix, and usea2 is only for sandbox which is already handled by the env component above
  //   const region = networkConfig.region === 'euwe2' ? '-euwe2' : '';

  //   return `https://hailer-${env}${region}.liftango.com`;
  // }

  return 'http://*************:3000';
};

const defaultAppConfig: AppConfig = {
  environment: 'dev',
  region: 'apse2',
  networkId: '',
  apiUrl: 'https://hailer-dev.sandbox.liftango.com',
  appTheme: 'liftango',
  appMode: 'fixed_route',
  appTitle: 'Liftango On-Demand',
  googleMapsApiKey: 'AIzaSyDN_50bgnHhCab7TrHwapUC6CbtxhEK8nQ',
  useGoogleMapsAddresses: true,
  pusherCluster: 'ap4',
  pusherKey: PUSHER_KEYS.dev,
};

const networkConfig: { [key: string]: Pick<AppConfig, 'networkId' | 'environment' | 'region'> & Partial<AppConfig> } = {
  localhost: {
    networkId: import.meta.env.VITE_REACT_APP_NETWORK_ID ?? '',
    apiUrl: import.meta.env.VITE_REACT_APP_HAILER_BASE_URL ?? '',
    appTheme: (import.meta.env.VITE_REACT_APP_THEME as ThemeName) ?? 'liftango',
    appMode: (import.meta.env.VITE_REACT_APP_APPLICATION_MODE as ApplicationMode) ?? 'fixed_route',
    appTitle: import.meta.env.VITE_REACT_APP_TITLE ?? 'Liftango On-Demand',
    googleMapsApiKey: import.meta.env.VITE_REACT_APP_GOOGLE_MAPS_API_KEY ?? '',
    useGoogleMapsAddresses: import.meta.env.VITE_REACT_APP_USE_GOOGLE_MAPS_ADDRESSES === 'true',
    pusherCluster: import.meta.env.VITE_REACT_APP_PUSHER_CLUSTER ?? 'ap4',
    pusherKey: import.meta.env.VITE_REACT_APP_PUSHER_KEY,
    region: defaultAppConfig.region, // not important for localhost, apiUrl and pusherKey specified anyway
    environment: defaultAppConfig.environment, // not important for localhost, apiUrl and pusherKey specified anyway
  },
  'lifty-lite-dev': {
    environment: 'dev',
    region: 'usea2',
    networkId: '91702a7c-9a3c-4539-b24a-c5a52fec1819',
  },
  'lifty-lite-qa': {
    environment: 'qa',
    region: 'usea2',
    networkId: '91702a7c-9a3c-4539-b24a-c5a52fec1819',
    googleMapsApiKey: 'AIzaSyB_N86K8HCeRgk8W_oDgts4Ghqx3uvSqF8',
  },
  'lifty-lite-uat': {
    environment: 'uat',
    networkId: '91702a7c-9a3c-4539-b24a-c5a52fec1819',
    region: 'apse2',
  },
  a1parkingshuttle: {
    environment: 'prod',
    networkId: '02bb7c2b-545a-49cf-be11-7a31571ed742',
    region: 'apse2',
    appTitle: 'A1 Parking Shuttle',
    useGoogleMapsAddresses: false,
  },
  'a1parkingshuttle-uat': {
    environment: 'uat',
    networkId: '02bb7c2b-545a-49cf-be11-7a31571ed742',
    region: 'apse2',
    appTitle: 'A1 Parking Shuttle',
    useGoogleMapsAddresses: false,
  },
  'abbotsford-airport': {
    environment: 'prod',
    networkId: 'd2cb7bec-17b1-46ab-a7f9-e3f271ce004b',
    region: 'apse2',
    appTitle: 'Abbotsford Airport',
  },
  'abbotsford-airport-uat': {
    environment: 'uat',
    networkId: 'd2cb7bec-17b1-46ab-a7f9-e3f271ce004b',
    region: 'apse2',
    appTitle: 'Abbotsford Airport',
  },
  'aladdin-airport-shuttle': {
    environment: 'prod',
    networkId: 'a483f84d-827a-43c5-94b6-ecca30516f2a',
    region: 'apse2',
    appTitle: 'Aladdin Airport Shuttle',
  },
  'aldar-dubai': {
    environment: 'prod',
    networkId: 'aa670564-b5f8-48ce-a5e1-b71dc5655ec3',
    region: 'euwe2',
    appTitle: 'ALDAR',
  },
  'aldar-dubai-uat': {
    environment: 'uat',
    networkId: 'aa670564-b5f8-48ce-a5e1-b71dc5655ec3',
    region: 'euwe2',
    appTitle: 'ALDAR',
  },
  'at-local-fixed-route-uat': {
    environment: 'uat',
    networkId: 'f142b8ab-079e-4660-8f05-f39642e8d616',
    region: 'apse2',
    appTitle: 'AT Local',
    useGoogleMapsAddresses: false,
  },
  bna9: {
    environment: 'prod',
    networkId: 'af9b713b-374d-433c-81da-cc91d433f705',
    region: 'apse2',
    appTitle: 'Amazon BNA9',
  },
  'bna9-uat': {
    environment: 'uat',
    networkId: 'af9b713b-374d-433c-81da-cc91d433f705',
    region: 'apse2',
    appTitle: 'Amazon BNA9',
  },
  bwi2: {
    environment: 'prod',
    networkId: '0b52fcab-7315-4980-982f-217929b22fbb',
    region: 'apse2',
    appTitle: 'Amazon BWI2',
  },
  'bwi2-uat': {
    environment: 'uat',
    networkId: '0b52fcab-7315-4980-982f-217929b22fbb',
    region: 'apse2',
    appTitle: 'Amazon BWI2',
  },
  'city-school-prod': {
    environment: 'prod',
    networkId: '0f05c665-903d-433e-87ed-0774dbf3a4d4',
    region: 'apse2',
    appTitle: 'City School',
  },
  'campbells-whq': {
    environment: 'prod',
    networkId: 'b0556fbc-9886-454c-a0b1-c51779fb5c01',
    region: 'apse2',
    appTitle: 'Campbell’s WHQ',
  },
  cooper: {
    environment: 'prod',
    networkId: 'dab10744-05c3-4273-bb37-230751b8396a',
    region: 'apse2',
    appTitle: 'Cooper Hospital',
  },
  'csumb-prod': {
    environment: 'prod',
    networkId: '9338bd0b-fef0-44c0-99c4-e6865f83803a',
    region: 'apse2',
    appTitle: 'CSUMB',
    useGoogleMapsAddresses: false,
  },
  'csumb-uat': {
    environment: 'uat',
    networkId: '9338bd0b-fef0-44c0-99c4-e6865f83803a',
    region: 'apse2',
    appTitle: 'CSUMB',
    useGoogleMapsAddresses: false,
  },
  chop: {
    environment: 'prod',
    networkId: '6ee40223-ad7d-4bc2-b3df-447e89af71f7',
    region: 'apse2',
    appTitle: 'Chop',
  },
  'chop-uat': {
    environment: 'uat',
    networkId: '6ee40223-ad7d-4bc2-b3df-447e89af71f7',
    region: 'apse2',
    appTitle: 'Chop',
  },
  dvc4: {
    environment: 'prod',
    networkId: 'a3281926-04a1-4cc8-b4dc-2b899081c669',
    region: 'apse2',
    appTitle: 'Amazon DVC4',
  },
  'dvc4-uat': {
    environment: 'uat',
    networkId: 'a3281926-04a1-4cc8-b4dc-2b899081c669',
    region: 'apse2',
    appTitle: 'Amazon DVC4',
  },
  dvc6: {
    environment: 'prod',
    networkId: 'c109acc6-05d4-4359-9c50-09bca3d972b2',
    region: 'apse2',
    appTitle: 'Amazon DVC6',
  },
  'dvc6-uat': {
    environment: 'uat',
    networkId: 'c109acc6-05d4-4359-9c50-09bca3d972b2',
    region: 'apse2',
    appTitle: 'Amazon DVC6',
  },
  dyt3: {
    environment: 'prod',
    networkId: 'ad2bcd7a-2158-4280-abc1-ff15d37efdda',
    region: 'apse2',
    appTitle: 'Amazon DYT3',
  },
  'dyt3-uat': {
    environment: 'uat',
    networkId: 'ad2bcd7a-2158-4280-abc1-ff15d37efdda',
    region: 'apse2',
    appTitle: 'Amazon DYT3',
  },
  'fulton-labs': {
    environment: 'prod',
    networkId: '94465aa7-7754-408b-8ecd-0c1ff1cc74e1',
    region: 'apse2',
    appTitle: 'Fulton Labs',
    useGoogleMapsAddresses: false,
  },
  'fulton-labs-uat': {
    environment: 'uat',
    networkId: '94465aa7-7754-408b-8ecd-0c1ff1cc74e1',
    region: 'apse2',
    appTitle: 'Fulton Labs',
    useGoogleMapsAddresses: false,
  },
  'hallandale-shuttle': {
    environment: 'prod',
    networkId: '4c353c93-1744-439b-937d-034c407f08e8',
    region: 'apse2',
    appTitle: 'Hallandale Shuttle',
  },
  'hilton-blue-lagoon': {
    environment: 'prod',
    networkId: '7e7f815e-4f1a-475c-bf84-6bf6d2ebb7e0',
    region: 'apse2',
    appTitle: 'Hilton Blue Lagoon Miami Airport',
  },
  'jhh-shuttle-bus-service': {
    environment: 'prod',
    networkId: '4e46d477-3805-4919-b804-c3231dc350d8',
    region: 'apse2',
    appTitle: 'JHH Shuttle Bus Service',
    useGoogleMapsAddresses: false,
  },
  'lax-105': {
    environment: 'prod',
    networkId: '3d80115e-737d-49c3-9c1e-b0f47e279831',
    region: 'apse2',
    appTitle: 'LAX 105 Shuttle',
  },
  'lax-105-uat': {
    environment: 'uat',
    networkId: '3d80115e-737d-49c3-9c1e-b0f47e279831',
    region: 'apse2',
    appTitle: 'LAX 105 Shuttle',
  },
  'lax-shuttle-prod': {
    environment: 'prod',
    networkId: 'f4366897-b601-46c4-89cf-31c6503894b9',
    region: 'apse2',
    appTitle: 'LAX Shuttle',
    useGoogleMapsAddresses: false,
  },
  'lax-shuttle-uat': {
    environment: 'uat',
    networkId: 'f4366897-b601-46c4-89cf-31c6503894b9',
    region: 'apse2',
    appTitle: 'LAX Shuttle',
    useGoogleMapsAddresses: false,
  },
  'lucketts-shuttle': {
    environment: 'prod',
    networkId: 'b4268f24-2a4b-43aa-937c-b810231f95f8',
    region: 'apse2',
    appTitle: 'Lucketts Shuttle',
    useGoogleMapsAddresses: false,
  },
  'lucketts-shuttle-uat': {
    environment: 'uat',
    networkId: 'b4268f24-2a4b-43aa-937c-b810231f95f8',
    region: 'apse2',
    appTitle: 'Lucketts Shuttle',
    useGoogleMapsAddresses: false,
  },
  'mount-sinai': {
    environment: 'prod',
    networkId: 'd6b68637-b182-4e94-973a-64f28cac2d08',
    region: 'apse2',
    appTitle: 'Mount Sinai',
    appTheme: 'mount-sinai',
    useGoogleMapsAddresses: false,
  },
  'mount-sinai-uat': {
    environment: 'uat',
    networkId: 'd6b68637-b182-4e94-973a-64f28cac2d08',
    region: 'apse2',
    appTitle: 'Mount Sinai',
    appTheme: 'mount-sinai',
    useGoogleMapsAddresses: false,
  },
  'nike-suttle': {
    environment: 'prod',
    networkId: 'f27748f0-8acd-4e8b-85ad-2b4563ca495b',
    region: 'apse2',
    appTitle: 'Nike Shuttle',
    appTheme: 'nike',
    useGoogleMapsAddresses: false,
  },
  'nike-shuttle-prod': {
    environment: 'prod',
    networkId: 'f27748f0-8acd-4e8b-85ad-2b4563ca495b',
    region: 'apse2',
    appTitle: 'Nike Shuttle',
    appTheme: 'nike',
    useGoogleMapsAddresses: false,
  },
  'nike-shuttle-uat': {
    environment: 'uat',
    networkId: 'f27748f0-8acd-4e8b-85ad-2b4563ca495b',
    region: 'apse2',
    appTitle: 'Nike Shuttle',
    appTheme: 'nike',
    useGoogleMapsAddresses: false,
  },
  theconnect: {
    // Perrone Robotics
    environment: 'prod',
    networkId: 'd5d09fb0-6dea-44b7-97db-79121c9db788',
    region: 'apse2',
    appTitle: 'The Connect',
    appTheme: 'perrone-robotics',
  },
  'perrone-robotics-uat': {
    environment: 'uat',
    networkId: 'd5d09fb0-6dea-44b7-97db-79121c9db788',
    region: 'apse2',
    appTitle: 'The Connect',
    appTheme: 'perrone-robotics',
  },
  'sutter-health': {
    environment: 'prod',
    networkId: 'c269ba8d-107e-4a39-b492-829aed7ab80e',
    region: 'apse2',
    appTitle: 'Sutter Health',
  },
  'sutter-health-uat': {
    environment: 'uat',
    networkId: 'c269ba8d-107e-4a39-b492-829aed7ab80e',
    region: 'apse2',
    appTitle: 'Sutter Health',
  },
  the55bus: {
    environment: 'prod',
    networkId: '5804c4b9-684a-43ae-af85-4af27cbdb5db',
    region: 'apse2',
    appTitle: 'The 55 Bus',
    appTheme: 'glenfarg',
    useGoogleMapsAddresses: false,
  },
  'tvp-shuttle': {
    environment: 'prod',
    networkId: '625c852d-97c7-41d6-9e14-7d50790abdc2',
    region: 'apse2',
    appTitle: 'Thames Valley Park Shuttle Bus',
    appTheme: 'tvp',
  },
  'tvp-shuttle-uat': {
    environment: 'uat',
    networkId: '625c852d-97c7-41d6-9e14-7d50790abdc2',
    region: 'apse2',
    appTitle: 'Thames Valley Park Shuttle Bus',
    appTheme: 'tvp',
  },
  trio: {
    environment: 'prod',
    networkId: '12c52930-5e36-4034-b9b8-fe1176d859a2',
    region: 'euwe2',
    appTitle: 'TRIO',
    appTheme: 'trio',
  },
  'trio-uat': {
    environment: 'uat',
    networkId: '12c52930-5e36-4034-b9b8-fe1176d859a2',
    region: 'euwe2',
    appTitle: 'TRIO',
    appTheme: 'trio',
  },
  uhnbc: {
    environment: 'prod',
    networkId: '38216586-29c9-48ed-96f5-f0c00a2034dd',
    region: 'apse2',
    appTitle: 'UHNBC',
  },
  'walmart-shuttle': {
    environment: 'prod',
    networkId: '6c5302e5-129a-4b37-8560-cdceab1600ff',
    region: 'apse2',
    appTitle: 'Walmart Shuttle',
    useGoogleMapsAddresses: false,
  },
  'walmart-shuttle-prod': {
    environment: 'prod',
    networkId: '6c5302e5-129a-4b37-8560-cdceab1600ff',
    region: 'apse2',
    appTitle: 'Walmart Shuttle',
    useGoogleMapsAddresses: false,
  },
  'walmart-shuttle-uat': {
    environment: 'uat',
    networkId: '6c5302e5-129a-4b37-8560-cdceab1600ff',
    region: 'apse2',
    appTitle: 'Walmart Shuttle',
    useGoogleMapsAddresses: false,
  },
  waterside: {
    environment: 'prod',
    networkId: '1e6ede65-edde-4a34-87bf-da93935c390a',
    region: 'apse2',
    appTitle: 'Waterside Plaza',
  },
  wcsu: {
    environment: 'prod',
    networkId: '02c7e08b-31a6-4256-a493-bd09045c4ff5',
    region: 'apse2',
    appTitle: 'Western State CT University',
  },
  'wcsu-uat': {
    environment: 'uat',
    networkId: '02c7e08b-31a6-4256-a493-bd09045c4ff5',
    region: 'apse2',
    appTitle: 'Western State CT University',
  },
  woodlands: {
    environment: 'prod',
    networkId: '09c395fb-4d93-4251-bdc4-0905b7029c58',
    region: 'apse2',
    appTitle: 'The Woodlands Township',
  },
  'woodlands-uat': {
    environment: 'uat',
    networkId: '09c395fb-4d93-4251-bdc4-0905b7029c58',
    region: 'apse2',
    appTitle: 'The Woodlands Township',
  },
  wptrack: {
    environment: 'prod',
    networkId: 'de76e7a9-e0b4-4f36-908a-84db0c50c558',
    region: 'euwe2',
    appTitle: 'WPTrack Shuttle',
  },
  ygk1: {
    environment: 'prod',
    networkId: 'ad2bcd7a-2158-4280-abc1-ff15d37efdda',
    region: 'apse2',
    appTitle: 'Amazon YGK1',
  },
  'ygk1-uat': {
    environment: 'uat',
    networkId: 'ad2bcd7a-2158-4280-abc1-ff15d37efdda',
    region: 'apse2',
    appTitle: 'Amazon YGK1',
  },
  yvr4: {
    environment: 'prod',
    networkId: '2609bd83-6be3-48d6-b915-731460d81917',
    region: 'apse2',
    appTitle: 'Amazon YVR4',
  },
  'yvr4-uat': {
    environment: 'uat',
    networkId: '2609bd83-6be3-48d6-b915-731460d81917',
    region: 'apse2',
    appTitle: 'Amazon YVR4',
  },
  yxx2: {
    environment: 'prod',
    networkId: '6ced1014-1334-4b45-949b-2d0792f5eb18',
    region: 'apse2',
    appTitle: 'Amazon YXX2',
  },
  'yxx2-uat': {
    environment: 'uat',
    networkId: '6ced1014-1334-4b45-949b-2d0792f5eb18',
    region: 'apse2',
    appTitle: 'Amazon YXX2',
  },
  '360-green': {
    environment: 'prod',
    networkId: 'dfc4388f-4b28-49ac-b77a-4f7f9de94711',
    region: 'apse2',
    appTitle: '360 Green',
  },
  '360-green-uat': {
    environment: 'uat',
    networkId: 'dfc4388f-4b28-49ac-b77a-4f7f9de94711',
    region: 'apse2',
    appTitle: '360 Green',
  },
  forsyth: {
    environment: 'prod',
    networkId: 'f215c986-9411-4d31-959b-109b1d0378f2',
    region: 'apse2',
    appTitle: 'Northside Forsyth',
  },
  'forsyth-uat': {
    environment: 'uat',
    networkId: 'f215c986-9411-4d31-959b-109b1d0378f2',
    region: 'apse2',
    appTitle: 'Northside Forsyth',
  },
  'netflix-shuttle': {
    environment: 'prod',
    networkId: '0b371941-81a8-417b-8479-29b4973f120d',
    region: 'apse2',
    appTitle: 'Netflix Shuttle',
    useGoogleMapsAddresses: false,
  },
  'netflix-shuttle-uat': {
    environment: 'uat',
    networkId: '0b371941-81a8-417b-8479-29b4973f120d',
    region: 'apse2',
    appTitle: 'Netflix Shuttle',
    useGoogleMapsAddresses: false,
  },
  uca1: {
    environment: 'prod',
    networkId: '211a36e9-ab28-4a67-8fb9-919799ced2f7',
    region: 'apse2',
    appTitle: 'Amazon UCA1',
  },
  'uca1-uat': {
    environment: 'uat',
    networkId: '211a36e9-ab28-4a67-8fb9-919799ced2f7',
    region: 'apse2',
    appTitle: 'Amazon UCA1',
  },
  wbstl: {
    environment: 'prod',
    networkId: 'c4c18346-e5a3-46a5-aaa0-24b1ef37e23b',
    region: 'euwe2',
    appTitle: 'Warner Bros. Studio Tour London',
    appTheme: 'warner-bros',
    useGoogleMapsAddresses: false,
  },
  'wbstl-uat': {
    environment: 'uat',
    networkId: 'c4c18346-e5a3-46a5-aaa0-24b1ef37e23b',
    region: 'euwe2',
    appTitle: 'Warner Bros. Studio Tour London',
    appTheme: 'warner-bros',
    useGoogleMapsAddresses: false,
  },
  rchmb: {
    environment: 'prod',
    networkId: '4434cef6-2c31-48b3-8bbf-1d636ece3d78',
    region: 'apse2',
    appTitle: 'RCHMB Employee Shuttle',
  },
  'rchmb-uat': {
    environment: 'uat',
    networkId: '4434cef6-2c31-48b3-8bbf-1d636ece3d78',
    region: 'apse2',
    appTitle: 'RCHMB Employee Shuttle',
  },
  'beverly-hilton': {
    environment: 'prod',
    networkId: 'bed3b307-f2ac-4ded-8db1-c709f8d4f0e3',
    region: 'apse2',
    appTitle: 'The Beverly Hilton Shuttle',
  },
  'beverly-hilton-uat': {
    environment: 'uat',
    networkId: 'bed3b307-f2ac-4ded-8db1-c709f8d4f0e3',
    region: 'apse2',
    appTitle: 'The Beverly Hilton Shuttle',
  },
  'netflix-shuttle-lg': {
    environment: 'prod',
    networkId: 'e1f15a1c-9771-4df3-9479-a1a2e6e06dd9',
    region: 'apse2',
    appTitle: 'Netflix Shuttle LG',
    useGoogleMapsAddresses: false,
  },
  'netflix-shuttle-lg-uat': {
    environment: 'uat',
    networkId: 'e1f15a1c-9771-4df3-9479-a1a2e6e06dd9',
    region: 'apse2',
    appTitle: 'Netflix Shuttle LG',
    useGoogleMapsAddresses: false,
  },
  'steamboat-shuttle': {
    environment: 'prod',
    networkId: '771c05fd-6e4b-4877-9960-9923995749dd',
    region: 'apse2',
    appTitle: 'Steamboat Ski & Resort Shuttle',
    useGoogleMapsAddresses: false,
  },
  'steamboat-shuttle-uat': {
    environment: 'uat',
    networkId: '771c05fd-6e4b-4877-9960-9923995749dd',
    region: 'apse2',
    appTitle: 'Steamboat Ski & Resort Shuttle',
    useGoogleMapsAddresses: false,
  },
  'mit-shuttle': {
    environment: 'prod',
    networkId: '59aded94-1bff-4c9f-9fb8-7ef6dbb11a7a',
    region: 'apse2',
    appTitle: 'MIT Shuttle',
    useGoogleMapsAddresses: false,
  },
  'mit-shuttle-uat': {
    environment: 'uat',
    networkId: '59aded94-1bff-4c9f-9fb8-7ef6dbb11a7a',
    region: 'apse2',
    appTitle: 'MIT Shuttle',
    useGoogleMapsAddresses: false,
  },
  'mercy-general': {
    environment: 'prod',
    networkId: 'c5c99125-5a2a-4417-896e-79d374c60cc6',
    region: 'apse2',
    appTitle: 'Mercy General Shuttle',
    useGoogleMapsAddresses: false,
  },
  'mercy-general-uat': {
    environment: 'uat',
    networkId: 'c5c99125-5a2a-4417-896e-79d374c60cc6',
    region: 'apse2',
    appTitle: 'Mercy General Shuttle',
    useGoogleMapsAddresses: false,
  },
  tesla: {
    environment: 'prod',
    networkId: '59864a0f-8968-4d49-831a-87629c2896ce',
    region: 'apse2',
    appTitle: 'Tesla Shuttle',
    useGoogleMapsAddresses: false,
  },
  'tesla-uat': {
    environment: 'uat',
    networkId: '59864a0f-8968-4d49-831a-87629c2896ce',
    region: 'apse2',
    appTitle: 'Tesla Shuttle',
    useGoogleMapsAddresses: false,
  },
  prudential: {
    environment: 'prod',
    networkId: '50dcaf69-0217-41d8-9c9d-e904262853d3',
    region: 'apse2',
    appTitle: 'Prudential Shuttle',
    useGoogleMapsAddresses: false,
  },
  'prudential-uat': {
    environment: 'uat',
    networkId: '50dcaf69-0217-41d8-9c9d-e904262853d3',
    region: 'apse2',
    appTitle: 'Prudential Shuttle',
    useGoogleMapsAddresses: false,
  },
  'johannesburg-shuttle-uat': {
    environment: 'uat',
    networkId: '3c2f0dca-4da1-4830-95b2-73a9066c7515',
    region: 'apse2',
    appTitle: 'Johannesburg  Shuttle',
    useGoogleMapsAddresses: false,
  },
  'smartride-uat': {
    environment: 'uat',
    networkId: '09540d7a-e16b-4143-a760-5581ab988c5d',
    region: 'euwe2',
    appTitle: 'SmartRide Fixed Route',
    useGoogleMapsAddresses: false,
  },
  dvv2: {
    environment: 'prod',
    networkId: '83b0a57a-c9ef-4fcc-9601-6413a9de9ad0',
    region: 'apse2',
    appTitle: 'DVV2 Shuttle',
    useGoogleMapsAddresses: false,
  },
  'dvv2-uat': {
    environment: 'uat',
    networkId: '83b0a57a-c9ef-4fcc-9601-6413a9de9ad0',
    region: 'apse2',
    appTitle: 'DVV2 Shuttle',
    useGoogleMapsAddresses: false,
  },
  whitmanlabs: {
    environment: 'prod',
    networkId: 'a580afbb-661c-4d99-bbd4-d34508b5ceca',
    region: 'euwe2',
    appTitle: 'Whitman Labs',
    useGoogleMapsAddresses: false,
  },
  'whitmanlabs-uat': {
    environment: 'uat',
    networkId: 'a580afbb-661c-4d99-bbd4-d34508b5ceca',
    region: 'euwe2',
    appTitle: 'Whitman Labs',
    useGoogleMapsAddresses: false,
  },
  odot: {
    environment: 'prod',
    networkId: '5540e3fb-edef-4a3f-ada9-faae54809161',
    region: 'apse2',
    appTitle: 'ODOT Shuttle',
    useGoogleMapsAddresses: false,
  },
  'odot-uat': {
    environment: 'uat',
    networkId: '5540e3fb-edef-4a3f-ada9-faae54809161',
    region: 'apse2',
    appTitle: 'ODOT Shuttle',
    useGoogleMapsAddresses: false,
  },
  'summit-county': {
    environment: 'prod',
    networkId: '5a88d816-a3df-4475-910b-35d841a52eea',
    region: 'apse2',
    appTitle: 'Summit County',
    useGoogleMapsAddresses: false,
  },
  'summit-county-uat': {
    environment: 'uat',
    networkId: '5a88d816-a3df-4475-910b-35d841a52eea',
    region: 'apse2',
    appTitle: 'Summit County',
    useGoogleMapsAddresses: false,
  },
  sdap: {
    environment: 'prod',
    networkId: '7d34b287-4a0c-4cee-8f11-d56bca906c1a',
    region: 'apse2',
    appTitle: 'San Diego Airport Parking',
    useGoogleMapsAddresses: false,
  },
  'sdap-uat': {
    environment: 'uat',
    networkId: '7d34b287-4a0c-4cee-8f11-d56bca906c1a',
    region: 'apse2',
    appTitle: 'San Diego Airport Parking',
    useGoogleMapsAddresses: false,
  },
};

// exported separately for ease of testing
export const buildAppConfig = (networkConfig: Partial<AppConfig>) => {
  const apiUrl = getApiUrl(networkConfig);
  const pusherKey = getPusherKey(networkConfig);

  return {
    ...defaultAppConfig,
    ...networkConfig,
    apiUrl,
    pusherKey,
  };
};

export const getNetworkConfig = (subdomain: string) => {
  console.log('subdomain', subdomain);
  const netConfig = networkConfig['localhost'];
  return buildAppConfig(netConfig);
};
