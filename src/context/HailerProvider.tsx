import React, { createContext, Context, useContext } from 'react';
import { HailerOpsClient, ALLOWED_ENV } from '@liftango/ops-client';
import { useAppSelector } from '../hooks/storeHooks';
import { selectApplicationMode } from '../store/reducers/applicationReducer';
import { Persistence } from '../storage/persistence';
import { LocalStorage } from '../storage/local-storage';
import { ConfigContext } from './ConfigProvider';

const defaultContext: any = null;
export const HailerContext: Context<HailerOpsClient> = createContext<HailerOpsClient>(defaultContext);

type HailerContextProviderProps = {
  children: any;
};

const HailerContextProvider = ({ children }: HailerContextProviderProps) => {
  const { apiUrl, environment } = useContext(ConfigContext);
  const application = useAppSelector(selectApplicationMode);

  const persistence: Persistence = new LocalStorage();
  const hailerService = new HailerOpsClient(
    apiUrl,
    environment,
    {
      type: 'http',
    },
    persistence,
    application,
  );

  return <HailerContext.Provider value={hailerService}>{children}</HailerContext.Provider>;
};

export default HailerContextProvider;
