import { Action, configureStore, ThunkAction } from '@reduxjs/toolkit';
import applicationReducer from './reducers/applicationReducer';
import networkReducer from './reducers/networkReducer';
import serviceReducer from './reducers/serviceReducer';
import themeReducer from './reducers/themeReducer';
import routesReducer from './reducers/routesReducer';
import alertsReducer from './reducers/alertsReducer';
import { listenerMiddleware } from './listenerMiddleware';
import { combineReducers } from 'redux';

const rootReducer = combineReducers({
  application: applicationReducer,
  network: networkReducer,
  service: serviceReducer,
  routes: routesReducer,
  theme: themeReducer,
  alerts: alertsReducer,
});

// @ts-expect-error due to type-only circular dependency which is fine here according to the author of redux https://stackoverflow.com/a/63924400
const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) => getDefaultMiddleware().prepend(listenerMiddleware.middleware),
});

export type RootState = ReturnType<typeof rootReducer>;

// @ts-expect-error due to type-only circular dependency which is fine here according to the author of redux https://stackoverflow.com/a/63924400
export type AppDispatch = typeof store.dispatch;

export type AppThunk<ReturnType = void> = ThunkAction<ReturnType, RootState, unknown, Action<string>>;

export default store;
