import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { FixedRoute } from '@liftango/ops-client';
import type { RootState } from '../global';

export type RoutesState = {
  hasListInitialised: boolean;
  list: FixedRoute.ServiceRoutesAndStopsPayload[];
  hidden: string[];
};

const initialState: RoutesState = {
  hasListInitialised: false,
  list: [],
  hidden: [],
};

// Checking of versionID in setHiddenRoutes, selectFilteredRoutes, unhideRoute, and RouteFiltering->isActive can be removed once sufficient
// time has passed since ENG-6657 is released to production that all lifty lite sessions can be assumed to have switched to storing route rather than version IDs.
// displayedRoutes bookmarkable feature was (functionally) released at the same time as the switch so old bookmarks won't be storing version IDs.
export const routesSlice = createSlice({
  name: 'routes',
  initialState,
  reducers: {
    setRoutes: (state: RoutesState, action: PayloadAction<FixedRoute.ServiceRoutesAndStopsPayload[]>) => {
      state.list = action.payload.sort((a, b) => a.label.localeCompare(b.label));
      state.hasListInitialised = true;
    },
    setHiddenRoutes: (state: RoutesState, action: PayloadAction<string[]>) => {
      // Set hidden list unless it would hide all, in which case set none hidden - in case a final hidden route was removed since the user last visited
      // payload from local storage could be version or route ids depending on if browser saved a filter before api return id switched from version to route.
      //   Checking both ID fields when determining if all would be hidden.
      state.hidden = state.list.some((route) => !action.payload.includes(route.id) && !action.payload.includes(route.versionId))
        ? action.payload
        : [];
    },
    // still sets state.hidden, but with inverted logic - hides all not in 'shown' payload
    setShownRoutes: (state: RoutesState, action: PayloadAction<string[]>) => {
      state.hidden = state.list.filter((route) => !action.payload.includes(route.id)).map((route) => route.id);
    },
    hideRoute: (state: RoutesState, action: PayloadAction<string>) => {
      state.hidden = [...state.hidden, action.payload];
    },
    unhideRoute: (state: RoutesState, action: PayloadAction<FixedRoute.ServiceRoutesAndStopsPayload>) => {
      // Filter list might be route id or version id depending on if browser saved a filter before api return id switched from version to route. Check and remove if it matches either.
      state.hidden = [...state.hidden.filter((routeId: string) => ![action.payload.versionId, action.payload.id].includes(routeId))];
    },
  },
});

export const { setRoutes, setHiddenRoutes, setShownRoutes, hideRoute, unhideRoute } = routesSlice.actions;

export const selectRoutes = (state: RootState): FixedRoute.ServiceRoutesAndStopsPayload[] => state.routes.list;

export const selectFilteredRoutes = (state: RootState): FixedRoute.ServiceRoutesAndStopsPayload[] =>
  state.routes.list.filter(
    (route: FixedRoute.ServiceRoutesAndStopsPayload) =>
      !state.routes.hidden.includes(route.id) && !state.routes.hidden.includes(route.versionId),
  );

export const selectRouteShapes = (state: RootState): FixedRoute.ServiceRouteShape[] =>
  selectFilteredRoutes(state).map((route: FixedRoute.ServiceRoutesAndStopsPayload) => route.routeShape);

export const selectStops = (state: RootState): FixedRoute.ServiceRouteStop[] =>
  selectFilteredRoutes(state)
    .map((route: FixedRoute.ServiceRoutesAndStopsPayload) => route.stops)
    .reduce((previous: FixedRoute.ServiceRouteStop[], current: FixedRoute.ServiceRouteStop[]) => [...previous, ...current], []);

export const selectRouteById =
  (routeId: string) =>
  (state: RootState): FixedRoute.ServiceRoutesAndStopsPayload | null =>
    state.routes.list.find((route: FixedRoute.ServiceRoutesAndStopsPayload) => route.id === routeId || route.versionId === routeId) || null;

export const selectRoutesInitialisationState = (state: RootState): boolean => state.routes.hasListInitialised;

export const selectHiddenRoutes = (state: RootState): string[] => state.routes.hidden;

export default routesSlice.reducer;
