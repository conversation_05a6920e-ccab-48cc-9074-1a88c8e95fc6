import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../global';
import { THEME as DEFAULT_THEME } from '../../theme/themes/default/theme';
import { THEME as NIKE_THEME } from '../../theme/themes/nike/theme';
import { THEME as WALMART_THEME } from '../../theme/themes/walmart/theme';
import { THEME as TVP_THEME } from '../../theme/themes/tvp/theme';
import { THEME as MOUNT_SINAI_THEME } from '../../theme/themes/mount-sinai/theme';
import { THEME as TRIO_THEME } from '../../theme/themes/trio/theme';
import { THEME as GLENFARG_THEME } from '../../theme/themes/glenfarg/theme';
import { THEME as PERRONE_ROBOTICS_THEME } from '../../theme/themes/perrone-robotics/theme';
import { THEME as WARNER_BROS_THEME } from '../../theme/themes/warner-bros/theme';

export type ThemeScheme = 'light' | 'dark';

export type ThemeName = 'liftango' | 'nike' | 'walmart' | 'tvp' | 'mount-sinai' | 'trio' | 'warner-bros' | 'glenfarg' | 'perrone-robotics';

export type Theme = {
  name: ThemeName;
  map: any;
  colors: ThemeColors;
  logo: string;
  favicon: string;
  /**
   * the filename of the css file to be required.
   */
  cssFilePath: string;
  /**
   * If the name of the folder differs from the theme name, specify it here so that the correct css file is required.
   */
  folderName?: string;
  loadingMask?: string;
  icons?: ThemeIcons;
  distinguishInactiveRoutes?: boolean;
  longWalkTranslationKey?: string;
};

export type ThemeColors = {
  primary: string;
  primaryRgb: string;
  secondary: string;
  secondaryRgb: string;
  accent: string;
};

export type ThemeState = {
  scheme: ThemeScheme;
  themeList: Partial<Theme>[];
  theme: Partial<Theme>;
};

export type ThemeIcons = {
  fixedRouteVehicleRounded?: string;
  fixedRouteVehicle?: string;
};

export type AlertStyles = {
  informationBackgroundColour: string;
  warningBackgroundColour: string;
  errorBackgroundColour: string;
  textColourLight: string;
  textColourDark: string;
};

const initialState: ThemeState = {
  scheme: 'light',
  themeList: [
    DEFAULT_THEME,
    NIKE_THEME,
    WALMART_THEME,
    TVP_THEME,
    MOUNT_SINAI_THEME,
    TRIO_THEME,
    GLENFARG_THEME,
    PERRONE_ROBOTICS_THEME,
    WARNER_BROS_THEME,
  ],
  theme: DEFAULT_THEME,
};

export const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setScheme: (state: ThemeState, action: PayloadAction<ThemeScheme>) => {
      state.scheme = action.payload;
    },
    setTheme: (state: ThemeState, action: PayloadAction<ThemeName>) => {
      state.theme = state.themeList.find((theme: Partial<Theme>) => theme.name === action.payload) ?? DEFAULT_THEME;
    },
  },
});

export const { setScheme, setTheme } = themeSlice.actions;

const getNetworkTheme = (state: RootState): Partial<Theme> => state.theme.theme;

export const selectTheme = createSelector(getNetworkTheme, (networkTheme: Partial<Theme>): Theme => {
  // base of default overridden by network theme
  return {
    ...DEFAULT_THEME,
    ...networkTheme,
  };
});
export const selectThemeScheme = (state: RootState) => state.theme.scheme;

export default themeSlice.reducer;
