// Create the middleware instance and methods
import { AnyAction, createListenerMiddleware, ListenerEffectAPI } from '@reduxjs/toolkit';
import type { AppDispatch, RootState } from './global';
import history from '../router/history';
import { ROUTE_URL_PARAM } from '../router/Router';

// @ts-expect-error due to type-only circular dependency which is fine here according to the author of redux https://stackoverflow.com/a/63924400
export const listenerMiddleware = createListenerMiddleware<RootState, AppDispatch>();

listenerMiddleware.startListening({
  predicate: (_: AnyAction, currentState: RootState, previousState: RootState) => {
    // Trigger if hidden route filter has changed
    return currentState?.routes?.hidden !== previousState?.routes?.hidden;
  },
  effect: (_: any, listenerApi: ListenerEffectAPI<RootState, AppDispatch>) => {
    const routes = listenerApi.getState().routes.list;
    const hidden = listenerApi.getState().routes.hidden;

    const displayList = routes.reduce<string[]>((acc, current) => {
      if (!hidden.includes(current.id)) {
        acc.push(current.id);
      }
      return acc;
    }, []);

    const searchParams = new URLSearchParams(history.location.search);
    // if all or no routes are displayed, keep URL cleaner and remove param
    if (displayList.length > 0 && displayList.length < routes.length) {
      searchParams.set(ROUTE_URL_PARAM, displayList.join(','));
    } else if (searchParams.has(ROUTE_URL_PARAM)) {
      searchParams.delete(ROUTE_URL_PARAM);
    }

    history.replace({
      pathname: history.location.pathname,
      search: `?${searchParams.toString()}`,
    });
  },
});
