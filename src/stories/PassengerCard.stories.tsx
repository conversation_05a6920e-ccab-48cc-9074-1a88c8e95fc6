import React from 'react';
import { Story, Meta } from '@storybook/react';
import PassengerCard, { PassengerCardProp } from '../components/features/PassengerSelector/PassengerCard';
import '../theme/themes/default/default.css';
import { personOutline, removeCircleOutline } from 'ionicons/icons';

export default {
  title: 'PassengerSelector/PassengerCard',
  component: PassengerCard,
} as Meta;

const Template: Story<PassengerCardProp> = (args) => <PassengerCard {...args} />;

export const Driver = Template.bind({});
Driver.args = {
  icon: personOutline,
  title: 'Me',
  iconColor: 'dark',
};

export const Rider = Template.bind({});
Rider.args = {
  icon: removeCircleOutline,
  title: 'Rider',
  iconColor: 'danger',
};
