import React from 'react';
import { Story, Meta } from '@storybook/react';
import PassengerSelector, { PassengerSelectorProps } from '../components/features/PassengerSelector/PassengerSelector';
import '../theme/themes/default/default.css';

export default {
  title: 'PassengerSelector/PassengerSelector',
  component: PassengerSelector,
  parameters: {
    backgrounds: {
      default: 'twitter',
      values: [{ name: 'twitter', value: '#00aced' }],
    },
  },
} as Meta;

const Template: Story<PassengerSelectorProps> = (args) => <PassengerSelector {...args} />;

export const WithResults = Template.bind({});
WithResults.args = {};
