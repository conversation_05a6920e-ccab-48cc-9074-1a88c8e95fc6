import React from 'react';
import { Story, Meta } from '@storybook/react';
import Avatar, { AvatarProps } from '../components/elements/Avatar/Avatar';

export default {
  title: 'Example/Avatar',
  component: Avatar,
  argTypes: {},
} as Meta;

const Template: Story<AvatarProps> = (args) => <Avatar {...args} />;

export const Basic = Template.bind({});
Basic.args = {
  src: 'https://gravatar.com/avatar/dba6bae8c566f9d4041fb9cd9ada7741?d=identicon&f=y',
};
