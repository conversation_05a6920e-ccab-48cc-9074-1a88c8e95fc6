import React from 'react';
import { Story, Meta } from '@storybook/react';
import CountSelector, { CountSelectorProps } from '../components/elements/CountSelector/CountSelector';
import '../theme/themes/default/default.css';

export default {
  title: 'PassengerSelector/CountSelector',
  component: CountSelector,
} as Meta;

const Template: Story<CountSelectorProps> = (args) => <CountSelector {...args} />;

export const WithCount = Template.bind({});
WithCount.args = {
  count: 0,
  onCountChange: () => {},
};

export const WithMaxCount = Template.bind({});
WithMaxCount.args = {
  count: 10,
  maxCount: 10,
  onCountChange: () => {},
};
