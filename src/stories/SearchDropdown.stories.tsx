import React from 'react';
import { Story, Meta } from '@storybook/react';
import SearchWithDropdown, { SearchWithDropdownProps } from '../components/components/SearchWithDropdown/SearchWithDropdown';
import '../theme/themes/default/default.css';

export default {
  title: 'SearchWithDropdown',
  component: SearchWithDropdown,
  parameters: {
    backgrounds: {
      default: 'twitter',
      values: [{ name: 'twitter', value: '#00aced' }],
    },
  },
} as Meta;

const Template: Story<SearchWithDropdownProps> = (args) => <SearchWithDropdown {...args} />;

export const WithResults = Template.bind({});
WithResults.args = {
  searchResults: [
    { id: '1', title: 'Timaru', label: '8 Tony McGrane PI, Dubbo NSW 2830' },
    { id: '2', title: 'Dubbo Visitors Information Centree', label: '24 Nulla Rd, Dubbo NSW 2830' },
  ],
  showCancelButton: 'always',
};
