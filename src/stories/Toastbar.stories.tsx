import React from 'react';
import { Story, Meta } from '@storybook/react';
import Toast, { ToastProps } from '../components/elements/Toastbar/Toastbar';
import '../theme/themes/default/default.css';
import { add, remove } from 'ionicons/icons';

export default {
  title: 'Toast',
  component: Toast,
  parameters: {
    backgrounds: {
      default: 'twitter',
      values: [{ name: 'twitter', value: '#00aced' }],
    },
  },
} as Meta;

const Template: Story<ToastProps> = (args) => <Toast {...args} />;

export const Add = Template.bind({});

Add.args = {
  isOpen: true,
  title: 'Welcome to dubbo AV Service',
  message: `We're a free autonomous vehicle services Version.`,
  duration: 200,
  image: 'https://upload.wikimedia.org/wikipedia/commons/e/e5/2005-2008_BMW_325i_%28E90%29_sedan_03.jpg',
  ctaIcon: add,
};

export const Remove = Template.bind({});
Remove.args = {
  isOpen: true,
  title: 'Welcome to dubbo AV Service',
  message: `We're a free autonomous vehicle services Version.`,
  duration: 200,
  image: 'https://upload.wikimedia.org/wikipedia/commons/e/e5/2005-2008_BMW_325i_%28E90%29_sedan_03.jpg',
  ctaIcon: remove,
};

export const WithLongTitle = Template.bind({});
WithLongTitle.args = {
  isOpen: true,
  title: 'Welcome to dubbo AV Service where we welcome every single one of our users!',
  message: `We're a free autonomous vehicle servicesVersion.`,
  duration: 200,
  image: 'https://upload.wikimedia.org/wikipedia/commons/e/e5/2005-2008_BMW_325i_%28E90%29_sedan_03.jpg',
  ctaIcon: remove,
};
