import React from 'react';
import { Story, Meta } from '@storybook/react';
import Button, { ButtonProps } from '../components/elements/Button/Button';

export default {
  title: 'Example/Button',
  component: Button,
} as Meta;

const Template: Story<ButtonProps> = (args) => <Button {...args} />;

export const Primary = Template.bind({});
Primary.args = {
  children: 'Button1',
};

export const Secondary = Template.bind({});
Secondary.args = {
  children: 'Button2',
  icon: 'Add-outline',
};
