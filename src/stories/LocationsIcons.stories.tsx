import { Story, Meta } from '@storybook/react';
import LocationsIcons, { LocationLeg, LocationsIconsProps } from '../components/components/LocationsIcons/LocationsIcons';
import { locationSharp, navigateCircle, walkSharp } from 'ionicons/icons';

export default {
  title: 'Example/LocationsIcons',
  component: LocationsIcons,
} as Meta;

const Template: Story<LocationsIconsProps> = (args) => <LocationsIcons {...args} />;

const locations: LocationLeg[] = [
  {
    id: '1',
    icon: navigateCircle,
    content: (
      <div>
        <p style={{ margin: 0, fontWeight: 'bold' }}>Departure</p>
      </div>
    ),
  },
  {
    id: '2',
    icon: locationSharp,
    content: (
      <div>
        <p style={{ margin: 0, fontWeight: 'bold' }}>Arrival point</p>
        <div>First line</div>
        <div>Second line</div>
      </div>
    ),
  },
  {
    id: '3',
    icon: walkSharp,
    content: (
      <div>
        <p style={{ margin: 0, fontWeight: 'bold' }}>Walk</p>
      </div>
    ),
  },
];
const colors = ['#7DC8C6', '#EE7334'];

export const LocationsIconsComponent = Template.bind({});
LocationsIconsComponent.args = {
  size: 'sm',
  locations,
  colors,
};
