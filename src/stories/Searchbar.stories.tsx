import React from 'react';
import { Story, Meta } from '@storybook/react';
import Searchbar, { SearchbarProps } from '../components/elements/SearchBar/SearchBar';
import '../theme/themes/default/default.css';

export default {
  title: 'Searchbar',
  component: Searchbar,
} as Meta;

const Template: Story<SearchbarProps> = (args) => <Searchbar {...args} />;

export const EmptySearchbar = Template.bind({});
EmptySearchbar.args = {
  value: '',
};

export const SearchbarWithVal = Template.bind({});
SearchbarWithVal.args = {
  value: 'Some search string',
};
