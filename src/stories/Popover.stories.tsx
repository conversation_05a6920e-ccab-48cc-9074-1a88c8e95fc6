import { Story, Meta } from '@storybook/react';
import Popover, { PopoverProps } from '../components/elements/Popover/Popover';

export default {
  title: 'Example/Popover',
  component: Popover,
} as Meta;

const Template: Story<PopoverProps> = (args) => <Popover {...args} />;

export const SmallPopover = Template.bind({});
SmallPopover.args = {
  isOpen: true,
  children: (
    <div style={{ padding: 15, backgroundColor: 'green' }}>
      <p>Hi, I'm a popover</p>
    </div>
  ),
};
