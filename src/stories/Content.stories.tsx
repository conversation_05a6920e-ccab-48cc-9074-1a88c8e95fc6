import { Story, Meta } from '@storybook/react';
import Content, { ContentProps } from '../components/elements/Content/Content';

export default {
  title: 'Example/Content',
  component: Content,
} as Meta;

const Template: Story<ContentProps> = (args) => <Content {...args} />;

export const SingleLineContent = Template.bind({});
SingleLineContent.args = {
  children: <p style={{ padding: 15 }}>This is the entire content</p>,
};

export const MultilineContent = Template.bind({});
MultilineContent.args = {
  children: (
    <div style={{ padding: 15, backgroundColor: 'red' }}>
      <div>Line 1</div>
      <div>Line 2</div>
      <div>Line 3</div>
      <div>Line 4</div>
      <div>Line 5</div>
    </div>
  ),
};
