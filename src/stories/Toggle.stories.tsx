import React from 'react';
import { Story, Meta } from '@storybook/react';
import Toggle, { ToggleProps } from '../components/elements/Toggle/Toggle';
import '../theme/themes/default/default.css';

export default {
  title: 'Toggle',
  component: Toggle,
} as Meta;

const Template: Story<ToggleProps> = (args) => <Toggle {...args} />;

export const Toggled = Template.bind({});
Toggled.args = {
  isToggled: true,
  handleToggle: () => {},
};

export const Untoggled = Template.bind({});
Untoggled.args = {
  isToggled: false,
  handleToggle: () => {},
};

export const DisabledToggle = Template.bind({});
DisabledToggle.args = {
  isToggled: false,
  handleToggle: () => {},
  disabled: true,
};

export const IOSToggle = Template.bind({});
IOSToggle.args = {
  isToggled: false,
  handleToggle: () => {},
  mode: 'ios',
};
