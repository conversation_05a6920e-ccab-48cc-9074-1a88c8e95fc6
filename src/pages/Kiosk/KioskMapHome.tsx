import React, { useMemo, useState } from 'react';
import { IonContent, IonPage } from '@ionic/react';
import { addOutline, removeOutline } from 'ionicons/icons';
import styled from 'styled-components';
import MapView from '../../containers/MapView/MapView';
import Toast, { ToastBanner } from '../../components/elements/Toastbar/Toastbar';
import { selectNetworkEnforced } from '../../store/reducers/networkReducer';
import { useAppSelector } from '../../hooks/storeHooks';
import { useTranslation } from 'react-i18next';
import { selectServices } from '../../store/reducers/serviceReducer';
import { OnDemandServiceHub, OnDemandServiceSettingsPayload } from '@liftango/liftango-client';
import { getFlattenedServiceSettings, ServiceSettingPayload } from '@liftango/service-toolkit';
import { SMALL_MAP_PADDING } from '../../constants/map';
import { selectApplicationMode } from '../../store/reducers/applicationReducer';

const mapContainerStyle: React.CSSProperties = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
};

const KioskMapHome: React.FC = () => {
  const { t } = useTranslation<['home']>(['home']);
  const network = useAppSelector(selectNetworkEnforced);
  const application = useAppSelector(selectApplicationMode);
  const banner: ToastBanner = {
    title: t('home:welcomeTitle', { name: network.name }),
    message: t(application === 'kiosk' ? 'home:welcomeMessageKiosk' : 'home:welcomeMessage'),
    messageCollapsed: application === 'kiosk' ? t('home:welcomeMessageKioskCollapsed') : undefined,
    image: require('../../assets/project-ute-2-higher.jpg').default,
  };

  const [bannerIsOpen, setBannerIsOpen] = useState<boolean>(true);

  const serviceSettings = useAppSelector(selectServices);

  const toggleBanner = () => setBannerIsOpen((prevState: boolean): boolean => !prevState);

  const serviceSetting: ServiceSettingPayload | null = useMemo<ServiceSettingPayload | null>(() => {
    return getFlattenedServiceSettings(serviceSettings);
  }, [serviceSettings]);

  const serviceHubs: OnDemandServiceHub[] = (serviceSetting as OnDemandServiceSettingsPayload)?.hubs || [];

  return (
    <IonPage>
      <IonContent fullscreen>
        {banner ? (
          <StyledToast
            isOpen={bannerIsOpen}
            title={banner.title}
            message={banner.message}
            messageCollapsed={banner.messageCollapsed}
            image={banner.image}
            ctaIcon={bannerIsOpen ? removeOutline : addOutline}
            ctaCallback={toggleBanner}
          />
        ) : null}

        <MapView
          showUserPosition
          center={network.address}
          initialCenter={network.address}
          regionCode={network.formatting.mobileCountryCode}
          serviceHubs={serviceHubs}
          showTripPlanner={() => {}}
          hideTripPlanner={() => {}}
          stops={[]}
          routeShapes={[]}
          style={mapContainerStyle}
          mapPadding={SMALL_MAP_PADDING}
          networkId={network.id}
        />
      </IonContent>
    </IonPage>
  );
};

const StyledToast = styled(Toast)<{ isOpen: boolean }>`
  top: 70px;
  right: 30px;
  max-width: ${(props) => (props.isOpen ? 600 : 350)}px;
  height: 120px;
  transition: max-width 0.2s ease;

  @media all and (max-width: 550px) {
    max-width: ${(props) => (props.isOpen ? 500 : 350)}px;
    height: 150px;
  }

  @media all and (max-width: 500px) {
    left: 20px;
    right: 20px;
    top: 60px;
    max-width: 350px;
    height: ${(props) => (props.isOpen ? 320 : 140)}px;
    transition: height 0.2s ease;
  }

  @media all and (max-width: 412px) {
    left: 10px;
    right: 10px;
    max-width: 340px;
    height: ${(props) => (props.isOpen ? 310 : 150)}px;
  }

  @media all and (max-width: 350px) {
    top: 60px;
    left: 5px;
    right: 5px;
    max-width: 340px;
    height: ${(props) => (props.isOpen ? 410 : 150)}px;
  }

  @media all and (max-width: 280px) {
    left: 5px;
    right: 5px;
    max-width: 340px;
    height: ${(props) => (props.isOpen ? 550 : 220)}px;
  }
`;

export default KioskMapHome;
