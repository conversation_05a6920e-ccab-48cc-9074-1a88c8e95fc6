import React from 'react';
import Home from '../../containers/Home/Home';
import { useAppSelector } from '../../hooks/storeHooks';
import { selectNetworkEnforced } from '../../store/reducers/networkReducer';
import { useTranslation } from 'react-i18next';
import { selectApplicationMode } from '../../store/reducers/applicationReducer';

const KioskHome: React.FC = () => {
  const { t } = useTranslation<['home']>(['home']);
  const network = useAppSelector(selectNetworkEnforced);
  const application = useAppSelector(selectApplicationMode);

  return (
    <Home
      network={network}
      banner={{
        title: t('home:welcomeTitle', { name: network.name }),
        message: t(application === 'kiosk' ? 'home:welcomeMessageKiosk' : 'home:welcomeMessage'),
        messageCollapsed: application === 'kiosk' ? t('home:welcomeMessageKioskCollapsed') : undefined,
        image: require('../../assets/project-ute-2-higher.jpg').default,
      }}
    />
  );
};

export default KioskHome;
