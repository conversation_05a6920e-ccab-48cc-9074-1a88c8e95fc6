import {
  OnDemandB<PERSON><PERSON><PERSON><PERSON><PERSON>,
  OnDemandBookingJourneyLeg,
  OnDemandJourneyLegAddress,
  OnDemandTripJourney,
  OnDemandTripJourneyLeg,
  OnDemandRideTimeType,
} from '@liftango/liftango-client';
import { format, isBefore } from 'date-fns';
import { TFunction } from 'i18next';
import convert from 'convert-units';
import i18n from 'i18next';
import { DATE_SHORT_TIME_FORMAT, DATE_TIME_FORMAT, TIME_FORMAT, M_TO_KM_THRESHOLD, M_TO_MILES_THRESHOLD } from '../constants/formatting';

export enum JourneyLegTravelType {
  Foot = 'foot',
  Bus = 'bus',
  Car = 'car',
}

export type JourneyAddress = {
  addressId: {
    value: number;
  };
  label: string;
  placeId: string;
  lat: number;
  lng: number;
};

export type WalkingJourney = {
  walkingDistance: number;
  walkingDuration: number;
  walkingAddress: JourneyAddress;
};

export type JourneyDataBookingPoint = {
  pickUpAddress: OnDemandJourneyLegAddress | null;
  dropOffAddress: OnDemandJourneyLegAddress | null;
  toPickUpWalk: WalkingJourney | null;
  fromDropOffWalk: WalkingJourney | null;
  pickUpTimeMessage: string;
  dropOffTimeMessage: string;
};

/**
 * The lock time will include the a buffer for the driver to adjust their course
 */
export const isJourneyLockedIn = (lockTime: string | null): boolean => {
  return !!lockTime && isBefore(new Date(lockTime), new Date());
};

export const isLegTripJourneyLeg = (leg: OnDemandBookingJourneyLeg | OnDemandTripJourneyLeg): leg is OnDemandTripJourneyLeg => {
  return 'travelTime' in leg && leg.travelTime !== undefined;
};

type WalkAndBusLeg = {
  walkLegs: OnDemandBookingJourneyLeg[] | OnDemandTripJourneyLeg[];
  busLeg: OnDemandBookingJourneyLeg | OnDemandTripJourneyLeg | undefined;
};

export const findWalkAndBusLeg = (journeyLegs: OnDemandBookingJourneyLeg[] | OnDemandTripJourneyLeg[]): WalkAndBusLeg => {
  const walkLegs: OnDemandBookingJourneyLeg[] | OnDemandTripJourneyLeg[] = journeyLegs.filter(
    (leg: OnDemandBookingJourneyLeg | OnDemandTripJourneyLeg) => leg.travelType === JourneyLegTravelType.Foot,
  );
  const busLeg: OnDemandBookingJourneyLeg | OnDemandTripJourneyLeg | undefined = journeyLegs.find(
    (leg: OnDemandBookingJourneyLeg | OnDemandTripJourneyLeg) =>
      leg.travelType === JourneyLegTravelType.Bus || leg.travelType === JourneyLegTravelType.Car,
  );

  return { walkLegs, busLeg };
};

export type TimeWindows = {
  pickUpStart: string;
  pickUpEnd: string;
  dropOffStart: string;
  dropOffEnd: string;
} | null;

export type TimeRange = { startTime: string; endTime: string };
export type BusLegTimeRange = TimeRange | undefined;

export type FindPickUpDropOffTimeResponse = {
  pickUpStartTime: string | null;
  pickUpEndTime: string | null;
  dropOffTime: string | null;
};

export const findPickUpDropOffTime = (
  isLockedIn: boolean,
  timeWindows: TimeWindows,
  bustLegTimeRange: BusLegTimeRange,
): FindPickUpDropOffTimeResponse => {
  let pickUpStartTime: string | null = null;
  let pickUpEndTime: string | null = null;
  let dropOffTime: string | null = null;
  if (!isLockedIn && timeWindows) {
    pickUpStartTime = timeWindows.pickUpStart;
    pickUpEndTime = timeWindows.pickUpEnd;
    dropOffTime = timeWindows.dropOffEnd;
  } else if (bustLegTimeRange) {
    pickUpStartTime = bustLegTimeRange.startTime;
    dropOffTime = bustLegTimeRange.endTime;
  }
  return { pickUpStartTime, pickUpEndTime, dropOffTime };
};

export type FindAddressesResult = {
  pickUpAddress: OnDemandJourneyLegAddress | null;
  dropOffAddress: OnDemandJourneyLegAddress | null;
  toPickUpWalk: WalkingJourney | null;
  fromDropOffWalk: WalkingJourney | null;
};

export const findAddresses = (
  busLeg: OnDemandBookingJourneyLeg | OnDemandTripJourneyLeg | undefined,
  walkLegs: OnDemandBookingJourneyLeg[] | OnDemandTripJourneyLeg[],
): FindAddressesResult => {
  let pickUpAddress: OnDemandJourneyLegAddress | null = null;
  let dropOffAddress: OnDemandJourneyLegAddress | null = null;
  let toPickUpWalk: WalkingJourney | null = null;
  let fromDropOffWalk: WalkingJourney | null = null;
  if (busLeg) {
    pickUpAddress = busLeg.startAddress;
    dropOffAddress = busLeg.endAddress;

    for (const walkLeg of walkLegs) {
      if (isLegTripJourneyLeg(walkLeg)) {
        if (pickUpAddress && walkLeg.endAddress.placeId === pickUpAddress.placeId) {
          toPickUpWalk = { walkingAddress: walkLeg.startAddress, walkingDistance: walkLeg.distance, walkingDuration: walkLeg.travelTime };
        }
        if (dropOffAddress && walkLeg.startAddress.placeId === dropOffAddress.placeId) {
          fromDropOffWalk = {
            walkingAddress: walkLeg.endAddress,
            walkingDistance: walkLeg.distance,
            walkingDuration: walkLeg.travelTime,
          };
        }
      }
    }
  }

  return {
    pickUpAddress,
    dropOffAddress,
    toPickUpWalk,
    fromDropOffWalk,
  };
};

export const findTimeMessage = (
  t: TFunction,
  rideStatus: string,
  ridePointType: OnDemandRideTimeType,
  pickUpStartTime: string | null,
  pickUpEndTime: string | null,
  dropOffTime: string | null,
): {
  pickUpTimeMessage: string;
  dropOffTimeMessage: string;
} => {
  let pickUpTimeMessage: string = '';
  let dropOffTimeMessage: string = '';
  if (pickUpStartTime && dropOffTime) {
    if (pickUpEndTime) {
      pickUpTimeMessage = t('trip:betweenTimes', {
        startTime: format(new Date(pickUpStartTime), DATE_SHORT_TIME_FORMAT),
        endTime: format(new Date(pickUpEndTime), TIME_FORMAT),
      });
      dropOffTimeMessage = format(new Date(dropOffTime), TIME_FORMAT);
    } else if (pickUpStartTime === dropOffTime) {
      if (ridePointType === 'ARRIVAL_FIXED') {
        dropOffTimeMessage = format(new Date(dropOffTime), DATE_TIME_FORMAT);
      } else {
        pickUpTimeMessage = format(new Date(pickUpStartTime), DATE_TIME_FORMAT);
      }
    } else {
      pickUpTimeMessage = format(new Date(pickUpStartTime), DATE_TIME_FORMAT);
      dropOffTimeMessage = format(new Date(dropOffTime), TIME_FORMAT);
    }
  }
  return {
    pickUpTimeMessage,
    dropOffTimeMessage,
  };
};

export const extractBookingPointDataFromJourneyData = (
  t: TFunction,
  journey: OnDemandBookingJourney | OnDemandTripJourney,
  lockedInTime: number, // minutes
  rideStatus: string,
  ridePointType: OnDemandRideTimeType,
): JourneyDataBookingPoint => {
  let isLockedIn: boolean = isJourneyLockedIn(journey.lockTime);

  const { walkLegs, busLeg } = findWalkAndBusLeg(journey.journeyLegs);
  const { pickUpStartTime, pickUpEndTime, dropOffTime } = findPickUpDropOffTime(isLockedIn, journey.timeWindows, busLeg || undefined);
  const { pickUpAddress, dropOffAddress, toPickUpWalk, fromDropOffWalk } = findAddresses(busLeg, walkLegs);
  const { pickUpTimeMessage, dropOffTimeMessage } = findTimeMessage(
    t,
    rideStatus,
    ridePointType,
    pickUpStartTime,
    pickUpEndTime,
    dropOffTime,
  );

  return {
    pickUpAddress,
    dropOffAddress,
    toPickUpWalk,
    fromDropOffWalk,
    dropOffTimeMessage,
    pickUpTimeMessage,
  };
};

export type UnitOfMeasurement = 'imperial' | 'metric';

export type ImperialUnit = 'ft' | 'mi';

export type MetricUnit = 'm' | 'km';

export type DistanceMeasurement = {
  distance: number;
  unit: ImperialUnit | MetricUnit;
};

export const formatDistanceMeasurment = (distanceInMeters: number, toUnit: UnitOfMeasurement): DistanceMeasurement => {
  if (toUnit === 'metric') {
    return distanceInMeters >= M_TO_KM_THRESHOLD
      ? {
          distance: parseFloat(convert(distanceInMeters).from('m').to('km').toFixed(1)),
          unit: 'km',
        }
      : {
          distance: Math.ceil(distanceInMeters),
          unit: 'm',
        };
  } else {
    return distanceInMeters >= M_TO_MILES_THRESHOLD
      ? {
          distance: parseFloat(convert(distanceInMeters).from('m').to('mi').toFixed(1)),
          unit: 'mi',
        }
      : {
          distance: Math.ceil(convert(distanceInMeters).from('m').to('ft')),
          unit: 'ft',
        };
  }
};

export const getDistanceSuffix = (distance: number, unit: ImperialUnit | MetricUnit): string => {
  switch (unit) {
    case 'm':
      return i18n.t(distance === 1 ? 'common:metre' : 'common:metre_plural');
    case 'km':
      return i18n.t(distance === 1 ? 'common:kilometre' : 'common:kilometre_plural');
    case 'ft':
      return i18n.t(distance === 1 ? 'common:feet_abbreviated' : 'common:feet_abbreviated_plural');
    case 'mi':
      return i18n.t(distance === 1 ? 'common:mile' : 'common:mile_plural');
  }
};
