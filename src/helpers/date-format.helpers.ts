const MS_IN_MIN: number = 60000 as const;

const isDateNotNumber = (stopEta: Date | number): stopEta is Date => {
  return (stopEta as Date).getTime !== undefined;
};

export const calculateStopEta = (
  stopEta: number | Date,
  translatedMinuteSingular: string,
  translatedMinutePlural: string,
  useSuffix: boolean = true,
): string => {
  const nowPlusEta: Date = isDateNotNumber(stopEta) ? stopEta : new Date(new Date().getTime() + MS_IN_MIN * stopEta);
  const stopEtaMins: number = isDateNotNumber(stopEta) ? Math.ceil((stopEta.getTime() - new Date().getTime()) / 60000) : stopEta;
  const stopEtaMinsTrunc: number = Math.trunc(stopEtaMins);

  if (stopEtaMins > 60) {
    const hours: number = nowPlusEta.getHours() % 12 || 12;
    const dateSuffix: 'am' | 'pm' = nowPlusEta.getHours() >= 12 ? 'pm' : 'am';
    const minutes: number = nowPlusEta.getMinutes();
    const timeValue = `${hours}:${minutes < 10 ? '0' + minutes : minutes}`;
    return useSuffix ? `${timeValue}${dateSuffix}` : timeValue;
  }

  return useSuffix
    ? `${stopEtaMinsTrunc} ${minutesSingularPlural(stopEtaMinsTrunc, translatedMinuteSingular, translatedMinutePlural)}`
    : `${stopEtaMinsTrunc}`;
};

/**
 * Given a number of minutes it returns the correct singular/plural string
 * @param minutes - number of minutes
 * @param translatedMinuteSingular - minute string in singular
 * @param translatedMinutePlural - minutes string in plural
 * @returns string min or mins
 */
export const minutesSingularPlural = (minutes: number, translatedMinuteSingular: string, translatedMinutePlural: string) =>
  minutes === 1 ? translatedMinuteSingular : translatedMinutePlural;

export const dateRoundedDownToNearestMinute = (date: string | Date | number) =>
  new Date(Math.floor(new Date(date).getTime() / MS_IN_MIN) * MS_IN_MIN).toISOString();

export const dateRoundedUpToNearestMinutesInterval = (date: string | Date | number, interval: number) => {
  const roundedDate: Date = new Date(date);
  roundedDate.setMinutes(Math.ceil(roundedDate.getMinutes() / interval) * interval);
  return roundedDate;
};
