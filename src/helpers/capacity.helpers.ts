import styled from 'styled-components';

type Colour = {
  namedValue: string;
  themeValue: string;
};

export const capacityColor = (availableSeatsPercentage: number): Colour => {
  const colour: Colour = { namedValue: 'capacity-', themeValue: 'var(--ion-color-capacity-' };

  if (availableSeatsPercentage === 0) {
    colour.namedValue += 'red';
    colour.themeValue += 'red)';
  } else if (availableSeatsPercentage >= 30) {
    colour.namedValue += 'green';
    colour.themeValue += 'green)';
  } else {
    colour.namedValue += 'orange';
    colour.themeValue += 'orange)';
  }

  return colour;
};

export const RouteVehicleCapacityWrapper = styled.div<{ backgroundColour?: string }>`
  flex: 1;
  display: flex;
  padding: 10px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  align-content: stretch;
  text-transform: uppercase;
  background-color: ${(props) => `rgba(var(--ion-color-${props.backgroundColour}-rgb), 0.1)` ?? 'transparent'};
`;
