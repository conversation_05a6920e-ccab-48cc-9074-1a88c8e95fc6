// This is useful when a component can receive both a hex code and color var name
export const getColourName = (colour: string): string => (colour.startsWith('#') ? colour : `var(--ion-color-${colour})`);

export const alphaToHex = (alpha: number): string => {
  return Math.round(alpha * 255) // convert to a percentage of 255 (so 0.5 = 127.5)
    .toString(16) // convert to hex string
    .padStart(2, '0'); // add leading zero if single digit as it needs to be a 2 digit hex result
};

export const getColourNameWithOpacity = (name: string, opacity: number): string =>
  name.startsWith('#') ? `${name}${alphaToHex(opacity)}` : `rgba(var(--ion-color-${name}-rgb), ${opacity})`;

export const FALLBACK_DARK_COLOUR = 'black';
export const FALLBACK_LIGHT_COLOUR = 'white';
