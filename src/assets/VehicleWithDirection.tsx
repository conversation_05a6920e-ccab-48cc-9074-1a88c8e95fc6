import React from 'react';
import styled from 'styled-components';

type VehicleWithDirectionProps = {
  colour: string;
  direction: number;
};

const VehicleWithDirection = (props: VehicleWithDirectionProps) => {
  return (
    <StyledVehicleContainer direction={props.direction}>
      <svg id="Layer_1" viewBox="0 0 36 100" version="1.1" width="32" height="100" xmlns="http://www.w3.org/2000/svg">
        <defs id="defs4">
          <linearGradient
            id="linear-gradient"
            x1="16.469999"
            y1="28.610001"
            x2="16.469999"
            y2="0"
            gradientUnits="userSpaceOnUse"
            gradientTransform="matrix(1,0,0,-1,-5.6314152e-8,28.607778)"
          >
            <stop offset="0" stopColor="#fff" stopOpacity="0" id="stop291" />
            <stop offset="0.6" stopOpacity="0.8" stopColor={props.colour} id="stop307" />
          </linearGradient>
        </defs>
        <path
          d="M 12.420001,28.537778 -5.6314152e-8,7.7377773 c 0,0 14.930001056314152,-17.409999 32.95000105631415,0 l -11.47,20.8700007 z"
          id="path312"
          fill="url(#linear-gradient)"
        />
        <path
          fill={props.colour}
          fillRule="evenodd"
          filter="drop-shadow( 2px 2px 3px rgba(0, 0, 0, .7))"
          d="m 24.526102,46.430222 c 3.27,1.28 3.27,3.21 3.27,3.21 l 1.02,2.85 v 35.95 c 0,1.58 -1.78,3.75 -3.24,3.75 H 8.0561011 c -1.46,0 -3.24,-2.18 -3.24,-3.75 v -35.95 l 1.02,-2.85 c 0,0 0,-2.06 3.61,-3.34 l 0.1400004,-0.32 c 0.17,-0.7 0.8600005,-1.19 1.6500005,-1.19 h 11.45 c 0.8,0 1.49,0.5 1.65,1.19 l 0.18,0.44 z"
          id="path6"
        />
        <g opacity=".3" id="g10" transform="matrix(1,0,0,-1,4.0661011,92.190222)">
          <path
            fillRule="evenodd"
            d="m 3.8,39 h 17.9 c 0.75,0 1.2,-1.04 0.8,-1.84 L 21.19,34.42 C 20.84,33.72 20.24,33.3 19.59,33.3 H 6.14 c -0.65,0 -1.25,0.42 -1.6,1.12 L 3,37.16 C 2.6,37.96 3.05,39 3.8,39 Z m -0.23,-5.47 -1.1,2 v 0 C 2.36,35.78 2.01,35.7 2.01,35.42 V 3.43 C 2.01,3.18 2.3,3.08 2.44,3.27 l 0.45,0.62 c 0.52,0.73 1.04,1.62 1.04,2.54 v 25.34 c 0,0.61 -0.12,1.21 -0.36,1.76 z m 18.36,0 1.1,2 v 0 c 0.11,0.25 0.46,0.17 0.46,-0.11 V 3.43 c 0,-0.25 -0.29,-0.35 -0.43,-0.16 l -0.45,0.62 c -0.52,0.73 -1.04,1.62 -1.04,2.54 v 25.34 c 0,0.61 0.12,1.21 0.36,1.76 z M 21.7,1.5 H 3.8 C 3.05,1.5 2.6,2.1 3,2.57 l 0.89,1.59 c 0.35,0.4 0.95,0.65 1.6,0.65 H 20 c 0.65,0 1.26,-0.24 1.6,-0.65 L 22.49,2.57 C 22.89,2.11 22.44,1.5 21.69,1.5 Z"
            id="path8"
          />
        </g>
        <path
          fill={props.colour}
          filter="drop-shadow( 2px 2px 3px rgba(0, 0, 0, .7))"
          d="m 26.306102,39.580222 c -1.04,-0.37 -2.09,-0.69 -3.16,-0.94 -2.18,-0.52 -4.42,-0.78 -6.66,-0.77 -4.2,0 -8.6300009,0.83 -12.4100009,2.7 2.69,-3.96 5.3900004,-7.93 8.0800009,-11.89 0.57,-0.84 1.13,-1.71 1.72,-2.54 1.3,-1.81 3.69,-1.79 5.36,-0.55 0,0 9.88,15.17 9.88,15.17 -0.33,-0.51 -2.2,-0.95 -2.81,-1.17 z m -9.56,-11.99 -6.09,8.17 c 5.17,-1.21 11.85,-0.02 11.85,-0.02 l -5.76,-8.14 z"
          id="path12"
        />
        <path fill="#fff" d="m 16.826102,27.600222 -6.09,8.17 c 5.17,-1.21 11.85,-0.02 11.85,-0.02 l -5.76,-8.14 z" id="path14" />
      </svg>
    </StyledVehicleContainer>
  );
};

const StyledVehicleContainer = styled.div<{ direction: number }>`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(${(props) => props.direction}deg);
  width: 100px;
  height: 100px;
  overflow: hidden;
  padding-left: 4px;
`;

export default VehicleWithDirection;
