import { NetworkFeaturesMap } from '@liftango/liftango-client';
import { useAppSelector } from './storeHooks';
import { selectNetworkEnforced } from '../store/reducers/networkReducer';

type NetworkFeaturesState = {
  networkFeatures: NetworkFeaturesMap;
};

const useNetworkFeatures = (): NetworkFeaturesState => {
  const { features: networkFeatures } = useAppSelector(selectNetworkEnforced);
  return { networkFeatures };
};

export default useNetworkFeatures;
