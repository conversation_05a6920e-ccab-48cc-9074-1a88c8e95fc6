import React, { useEffect } from 'react';

type UserScrollableOptions = {
  horizontal?: boolean;
  allowOverscroll?: boolean;
};

const useScrollable = (
  ref: React.MutableRefObject<HTMLDivElement>,
  onScrollUpdate?: (value: number) => void,
  options: UserScrollableOptions = {},
) => {
  useEffect(() => {
    // give the clean up time run
    const currentRef: React.MutableRefObject<HTMLDivElement>['current'] = ref.current;

    let isDown = false;
    let startPosition: number = 0;
    let scrollAmount: number = 0;

    const startHandler = (e: MouseEvent | TouchEvent) => {
      isDown = true;
      ref.current.classList.add('active');

      if (options.horizontal) {
        if ('pageX' in e) {
          startPosition = e.pageX;
        } else if ('touches' in e) {
          startPosition = e.touches[0].pageX;
        }
      } else {
        if ('pageY' in e) {
          startPosition = e.pageY;
        } else if ('touches' in e) {
          startPosition = e.touches[0].pageY;
        }
      }

      if (options.horizontal) {
        scrollAmount = ref.current.scrollLeft;
      } else {
        scrollAmount = ref.current.scrollTop;
      }
    };

    const endHandler = () => {
      if (isDown && typeof onScrollUpdate === 'function') {
        onScrollUpdate(options.horizontal ? ref.current.scrollLeft : ref.current.scrollTop);
      }

      isDown = false;
      ref.current.classList.remove('active');
    };

    const moveHandler = (e: MouseEvent | TouchEvent) => {
      if (!isDown) {
        return;
      }

      e.preventDefault();

      let currentPosition: number = 0;
      if (options.horizontal) {
        if ('pageX' in e) {
          currentPosition = e.pageX;
        } else if ('touches' in e) {
          currentPosition = e.touches[0].pageX;
        }
      } else {
        if ('pageY' in e) {
          currentPosition = e.pageY;
        } else if ('touches' in e) {
          currentPosition = e.touches[0].pageY;
        }
      }

      const speed = (currentPosition - startPosition) * 2;
      const scrollTo: number = scrollAmount - speed;

      if (options.horizontal) {
        if (options.allowOverscroll && scrollTo < 0) {
          ref.current.scrollLeft = ref.current.scrollWidth;
        } else if (options.allowOverscroll && scrollTo > ref.current.scrollWidth) {
          ref.current.scrollLeft = 0;
        } else {
          ref.current.scrollLeft = scrollTo;
        }
      } else {
        if (options.allowOverscroll && scrollTo < 0) {
          ref.current.scrollTop = ref.current.scrollHeight;
        } else if (options.allowOverscroll && scrollTo > ref.current.scrollHeight) {
          ref.current.scrollTop = 0;
        } else {
          ref.current.scrollTop = scrollTo;
        }
      }
    };

    if (currentRef) {
      currentRef.addEventListener(`mousedown`, startHandler);
      currentRef.addEventListener(`mouseleave`, endHandler);
      currentRef.addEventListener(`mouseup`, endHandler);
      currentRef.addEventListener(`mousemove`, moveHandler);
      currentRef.addEventListener(`touchstart`, startHandler);
      currentRef.addEventListener(`touchend`, endHandler);
      currentRef.addEventListener(`touchmove`, moveHandler);
    }

    return () => {
      if (currentRef) {
        currentRef.removeEventListener(`mousedown`, startHandler);
        currentRef.removeEventListener(`mouseleave`, endHandler);
        currentRef.removeEventListener(`mouseup`, endHandler);
        currentRef.removeEventListener(`mousemove`, endHandler);
        currentRef.removeEventListener(`touchstart`, startHandler);
        currentRef.removeEventListener(`touchend`, endHandler);
        currentRef.removeEventListener(`mousemove`, moveHandler);
      }
    };
  }, [ref, onScrollUpdate, options.allowOverscroll, options.horizontal]);
};

export default useScrollable;
