import React, { useEffect, useState } from 'react';
import { HailerOpsClient, FixedRoute } from '@liftango/ops-client';
import { useHailerService } from '../services/HailerService';

const useShiftSolutionDetails = (shiftSolutionId?: string): FixedRoute.RunningShiftSolutionDetails | null => {
  const hailerService = useHailerService();
  const [shiftSolutionDetails, setShiftSolutionDetails] = useState<FixedRoute.RunningShiftSolutionDetails | null>(null);

  useEffect(() => {
    if (shiftSolutionId) {
      hailerService.FixedRoute.Service.getRunningShiftSolutionDetails(shiftSolutionId).then((result) => {
        setShiftSolutionDetails(result.shiftSolution);
      });
    }
  }, [shiftSolutionId]);

  return shiftSolutionDetails;
};

export default useShiftSolutionDetails;
