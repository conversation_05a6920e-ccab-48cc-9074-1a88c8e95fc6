import { useEffect, useState, useCallback } from 'react';
import { format as dateFnsFormat, Locale } from 'date-fns';
import * as Locales from 'date-fns/locale';
import i18n from '../i18n';

const DEFAULT_LOCALE = Locales.enGB;

export default function useLocalisedDate() {
  const [locale, setLocale] = useState<Locale>(DEFAULT_LOCALE);

  useEffect(() => {
    const [language, region] = i18n.language.split('-');
    const convertedLocaleString: string = `${language}${region ? region.toUpperCase() : ''}`;
    // Locales is a Module object, so we need to convert it to a plain object to get arbitrary properties from it
    const localeObject = convertedLocaleString in Locales ? { ...Locales }[convertedLocaleString] : DEFAULT_LOCALE;
    if (localeObject) {
      setLocale(localeObject);
    }
  }, [i18n.language]);

  const localiseDate = useCallback(
    (
      date: Date | number,
      format: string,
      options?: {
        locale?: Locale;
        weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
        firstWeekContainsDate?: number;
        useAdditionalWeekYearTokens?: boolean;
        useAdditionalDayOfYearTokens?: boolean;
      },
    ): string => {
      return dateFnsFormat(date, format, { ...options, locale });
    },
    [locale],
  );

  return { localiseDate };
}
