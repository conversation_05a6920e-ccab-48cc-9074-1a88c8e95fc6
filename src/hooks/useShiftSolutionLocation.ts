import { useCallback, useEffect, useMemo, useState } from 'react';
import { HailerOpsClient, FixedRoute } from '@liftango/ops-client';
import { useHailerService } from '../services/HailerService';
import { usePusher } from '@harelpls/use-pusher';
import { Channel } from 'pusher-js';
import { useAppSelector } from './storeHooks';
import { selectNetworkEnforced } from '../store/reducers/networkReducer';
import { selectTheme, Theme } from '../store/reducers/themeReducer';
import { selectFilteredRoutes } from '../store/reducers/routesReducer';
import { pusherEvent } from '../helpers/pusher.helpers';
import { useInterval } from 'usehooks-ts';


type FixedRouteShiftSolutionLocationPush = {
  shiftSolutionId: string;
  latitude: number;
  longitude: number;
  recordedAt: string;
  route: {
    label: string;
    colour: string;
  };
  availableCapacities: {
    amount: number;
    capacityName: string;
    capacityTypeId: string;
    displayOrder: number;
  }[];
  etaNextStop: number | null;
};

export type RunningShiftSolutionsDictionary = Record<string, FixedRoute.RunningShiftSolution>;

const RUNNING_SHIFT_SOLUTION_TICK: number = 10000; // every 10 seconds

const useRunningShiftSolutions = (): [
  RunningShiftSolutionsDictionary,
  RunningShiftSolutionsDictionary,
  (routeVersionId: string) => boolean,
] => {
  const hailerService = useHailerService();
  const pusher = usePusher();
  const [currentShiftSolutions, setCurrentShiftSolutions] = useState<Record<string, FixedRoute.RunningShiftSolution>>({});

  const filteredRoutes: FixedRoute.ServiceRoutesAndStopsPayload[] = useAppSelector(selectFilteredRoutes);
  const filteredShiftSolutions: Record<string, FixedRoute.RunningShiftSolution> = useMemo(
    () =>
      Object.keys(currentShiftSolutions)
        .filter((currentShiftSolutionId: string) =>
          filteredRoutes.find((filteredRoute: FixedRoute.ServiceRoutesAndStopsPayload) =>
            [filteredRoute.id, filteredRoute.versionId].includes(currentShiftSolutions[currentShiftSolutionId].route.id),
          ),
        )
        .reduce((filteredShiftSolutionsAcc: Record<string, FixedRoute.RunningShiftSolution>, filteredShiftSolutionIdCurrent: string) => {
          filteredShiftSolutionsAcc[filteredShiftSolutionIdCurrent] = currentShiftSolutions[filteredShiftSolutionIdCurrent];
          return filteredShiftSolutionsAcc;
        }, {}),
    [currentShiftSolutions, filteredRoutes],
  );

  const network = useAppSelector(selectNetworkEnforced);
  const { distinguishInactiveRoutes }: Theme = useAppSelector(selectTheme);

  const locationChannels: (Channel | undefined)[] = [];

  const runningShiftSolutionsRunner = useCallback(() => {
    if (network.id) {
      hailerService.FixedRoute.Service.getRunningShiftSolutions(network.id).then((result) => {
        const runningShiftSolutions = result.shiftSolutions.reduce<RunningShiftSolutionsDictionary>(
          (acc, shiftSolution: FixedRoute.RunningShiftSolution) => {
            acc[shiftSolution.shiftSolutionId] = shiftSolution;
            return acc;
          },
          {},
        );
        setCurrentShiftSolutions((prevState) => {
          Object.keys(prevState)
            .filter((x) => !runningShiftSolutions[x])
            .forEach((x) => locationChannels.filter((y) => y?.pusher.channels.find(x)?.unbind()));
          return runningShiftSolutions;
        });
      });
    }
  }, [hailerService, network.id, locationChannels.length]);

  const updateShiftSolutionPosition = useCallback((data: FixedRouteShiftSolutionLocationPush | undefined) => {
    if (data) {
      setCurrentShiftSolutions((prevState) => {
        const oldShiftSolution: FixedRoute.RunningShiftSolution | null = prevState[data.shiftSolutionId] || null;

        if (oldShiftSolution) {
          return {
            ...prevState,
            [oldShiftSolution.shiftSolutionId]: {
              ...oldShiftSolution,
              location: {
                latitude: data.latitude,
                longitude: data.longitude,
                timestamp: data.recordedAt,
              },
              availableCapacities: data.availableCapacities,
              etaNextStop: data.etaNextStop,
              route: {
                ...oldShiftSolution.route,
                label: data.route.label,
                colour: data.route.colour,
              },
            },
          };
        }

        return prevState;
      });
    }
  }, []);

  useInterval(runningShiftSolutionsRunner, RUNNING_SHIFT_SOLUTION_TICK);

  useEffect(() => {
    runningShiftSolutionsRunner();
  }, [runningShiftSolutionsRunner]);

  useEffect(() => {
    const tempLocationChannels = Object.values(currentShiftSolutions).map((currentShiftSolution) =>
      pusher.client?.subscribe(currentShiftSolution.shiftSolutionId),
    );
    locationChannels.push(...tempLocationChannels);

    if (locationChannels.length) {
      for (const channel of locationChannels) {
        channel?.bind(pusherEvent.FixedRouteShiftSolutionLocation, updateShiftSolutionPosition);
      }

      return () => {
        locationChannels?.map((channel) => {
          channel?.unbind();
        });
      };
    }
  }, [currentShiftSolutions, updateShiftSolutionPosition]);

  /**
   * @returns If the active theme has distinguishInactiveRoutes flag, the function checks if the route belongs to any active shift solution, otherwise always returns TRUE!
   */
  const checkIsRouteActive = (routeVersionId: string): boolean => {
    if (distinguishInactiveRoutes) {
      let isRouteActive: boolean = false;
      if (currentShiftSolutions) {
        for (const [key, value] of Object.entries(currentShiftSolutions)) {
          if (value.route.id === routeVersionId) {
            isRouteActive = true;
          }
        }
      }
      return isRouteActive;
    } else {
      return true;
    }
  };

  return [currentShiftSolutions, filteredShiftSolutions, checkIsRouteActive];
};

export default useRunningShiftSolutions;
