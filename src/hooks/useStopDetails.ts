import React, { useEffect, useState } from 'react';
import { HailerOpsClient, FixedRoute } from '@liftango/ops-client';
import { useHailerService } from '../services/HailerService';

const useStopDetails = (networkId?: string, stopId?: string): FixedRoute.RunningStopDetails[] => {
  const hailerService = useHailerService();
  const [stopDetails, setStopDetails] = useState<FixedRoute.RunningStopDetails[]>([]);

  const STOP_DETAILS_TICK: number = 60000; // every minute

  const getRunningStopDetails = (networkId: string, stopId: string) =>
    hailerService.FixedRoute.Service.getRunningStopDetails(networkId, stopId).then((result) => {
      setStopDetails(result.stop);
    });

  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    if (stopId && networkId) {
      getRunningStopDetails(networkId, stopId);
      intervalId = setInterval(() => {
        getRunningStopDetails(networkId, stopId);
      }, STOP_DETAILS_TICK);
    }

    return () => clearInterval(intervalId);
  }, [stopId, networkId]);

  return stopDetails;
};

export default useStopDetails;
