import React, { useCallback, useEffect, useState } from 'react';
import { HailerOpsClient, Kiosk } from '@liftango/ops-client';
import { useHailerService } from '../services/HailerService';
import { usePusher } from '@harelpls/use-pusher';
import { Channel } from 'pusher-js';
import { ApplicationMode, selectApplicationMode } from '../store/reducers/applicationReducer';
import { useAppSelector } from '../hooks/storeHooks';
import { pusherEvent } from '../helpers/pusher.helpers';

type OnDemandScheduleLocationPush = {
  scheduleId: string;
  latitude: number;
  longitude: number;
  recordedAt: string;
};

export type SchedulePositionsDictionary = Record<string, Kiosk.RunningScheduleLocation> | null;

const useScheduleLocation = (networkId?: string): SchedulePositionsDictionary => {
  const hailerService = useHailerService();
  const [currentSchedules, setCurrentSchedules] = useState<Kiosk.RunningSchedule[]>([]);
  const [schedulePositions, setSchedulePosition] = useState<SchedulePositionsDictionary>(null);
  const application: ApplicationMode = useAppSelector(selectApplicationMode);
  const pusher = usePusher();

  useEffect(() => {
    if (networkId && application === 'kiosk') {
      hailerService.Kiosk.Service.getRunningSchedules(networkId).then((result: Kiosk.GetRunningSchedulesResult) => {
        setCurrentSchedules(result.schedules);
      });
    }
  }, [networkId]);

  const updateSchedulePosition = useCallback((data: OnDemandScheduleLocationPush | undefined) => {
    if (data) {
      setSchedulePosition((prevState) => ({
        ...prevState,
        [data.scheduleId]: {
          scheduleId: data.scheduleId,
          latitude: data.latitude,
          longitude: data.longitude,
          timestamp: data.recordedAt,
        },
      }));
    }
  }, []);

  useEffect(() => {
    for (const currentSchedule of currentSchedules) {
      if (currentSchedule.location) {
        setSchedulePosition((prevState) => {
          if (
            !currentSchedule.location ||
            (prevState &&
              currentSchedule.location &&
              prevState[currentSchedule.scheduleId] &&
              prevState[currentSchedule.scheduleId].timestamp > currentSchedule.location.timestamp)
          ) {
            return prevState;
          }

          return {
            ...prevState,
            [currentSchedule.scheduleId]: currentSchedule.location,
          };
        });
      }
    }
  }, [currentSchedules]);

  useEffect(() => {
    const locationChannels: (Channel | undefined)[] = currentSchedules.map((currentSchedule) =>
      pusher.client?.subscribe(currentSchedule.scheduleId),
    );

    if (locationChannels) {
      for (const channel of locationChannels) {
        channel?.bind(pusherEvent.OnDemandScheduleLocation, updateSchedulePosition);
      }
    }

    return () => {
      if (locationChannels) {
        locationChannels.map((channel) => {
          channel?.unbind();
        });
      }
    };
  }, [updateSchedulePosition, currentSchedules]);

  return schedulePositions;
};

export default useScheduleLocation;
