import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router';

const useQueryParam = (key: string): string => {
  const [queryParam, setQueryParam] = useState<string>('');
  const queryString: string = useLocation().search;

  useEffect(() => {
    const param: string = new URLSearchParams(queryString).get(key) ?? '';

    setQueryParam(param);
  }, [queryString, key]);

  return queryParam;
};

export default useQueryParam;
