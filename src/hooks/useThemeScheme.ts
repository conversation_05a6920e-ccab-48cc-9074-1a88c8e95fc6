import { useEffect } from 'react';
import { useAppDispatch } from './storeHooks';
import { setScheme, ThemeScheme } from '../store/reducers/themeReducer';

const useThemeScheme = (themeScheme: ThemeScheme) => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    document.body.classList.toggle('dark', themeScheme === 'dark');
  }, [themeScheme]);

  useEffect(() => {
    const handleThemeModeChange = (isDark: boolean) => {
      dispatch(setScheme(isDark ? 'dark' : 'light'));
    };

    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');

    const listener = () => handleThemeModeChange(prefersDark.matches);

    // Listen for changes to the prefers-color-scheme media query
    prefersDark.addEventListener('change', listener);

    return () => {
      prefersDark.removeEventListener('change', listener);
    };
  });
};

export default useThemeScheme;
