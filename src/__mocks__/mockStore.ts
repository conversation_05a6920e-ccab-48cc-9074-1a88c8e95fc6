export const mockAlertsData = [
  {
    id: 107,
    title: 'Test 1',
    message: 'test message 1',
    type: 'Information',
    startTime: '2023-10-16T11:46:34.072Z',
    endTime: '2023-10-24T11:46:34.075Z',
    createdAt: '2023-10-16T11:46:56.150Z',
    updatedAt: '2023-10-16T11:46:56.150Z',
  },
  {
    id: 108,
    title: 'Test 2',
    message: 'test message 2',
    type: 'Warning',
    startTime: '2023-10-16T11:46:34.072Z',
    endTime: '2023-10-24T11:46:34.075Z',
    createdAt: '2023-10-16T11:47:30.231Z',
    updatedAt: '2023-10-16T11:47:30.231Z',
  },
  {
    id: 109,
    title: 'Test 3',
    message: 'test message 3',
    type: 'Error',
    startTime: '2023-10-16T11:46:34.072Z',
    endTime: '2023-10-24T11:46:34.075Z',
    createdAt: '2023-10-16T11:47:51.890Z',
    updatedAt: '2023-10-16T11:47:51.890Z',
  },
];

export const mockThemeData = {
  theme: {
    scheme: 'light',
    themeList: [],
    theme: {
      name: 'nike',
      map: {},
      colors: {
        primary: '#FFFFFF',
        primaryRgb: '255, 255, 255',
        secondary: '#000000',
        secondaryRgb: '0, 0, 0',
        accent: '#888888',
      },
      logo: 'mockLogo.svg',
      favicon: 'mockFavicon.png',
      cssFilePath: 'mock',
    },
  },
};
