/// <reference types="vite/client" />
/// <reference types="vite-plugin-svgr/client" />

interface ImportMetaEnv {
  readonly VITE_REACT_APP_PRODUCT: string;
  readonly VITE_REACT_APP_HAILER_BASE_URL: string;
  readonly VITE_REACT_APP_VERSION: string;
  readonly VITE_REACT_APP_TITLE: string;
  readonly VITE_REACT_APP_GOOGLE_MAPS_API_KEY: string;
  readonly VITE_REACT_APP_PUSHER_CLUSTER: string;
  readonly VITE_REACT_APP_PUSHER_KEY: string;
  readonly VITE_REACT_APP_DISABLE_REACT_SPRING_TRANSITIONS: string;
  readonly VITE_REACT_APP_ENVIRONMENT: string;
  readonly VITE_GA_TRACKING_ID: string;
  readonly VITE_REACT_APP_SENTRY_DSN_URL: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

declare global {
  interface Navigator {
    msSaveBlob?: (blob: any, defaultName?: string) => boolean;
  }
}
