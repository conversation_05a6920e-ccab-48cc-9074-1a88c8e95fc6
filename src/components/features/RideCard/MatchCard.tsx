import React, { useState } from 'react';
import styled from 'styled-components';
import { differenceInMinutes, format } from 'date-fns';
import VehicleEstimate from '../../components/VehicleEstimate/VehicleEstimate';
import LocationsIcons, { LocationLeg } from '../../components/LocationsIcons/LocationsIcons';
import { useTranslation } from 'react-i18next';
import RideCardLeg from './RideCardLeg';
import { locationSharp, navigateCircle } from 'ionicons/icons';
import { DATE_TIME_FORMAT, TIME_FORMAT } from '../../../constants/formatting';
import PassengerCount from '../../components/Passenger/PassengerCount';
import { useInterval } from 'usehooks-ts';

export type MatchCardProps = {
  passengerCount: number;
  ride: {
    departureTime: string;
    arrivalTime: string;
    departure: {
      label: string;
      hubName?: string;
    };
    arrival: {
      label: string;
      hubName?: string;
    };
    vehicle?: {
      picture: string;
    } | null;
  };
  className?: string;
};

const colors: string[] = ['#7DC8C6', '#EE7334'];

const MatchCard = (props: MatchCardProps) => {
  const [currentTime, setCurrentTime] = useState<Date>(new Date());

  const { t } = useTranslation<['common', 'trip']>(['common', 'trip']);

  useInterval(() => {
    setCurrentTime(new Date());
  }, 60000);

  const locations: LocationLeg[] = [
    {
      id: 'pickup',
      icon: navigateCircle,
      content: (
        <RideCardLeg
          title={t('common:Pickup') + ': ' + format(new Date(props.ride.departureTime), DATE_TIME_FORMAT)}
          subtitle={props.ride.departure.hubName || props.ride.departure.label}
          message={props.ride.departure.hubName ? props.ride.departure.label : ''}
        />
      ),
    },
    {
      id: 'dropdown',
      icon: locationSharp,
      content: (
        <RideCardLeg
          title={t('common:Dropoff') + ': ' + format(new Date(props.ride.arrivalTime), TIME_FORMAT)}
          subtitle={props.ride.arrival.hubName || props.ride.arrival.label}
          message={props.ride.departure.hubName ? props.ride.arrival.label : ''}
        />
      ),
    },
  ];

  return (
    <StyledMatchCard className={props.className}>
      {props.ride.vehicle?.picture ? (
        <StyledVehicleEstimate
          label={t('trip:estimateMin', { count: differenceInMinutes(new Date(props.ride.departureTime), currentTime) })}
          src={props.ride.vehicle.picture}
        />
      ) : null}

      <StyledMatchCardContent>
        <LocationsIcons locations={locations} colors={colors} size="md" />

        <StyledPassengerCount count={props.passengerCount} />
      </StyledMatchCardContent>
    </StyledMatchCard>
  );
};

const StyledMatchCard = styled.div`
  padding: 15px 0;
  overflow: auto;
  display: flex;
  align-items: flex-start;
`;

const StyledMatchCardContent = styled.div`
  display: flex;
  align-items: flex-start;
  flex-direction: column;
`;

const StyledPassengerCount = styled(PassengerCount)`
  margin-top: 15px;
`;

const StyledVehicleEstimate = styled(VehicleEstimate)`
  margin-right: 20px;
`;

export default MatchCard;
