import React, { useState } from 'react';
import { OnDemandRidePayload } from '@liftango/liftango-client';
import styled from 'styled-components';
import { differenceInMinutes } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { locationSharp, navigateCircle, walkSharp } from 'ionicons/icons';
import VehicleEstimate from '../../components/VehicleEstimate/VehicleEstimate';
import LocationsIcons, { LocationLeg } from '../../components/LocationsIcons/LocationsIcons';
import RideCardLeg from './RideCardLeg';
import { DistanceMeasurement, extractBookingPointDataFromJourneyData, formatDistanceMeasurment } from '../../../helpers/journey.helpers';
import { useAppSelector } from '../../../hooks/storeHooks';
import { selectNetworkEnforced } from '../../../store/reducers/networkReducer';
import Avatar from '../../elements/Avatar/Avatar';
import PassengerCount from '../../components/Passenger/PassengerCount';
import { useInterval } from 'usehooks-ts';

export type RideCardProps = {
  ride: OnDemandRidePayload;
  lockedInTime?: number;
  className?: string;
};

const colors: string[] = ['#7DC8C6', '#EE7334'];

const RideCard = (props: RideCardProps) => {
  const network = useAppSelector(selectNetworkEnforced);
  const [currentTime, setCurrentTime] = useState<Date>(new Date());

  const { t } = useTranslation<['common', 'trip']>(['common', 'trip']);

  useInterval(() => {
    setCurrentTime(new Date());
  }, 60000);

  const {
    pickUpAddress,
    dropOffAddress,
    toPickUpWalk,
    fromDropOffWalk,
    pickUpTimeMessage,
    dropOffTimeMessage,
  } = extractBookingPointDataFromJourneyData(
    t,
    props.ride.journeyData.journey,
    props.lockedInTime || 0,
    props.ride.status,
    props.ride.timeType,
  );

  const locations: LocationLeg[] = [
    {
      id: 'pickup',
      icon: navigateCircle,
      content: (
        <RideCardLeg
          title={t('common:Pickup') + `: ${pickUpTimeMessage}`}
          subtitle={pickUpAddress ? pickUpAddress.label : props.ride.departure.label}
        />
      ),
    },
    {
      id: 'dropoff',
      icon: locationSharp,
      content: (
        <RideCardLeg
          title={t('common:Dropoff') + `: ${dropOffTimeMessage}`}
          subtitle={dropOffAddress ? dropOffAddress.label : props.ride.arrival.label}
        />
      ),
    },
  ];

  if (toPickUpWalk) {
    const formattedDistance: DistanceMeasurement = formatDistanceMeasurment(
      toPickUpWalk.walkingDistance,
      network.formatting.unitOfMeasurement,
    );
    locations.unshift({
      id: 'pickupWalk',
      icon: walkSharp,
      content: (
        <RideCardLeg
          title={t('trip:walkJourneyLeg', {
            distance: `${formattedDistance.distance}${formattedDistance.unit}`,
            count: Math.ceil(toPickUpWalk.walkingDuration / 60),
          })}
          subtitle={toPickUpWalk.walkingAddress.label}
        />
      ),
    });
  }

  if (fromDropOffWalk) {
    const formattedDistance: DistanceMeasurement = formatDistanceMeasurment(
      fromDropOffWalk.walkingDistance,
      network.formatting.unitOfMeasurement,
    );
    locations.push({
      id: 'dropoffWalk',
      icon: walkSharp,
      content: (
        <RideCardLeg
          title={t('trip:walkJourneyLeg', {
            distance: `${formattedDistance.distance}${formattedDistance.unit}`,
            count: Math.ceil(fromDropOffWalk.walkingDuration / 60),
          })}
          subtitle={fromDropOffWalk.walkingAddress.label}
        />
      ),
    });
  }

  const diffCount: number = differenceInMinutes(new Date(props.ride.departureTime), currentTime);

  return (
    <StyledRideCard className={props.className}>
      {props.ride.vehicle?.picture ? (
        diffCount <= 0 || diffCount >= 100 ? (
          <StyledVehicleAvatar src={props.ride.vehicle.picture} />
        ) : (
          <StyledVehicleEstimate label={t('trip:estimateMin', { count: diffCount })} src={props.ride.vehicle.picture} />
        )
      ) : null}

      <StyledRideCardContent>
        <LocationsIcons locations={locations} colors={colors} size="sm" />

        <StyledPassengerCount count={props.ride.tripPassengers.length} />
      </StyledRideCardContent>
    </StyledRideCard>
  );
};

const StyledRideCard = styled.div`
  display: flex;
  align-items: flex-start;
  padding: 15px 0;
  overflow: auto;
`;

const StyledVehicleEstimate = styled(VehicleEstimate)`
  margin-right: 10px;
`;

const StyledRideCardContent = styled.div`
  display: flex;
  align-items: flex-start;
  flex-direction: column;
`;

const StyledPassengerCount = styled(PassengerCount)`
  margin-top: 15px;
`;

const StyledVehicleAvatar = styled(Avatar)`
  margin-right: 10px;
`;

export default RideCard;
