import React from 'react';
import styled from 'styled-components';
import Text from '../../elements/Text/Text';

type RideCardLegProps = {
  title: string;
  subtitle: string;
  message?: string;
  className?: string;
};

const RideCardLeg = (props: RideCardLegProps) => {
  return (
    <StyledRideCardLeg className={props.className}>
      <StyledRideCardLegTitle weight={500} size={0.9}>
        {props.title}
      </StyledRideCardLegTitle>

      <StyledRideCardLegSubtitle weight={400} size={0.75}>
        {props.subtitle}
      </StyledRideCardLegSubtitle>

      {props.message && <Text size={0.7}>{props.message}</Text>}
    </StyledRideCardLeg>
  );
};

const StyledRideCardLeg = styled.div``;

const StyledRideCardLegTitle = styled(Text)`
  margin-bottom: 2px;
`;

const StyledRideCardLegSubtitle = styled(Text)`
  margin-bottom: 3px;
`;

export default RideCardLeg;
