import { render, waitFor, fireEvent, within } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import Alerts from './Alert';
import { mockThemeData } from '../../../__mocks__/mockStore';
import { mockAlertsData } from '../../../__mocks__/mockStore';

vi.mock('../../../services/AlertsApi', () => ({
  useAlertsApi: vi.fn().mockReturnValue({
    fetchAllAlerts: vi.fn(() => Promise.resolve(mockAlertsData)),
  }),
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

const mockStore = configureMockStore();
const store = mockStore({
  alerts: {
    alerts: mockAlertsData,
    loading: false,
    error: null,
  },
  theme: mockThemeData,
});

describe('<Alerts />', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('renders fetched alerts after component mount', async () => {
    const { container, getByText } = render(
      <Provider store={store}>
        <Alerts />
      </Provider>,
    );

    await waitFor(() => expect(getByText('Test 1')).toBeInTheDocument());

    expect(getByText('Test 1')).toBeInTheDocument();
    expect(getByText('test message 1')).toBeInTheDocument();
    expect(getByText('Test 2')).toBeInTheDocument();
    expect(getByText('test message 2')).toBeInTheDocument();
    expect(getByText('Test 3')).toBeInTheDocument();
    expect(getByText('test message 3')).toBeInTheDocument();
  });

  it('dismisses an alert when the Dismiss button is clicked', async () => {
    const { getByText, queryByText } = render(
      <Provider store={store}>
        <Alerts />
      </Provider>,
    );

    // Ensure the alert is in the document
    const alertTitle = getByText('Test 1');
    expect(alertTitle).toBeInTheDocument();

    const test1Alert: HTMLElement | null = alertTitle.closest('[data-testid="alert-banner"]');
    if (test1Alert) {
      const dismissButton = within(test1Alert).getByText('common:dismiss');
      fireEvent.click(dismissButton);
    }

    // Ensure the alert is no longer in the document
    expect(queryByText('Test 1')).not.toBeInTheDocument();
  });
});
