import React, { useContext, useEffect, useState } from 'react';
import styled from 'styled-components';
import { fetchAlertsStart, fetchAlertsSuccess, fetchAlertsFailure, selectAlerts } from '../../../store/reducers/alertsReducer';
import { useAppDispatch, useAppSelector } from '../../../hooks/storeHooks';
import { selectTheme, Theme } from '../../../store/reducers/themeReducer';
import { useTranslation } from 'react-i18next';
import { useAlertsApi } from '../../../services/AlertsApi';
import { LocalStorage } from '../../../storage/local-storage';
import { ConfigContext } from '../../../context/ConfigProvider';

type Alert = {
  id: string;
  title: string;
  message: string;
  type: AlertType | string;
};

type AlertType = 'Information' | 'Warning' | 'Error' | 'Unknown';

const AlertsComponent = () => {
  const { t } = useTranslation<['common']>(['common']);
  const { networkId } = useContext(ConfigContext);

  const localStorageService = new LocalStorage();
  const alertsApi = useAlertsApi(networkId);

  const dispatch = useAppDispatch();
  const alerts = useAppSelector(selectAlerts);

  const theme: Theme = useAppSelector(selectTheme);
  const [dismissedAlerts, setDismissedAlerts] = useState<string[]>([]);

  const handleDismiss = (alertId: string) => {
    const updatedDismissedAlerts = [...dismissedAlerts, alertId];
    setDismissedAlerts(updatedDismissedAlerts);
    localStorageService.setItemSync('dismissedAlerts', JSON.stringify(updatedDismissedAlerts));
  };

  const visibleAlerts = alerts.filter((alert) => !dismissedAlerts.includes(alert.id));

  useEffect(() => {
    const storedDismissedAlerts = localStorageService.getItemSync('dismissedAlerts');
    if (storedDismissedAlerts) {
      setDismissedAlerts(JSON.parse(storedDismissedAlerts));
    }
  }, []);

  useEffect(() => {
    const fetchAlerts = async () => {
      dispatch(fetchAlertsStart());
      try {
        const data = await alertsApi.fetchAllAlerts();
        dispatch(fetchAlertsSuccess(data));
      } catch (error: any) {
        dispatch(fetchAlertsFailure(error.message));
      }
    };

    fetchAlerts();
  }, [dispatch]);

  return alerts.length === 0 ? null : (
    <AlertsContainer>
      {visibleAlerts.map(
        (alert: Alert): JSX.Element => (
          <AlertBanner type={alert.type as AlertType} key={alert.id} data-testid="alert-banner">
            <AlertContent>
              <h4>{alert.title}</h4>
              <p>{alert.message}</p>
            </AlertContent>
            <DismissButton theme={theme} onClick={() => handleDismiss(alert.id)}>
              {t('common:dismiss')}
            </DismissButton>
          </AlertBanner>
        ),
      )}
    </AlertsContainer>
  );
};

const COLOURS = {
  Information: '#D9E5FF',
  Warning: '#FFF5D9',
  Error: '#FFD9D9',
  Default: '#333333',
};

const AlertBanner = styled.div<{ type: AlertType }>`
  width: 100%;
  padding: 12px;
  background-color: ${(props) => {
    switch (props.type) {
      case 'Information':
        return COLOURS.Information;
      case 'Warning':
        return COLOURS.Warning;
      case 'Error':
        return COLOURS.Error;
      default:
        return COLOURS.Default;
    }
  }};
  display: flex;
  flex-direction: column;
  align-items: start;
`;

const AlertsContainer = styled.div`
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
`;

const AlertContent = styled.div`
  flex-grow: 1;
  text-align: left;
`;
const DismissButton = styled.button`
  background-color: transparent;
  align-self: flex-end;
  border: none;
  cursor: pointer;
  padding: 4px 12px;
  font-size: 14px;
  font-weight: bold;
  color: black;
  border: 1px solid black;
  &:hover {
    text-decoration: black;
  }
  &:focus {
    outline: none;
  }
`;

export default AlertsComponent;
