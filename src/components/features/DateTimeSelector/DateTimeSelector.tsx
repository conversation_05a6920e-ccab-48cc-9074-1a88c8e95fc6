import React, { useState } from 'react';
import { calendarNumberOutline, chevronUpOutline, chevronDownOutline } from 'ionicons/icons';
import styled from 'styled-components';
import { OnDemandSettingsSolutionDates } from '@liftango/liftango-client';
import { useWindowWidth } from '@react-hook/window-size';
import Popover from '../../elements/Popover/Popover';
import DateTimeSelectorPopup from './DateTimeSelectorPopup';
import { DropdownOptions } from '../../components/Dropdown/DropdownOption';
import { TimeType } from '../../../machines/Kiosk/kioskBooking';
import { useTranslation } from 'react-i18next';
import { BREAKPOINT_MD, BREAKPOINT_SM } from '../../../constants/breakpoints';
import PopupButton from '../../components/PopupButton/PopupButton';
import { HEIGHT_OF_THE_TOP_PART_OF_THE_SCREEN_MOBILE } from '../../components/AutoComplete/AddressAutoCompleteInput';
import { ABOVE_MAP_Z_INDEX } from '../../../constants/positioning';
import useQueryParam from '../../../hooks/useQueryParam';

export type ActiveTimeType = TimeType | 'leaveNow';

export type DateTimeSelectorProps = {
  selectedDateTime: string;
  timeType: TimeType;
  onSubmit: (selectedDateTime: Date, timeType: TimeType) => void;
  solutionDates: OnDemandSettingsSolutionDates[];
  defaultOpen: boolean;
  label?: string;
  routePlannerHeight?: number;
  showInModal?: boolean;
  className?: string;
};

const DateTimeSelector: React.FC<DateTimeSelectorProps> = (props: DateTimeSelectorProps) => {
  const { t } = useTranslation<['booking']>(['booking']);
  const [isToggled, setToggled] = useState<boolean>(props.defaultOpen); // closed by default
  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;
  const [activeTimeType, setActiveTimeType] = useState<ActiveTimeType>('leaveNow');

  const topQueryParam: string = useQueryParam('top');
  const paramMarginTop: number = topQueryParam ? Number(topQueryParam) : 0;

  const onSubmit = (selectedDateTime: Date, timeType: TimeType): void => {
    props.onSubmit(selectedDateTime, timeType);

    setToggled(false);
  };

  const onToggle = (prevState: boolean): boolean => !prevState;

  const timeTypeOptions: DropdownOptions<TimeType, string>[] = [
    {
      id: 'leaveAt',
      title: t('booking:leaveAt'),
    },
    {
      id: 'arriveBy',
      title: t('booking:arriveBy'),
    },
  ];

  const [selectedTimeType, setSelectedTimeType] = useState<DropdownOptions<TimeType, string> | undefined>(
    timeTypeOptions.find((option: DropdownOptions<TimeType, string>) => option.id === props.timeType),
  );

  return (
    <StyledDateTimeSelector className={props.className}>
      <StyledPopupButton
        onClick={() => setToggled(onToggle)}
        icon={calendarNumberOutline}
        // @ts-ignore activeTimeType can be leaveAt, arriveBy or leaveNow, and we have translations for all of those
        label={props.label?.toUpperCase() || t(`booking:${activeTimeType}`).toUpperCase()}
        style={{ fontSize: '24px', color: 'black' }}
        textSize={0.85}
        isActive={isToggled}
        extraIcon={isMobile ? undefined : isToggled ? chevronUpOutline : chevronDownOutline}
        extraIconSize={'small'}
        hasLabel={!!props.label}
      />

      {isMobile && !props.showInModal ? (
        isToggled ? (
          <StyledWrapper routePlannerHeight={props.routePlannerHeight || HEIGHT_OF_THE_TOP_PART_OF_THE_SCREEN_MOBILE + paramMarginTop}>
            <DateTimeSelectorPopup
              solutionDates={props.solutionDates}
              selectedDateTime={props.selectedDateTime}
              timeType={selectedTimeType?.id || props.timeType}
              activeTimeType={activeTimeType}
              setActiveTimeType={setActiveTimeType}
              onSubmit={onSubmit}
              timeTypeOptions={timeTypeOptions}
              setSelectedTimeType={setSelectedTimeType}
            />
          </StyledWrapper>
        ) : null
      ) : (
        <StyledPopover isOpen={isToggled} onWillDismiss={() => setToggled(false)}>
          <DateTimeSelectorPopup
            solutionDates={props.solutionDates}
            selectedDateTime={props.selectedDateTime}
            timeType={selectedTimeType?.id || props.timeType}
            activeTimeType={activeTimeType}
            setActiveTimeType={setActiveTimeType}
            onSubmit={onSubmit}
            timeTypeOptions={timeTypeOptions}
            setSelectedTimeType={setSelectedTimeType}
          />
        </StyledPopover>
      )}
    </StyledDateTimeSelector>
  );
};

const StyledWrapper = styled.div<{ routePlannerHeight: number }>`
  position: absolute;
  background: white;
  width: 100vw !important;
  top: ${({ routePlannerHeight }) => routePlannerHeight}px !important;
  height: calc(100% - ${({ routePlannerHeight }) => routePlannerHeight}px);
  overflow: auto;
  margin-left: -30px;
  z-index: ${ABOVE_MAP_Z_INDEX + 1};
`;

const StyledDateTimeSelector = styled.div`
  min-width: 127px;
  margin-right: 10px;
`;

const StyledPopupButton = styled(PopupButton)<{ isActive: boolean; hasLabel: boolean }>`
  border: 1px solid ${(props) => (props.isActive ? 'black' : 'white')};
  padding: 0 ${(props) => (props.hasLabel ? 10 : 15)}px;
  position: relative;
  max-width: 210px;

  @media all and (max-width: ${BREAKPOINT_SM}px) {
    padding: 0 ${(props) => (props.hasLabel ? 10 : 20)}px;
  }
`;

const StyledPopover = styled(Popover)`
  &::part(content) {
    background: white;
    width: 350px;

    @media all and (min-width: ${BREAKPOINT_MD}px) {
      left: 65px !important;
      top: 250px !important;
    }
  }
`;

export default DateTimeSelector;
