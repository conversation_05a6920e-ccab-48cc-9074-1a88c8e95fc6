import React, { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import Button from '../../elements/Button/Button';
import StripCalendar from '../../components/StripCalendar/StripCalendar';
import { useTranslation } from 'react-i18next';
import { DropdownOptions } from '../../components/Dropdown/DropdownOption';
import { TimeType } from '../../../machines/Kiosk/kioskBooking';
import { useAppSelector } from '../../../hooks/storeHooks';
import { selectNetworkEnforced } from '../../../store/reducers/networkReducer';
import { addMinutes, format, isSameDay } from 'date-fns';
import ErrorMessageBlock from '../../components/MessageBlock/ErrorMessageBlock';
import { TIME_FORMAT } from '../../../constants/formatting';
import TimeWheel from '../../components/TimeWheel/TimeWheel';
import Text from '../../elements/Text/Text';
import { OnDemandSettingsSolutionDates } from '@liftango/liftango-client';
import ToggleButton from '../../components/ToggleButton/ToggleButton';
import { ActiveTimeType } from './DateTimeSelector';
import { selectApplicationMode } from '../../../store/reducers/applicationReducer';

export type DateTimeSelectorPopupProps = {
  selectedDateTime: string;
  timeType: TimeType;
  activeTimeType: ActiveTimeType;
  setActiveTimeType: (timeType: ActiveTimeType) => void;
  onSubmit: (selectedDateTime: Date, timeType: TimeType) => void;
  timeTypeOptions: DropdownOptions<TimeType, string>[];
  solutionDates: OnDemandSettingsSolutionDates[];
  setSelectedTimeType: Dispatch<SetStateAction<DropdownOptions<TimeType, string> | undefined>>;
  className?: string;
};

const DateTimeSelectorPopup: React.FC<DateTimeSelectorPopupProps> = (props: DateTimeSelectorPopupProps) => {
  const { t } = useTranslation<['common', 'booking']>(['common', 'booking']);
  const network = useAppSelector(selectNetworkEnforced);
  const application = useAppSelector(selectApplicationMode);

  const [error, setError] = useState<string>('');
  const [timeType, setSelectedTimeType] = useState<TimeType>(props.timeType);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date(props.selectedDateTime));
  const [currentHour, setCurrentHour] = useState<number>(() => {
    const currentHours: number = new Date(props.selectedDateTime).getHours();
    return currentHours >= 12 ? currentHours - 12 : currentHours;
  });
  const [currentMinute, setCurrentMinute] = useState<number>(new Date(props.selectedDateTime).getMinutes());
  const [timePeriod, setTimePeriod] = useState<'am' | 'pm'>(new Date(props.selectedDateTime).getHours() >= 12 ? 'pm' : 'am');

  const { dateRideRestriction, dateRideRestrictionMinutes } = network.settings;

  const { minTime, maxTime } = props.solutionDates.reduce<{ minTime: string; maxTime: string }>(
    (acc, cur: OnDemandSettingsSolutionDates) => {
      if (!acc.minTime || cur.start < acc.minTime) {
        acc.minTime = cur.start;
      }

      if (!acc.maxTime || cur.end > acc.maxTime) {
        acc.maxTime = cur.end;
      }

      return acc;
    },
    { minTime: '', maxTime: '' },
  );

  const enabledDates: string[] = props.solutionDates.map<string>((solutionDate) => solutionDate.start);

  useEffect(() => {
    if (error) {
      // clear the error when a new date is selected
      setError('');
    }
  }, [selectedDate, currentHour, currentMinute, timePeriod]);

  const handleSubmitDateTime = () => {
    const timeOfSubmission: Date = new Date();

    let selectedDateTime: Date = new Date(selectedDate);
    let selectededHour: number =
      timePeriod === 'pm' ? (currentHour === 12 ? currentHour : currentHour + 12) : currentHour === 12 ? currentHour + 12 : currentHour;
    let selectedMinute: number = currentMinute;

    if (props.activeTimeType === 'leaveNow') {
      // If 'Leave now' option is selected, use time of submission
      selectedDateTime = new Date(timeOfSubmission);
      selectededHour = selectedDateTime.getHours();
      selectedMinute = selectedDateTime.getMinutes();
    }

    selectedDateTime.setHours(selectededHour);
    selectedDateTime.setMinutes(selectedMinute);

    // check if the selected date is a valid one
    if (!enabledDates.some((date: string) => isSameDay(new Date(date), selectedDate))) {
      setError(t('booking:invalidDate'));
      // check against min / max times
    } else if ((!minTime || selectedDateTime > new Date(minTime)) && (!maxTime || selectedDateTime < new Date(maxTime))) {
      // bookings must be more than x minutes from now
      const minNow: Date =
        application === 'fixed_route' ? timeOfSubmission : addMinutes(new Date(timeOfSubmission), dateRideRestrictionMinutes);

      // check if booking on the current day, and earlier than now
      if (isSameDay(new Date(selectedDate), minNow) && selectedDateTime < minNow) {
        if (dateRideRestrictionMinutes === 0) {
          setError(t('booking:minTimeErrorNow'));
        } else {
          setError(t('booking:minTimeError', { minTime: format(minNow, TIME_FORMAT) }));
        }
      } else {
        props.onSubmit(selectedDateTime, timeType);
      }
    } else if (minTime && maxTime) {
      setError(
        t('booking:minMaxTimeError', {
          minTime: format(new Date(minTime), TIME_FORMAT),
          maxTime: format(new Date(maxTime), TIME_FORMAT),
        }),
      );
    } else if (minTime) {
      setError(t('booking:minTimeError', { minTime: format(new Date(minTime), TIME_FORMAT) }));
    } else if (maxTime) {
      setError(t('booking:maxTimeError', { maxTime: format(new Date(maxTime), TIME_FORMAT) }));
    }
  };

  const handleToggleButton = (timeType: TimeType | 'leaveNow') => {
    props.setActiveTimeType(timeType);
    if (timeType === 'leaveNow') {
      setSelectedTimeType('leaveAt');
      props.setSelectedTimeType(props.timeTypeOptions.find((option: DropdownOptions<TimeType, string>) => option.id === 'leaveAt'));
      resetTimeWheel();
    } else {
      setSelectedTimeType(timeType);
      props.setSelectedTimeType(props.timeTypeOptions.find((option: DropdownOptions<TimeType, string>) => option.id === timeType));
    }
  };

  const resetTimeWheel = () => {
    const now: Date = new Date();
    const hours: number = now.getHours();
    const currentHours: number = hours >= 12 ? hours - 12 : hours;
    const timePeriod: 'am' | 'pm' = hours >= 12 ? 'pm' : 'am';
    const currentMinutes: number = now.getMinutes();

    setSelectedDate(now);
    setCurrentHour(currentHours);
    setCurrentMinute(currentMinutes);
    setTimePeriod(timePeriod);
  };

  return (
    <StyledWrapper className={props.className}>
      <StyledDateTimesWrapper>
        <StripCalendar
          selectedDate={selectedDate}
          setSelectedDate={(date: Date) => {
            if (props.activeTimeType === 'leaveNow') {
              props.setActiveTimeType('leaveAt');
            }
            setSelectedDate(date);
          }}
          calendarDaysBehind={-2}
          calendarDaysAhead={dateRideRestriction}
          enabledDates={enabledDates}
        />

        <StyledDateTimePickerFlex>
          <ToggleButton
            onClick={() => handleToggleButton('leaveNow')}
            label={t('booking:leaveNow')}
            textSize={1}
            isActive={props.activeTimeType === 'leaveNow'}
          />
          <ToggleButton
            onClick={() => handleToggleButton('leaveAt')}
            label={t('booking:leaveAt')}
            textSize={1}
            isActive={props.activeTimeType === 'leaveAt'}
          />
          {application === 'kiosk' && (
            <ToggleButton
              onClick={() => handleToggleButton('arriveBy')}
              label={t('booking:arriveBy')}
              textSize={1}
              isActive={props.activeTimeType === 'arriveBy'}
            />
          )}
        </StyledDateTimePickerFlex>

        <StyledTimeWrapper>
          <TimeWheel
            allowOverscroll
            isAnimated
            start={1}
            end={12}
            value={currentHour === 0 ? 12 : currentHour}
            onChange={(value) => {
              if (props.activeTimeType === 'leaveNow') {
                props.setActiveTimeType('leaveAt');
              }
              setCurrentHour(value);
            }}
            step={1}
            min={1}
            max={12}
            hasError={!!error}
          />

          <Text weight={600} size={1.5}>
            :
          </Text>

          <TimeWheel
            allowOverscroll
            isAnimated
            leadingZero
            start={0}
            end={59}
            value={currentMinute}
            onChange={(value) => {
              if (props.activeTimeType === 'leaveNow') {
                props.setActiveTimeType('leaveAt');
              }
              setCurrentMinute(value);
            }}
            step={5}
            min={0}
            max={59}
            hasError={!!error}
          />

          <StyledAmPmContainer>
            <StyledAmPmItem isActive={timePeriod === 'am'} weight={700} onClick={() => setTimePeriod('am')}>
              {t('common:AM')}
            </StyledAmPmItem>

            <StyledAmPmDivider />

            <StyledAmPmItem isActive={timePeriod === 'pm'} weight={700} onClick={() => setTimePeriod('pm')}>
              {t('common:PM')}
            </StyledAmPmItem>
          </StyledAmPmContainer>
        </StyledTimeWrapper>

        {error ? <StyledErrorMessage message={error} /> : null}
      </StyledDateTimesWrapper>

      <StyledButton onClick={handleSubmitDateTime}>{t('common:Confirm')}</StyledButton>
    </StyledWrapper>
  );
};

const StyledButton = styled(Button)`
  color: blue;

  ion-button {
    color: white;
    width: 100%;
  }
`;

const StyledWrapper = styled.div`
  justify-content: center;
  padding: 15px;
  overflow-y: hidden;
`;

const StyledTimeWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--top-text-color, #8c8c8c);
  margin-top: 5px;

  .MuiPickersToolbar-toolbar {
    background-color: #7dc8c6;
  }
`;

const StyledDateTimesWrapper = styled.div``;

const StyledDateTimePickerFlex = styled.div`
  display: flex;
  justify-content: space-evenly;
`;

const StyledErrorMessage = styled(ErrorMessageBlock)`
  width: 250px;
`;

const StyledAmPmContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: #efefef;
  border-radius: 10px;
  padding: 5px;
`;

const StyledAmPmItem = styled(Text)<{ isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border-radius: 10px;
  padding: 10px;
  cursor: pointer;

  color: ${(props) => (props.isActive ? '#3A3A48' : '#C6C6C7')};

  &:hover {
    color: #3a3a48;
    background-color: #e3e3e3;
  }
`;

const StyledAmPmDivider = styled.div`
  height: 1px;
  width: 70%;
  background-color: #c6c6c7;
  margin-top: 5px;
`;

export default DateTimeSelectorPopup;
