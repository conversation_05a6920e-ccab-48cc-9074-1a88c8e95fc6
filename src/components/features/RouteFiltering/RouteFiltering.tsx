import React, { useEffect, useMemo, useState } from 'react';
import { useWindowWidth } from '@react-hook/window-size';
import styled, { keyframes, Keyframes } from 'styled-components';
import { BREAKPOINT_MD } from '../../../constants/breakpoints';
import { selectTheme, Theme } from '../../../store/reducers/themeReducer';
import { MAP_DARKENING_OVERLAY_COLOUR } from '../../../constants/map';
import Text from '../../elements/Text/Text';
import useQueryParam from '../../../hooks/useQueryParam';
import { useTranslation } from 'react-i18next';
import { FixedRoute } from '@liftango/ops-client';
import { IonIcon } from '@ionic/react';
import { closeOutline } from 'ionicons/icons';
import ToggleSwitch from '../../elements/ToggleSwitch/ToggleSwitch';
import { hideRoute, selectRoutes, selectHiddenRoutes, unhideRoute } from '../../../store/reducers/routesReducer';
import { useAppDispatch, useAppSelector } from '../../../hooks/storeHooks';
import { LocalStorage } from '../../../storage/local-storage';
import { Persistence } from '../../../storage/persistence';
import Searchbar from '../../elements/SearchBar/SearchBar';

const NEW_LINE_CHARACTER: string = '\n';

type RouteFilteringProps = {
  hideRouteFilter: () => void;
};

const RouteFiltering: React.FC<RouteFilteringProps> = (props: RouteFilteringProps) => {
  const theme: Theme = useAppSelector(selectTheme);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  const { t } = useTranslation<['home']>(['home']);
  const windowWidth: number = useWindowWidth();
  const dispatch = useAppDispatch();
  const persistence: Persistence = useMemo(() => new LocalStorage(), []);
  const routes: FixedRoute.ServiceRoutesAndStopsPayload[] = useAppSelector(selectRoutes);
  const hiddenRoutes: string[] = useAppSelector(selectHiddenRoutes);
  const [filteredRoutes, setFilteredRoutes] = useState<FixedRoute.ServiceRoutesAndStopsPayload[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');

  const isMobile: boolean = windowWidth < BREAKPOINT_MD;
  const title: string = useQueryParam('title') || t('home:Service');
  const defaultDescription: string = useQueryParam('description') || t('home:vehiclesRunningOnTheseRoutes');
  const description: string = routes.length ? decodeURI(defaultDescription) : t('home:noServicesAvailableToday');

  // save changes to hide route toggles
  useEffect(() => {
    persistence.setItem('hiddenRoutes', JSON.stringify(hiddenRoutes));
  }, [persistence, hiddenRoutes]);

  // apply route search by string
  useEffect(() => {
    setFilteredRoutes(routes.filter((x) => x.label.toLocaleLowerCase().includes(searchValue.toLocaleLowerCase())));
  }, [routes, searchValue]);

  useEffect(() => {
    setSelectAll(hiddenRoutes.length === 0);
  }, [hiddenRoutes, routes]);

  const handleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);

    if (newSelectAll) {
      routes.forEach((route) => {
        dispatch(unhideRoute(route));
      });
    } else {
      routes.forEach((route) => dispatch(hideRoute(route.id)));
    }
  };

  const enabledRoutesCount: number = routes.length - (hiddenRoutes?.length ?? 0);

  const cardContent: JSX.Element = (
    <StyledCardContentBody>
      <>
        <StyledCardHeader>
          <StyledCardHeaderTop>
            <StyledTitle weight={700} size={1.25}>
              {title}
            </StyledTitle>
            <StyledCloseButton isMobile={isMobile} icon={closeOutline} size={'small'} onClick={props.hideRouteFilter} />
          </StyledCardHeaderTop>
          <StyledCardHeaderDescription>
            {description.split(NEW_LINE_CHARACTER).map<JSX.Element>((textLine: string, index: number) => {
              const key: string = `route-filtering-description-${index}`;

              return textLine ? (
                <Text key={key} size={0.9}>
                  {textLine}
                </Text>
              ) : (
                <StyleLineBreak key={key} />
              );
            })}
          </StyledCardHeaderDescription>
        </StyledCardHeader>
        <StyledSearchBar
          inputmode="text"
          autocorrect="off"
          placeholder={t('home:routeFilteringSearchRoute')}
          value={searchValue}
          debounce={250}
          onIonChange={(e) => setSearchValue(e.detail.value || '')}
        />
      </>
      <StyledList>
        <StyledListItem>
          <Text weight={600} size={0.9}>
            {selectAll
              ? `${t('home:toggleSwitchLabelDeselect')} (${enabledRoutesCount})`
              : `${t('home:toggleSwitchLabelSelect')} (${filteredRoutes?.length || 0})`}
          </Text>
          <ToggleSwitch
            label={selectAll ? 'X' : enabledRoutesCount.toString()}
            colour={theme.colors.primary}
            onChange={handleSelectAll}
            isActive={selectAll}
          />
        </StyledListItem>
      </StyledList>

      {filteredRoutes.length > 0 ? (
        <StyledList>
          {filteredRoutes.map((route: FixedRoute.ServiceRoutesAndStopsPayload) => (
            <StyledListItem key={`route-filtering-item-${route.id}`}>
              <Text weight={600} size={0.9}>
                {route.label}
              </Text>
              <ToggleSwitch
                label={route.label.charAt(0).toUpperCase()}
                colour={route.colour}
                onChange={(selected: boolean) => dispatch(selected ? unhideRoute(route) : hideRoute(route.id))}
                isActive={!hiddenRoutes.includes(route.id) && !hiddenRoutes.includes(route.versionId)}
              />
            </StyledListItem>
          ))}
        </StyledList>
      ) : (
        <StyledEmptyList>
          <Text weight={700} size={0.9}>
            {t('home:routeFilteringEmptyTitle')}
          </Text>
          <StyledEmptyListDescription size={0.9}>{t('home:routeFilteringEmptyDescription')}</StyledEmptyListDescription>
        </StyledEmptyList>
      )}
    </StyledCardContentBody>
  );

  return isMobile ? (
    <StyledMobileOverviewWrapper backgroundColor={MAP_DARKENING_OVERLAY_COLOUR}>
      <StyledAnimatedCard>{cardContent}</StyledAnimatedCard>
    </StyledMobileOverviewWrapper>
  ) : (
    <StyledStaticCard>{cardContent}</StyledStaticCard>
  );
};

const onLoadAnimation = (): Keyframes => keyframes`
  0% {
    transform: translateY(calc(100%));
  }
  100% {
    transform: translateY(0);
  }
`;

const StyledMobileOverviewWrapper = styled.div<{ backgroundColor: string }>`
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: ${({ backgroundColor }) => backgroundColor};
  display: flex;
  justify-content: flex-end;
  flex-direction: column;
`;

const StyledAnimatedCard = styled.div`
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  background-color: white;
  box-shadow: 0 3px 10px rgb(0 0 0 / 20%);
  min-height: 80%;
  transition: all 150ms linear;
  overflow: hidden;
  animation: ${onLoadAnimation()} 300ms linear;
`;

const StyledStaticCard = styled.div`
  border-radius: 20px;
  background-color: white;
  box-shadow: 0 3px 10px rgb(0 0 0 / 20%);
  padding: 10px;
  position: absolute;
  z-index: 3;
  top: 40px;
  right: 40px;
  min-width: 350px;
`;

const StyledCardContentBody = styled.div`
  position: relative;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const StyledCardHeader = styled.div`
  padding: 30px 30px 0px 30px;
`;

const StyledCardHeaderTop = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
`;

const StyledCardHeaderDescription = styled.div``;

const StyleLineBreak = styled.br``;

const StyledTitle = styled(Text)`
  flex: 1;
`;

const StyledCloseButton = styled(IonIcon)<{ isMobile: boolean }>`
  width: ${({ isMobile }) => (isMobile ? 30 : 24)}px;
  height: ${({ isMobile }) => (isMobile ? 30 : 24)}px;

  border-radius: 36px;
  background-color: #f2f2f2;
  padding: 0.1em 0.1em;
  cursor: pointer;
`;

const StyledSearchBar = styled(Searchbar)`
  margin-top: 15px;
  padding: 0px 30px;
  ion-searchbar {
    --background: #eeeeee;
    padding-left: 0;
    padding-right: 0;
  }
`;

const StyledList = styled.div`
  margin-top: 24px;
  padding: 0px 30px;
  overflow-y: auto;
  min-height: 56px;
`;

const StyledListItem = styled.div`
  padding: 10px 0px;
  border-top: 1px solid #f2f2f2;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const StyledEmptyList = styled.div`
  margin-top: 24px;
  padding: 0px 30px;
`;

const StyledEmptyListDescription = styled(Text)`
  margin-top: 10px;
`;

export default RouteFiltering;
