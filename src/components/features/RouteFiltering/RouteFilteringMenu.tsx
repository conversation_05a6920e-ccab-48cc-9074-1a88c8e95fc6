import React from 'react';
import { FixedRoute } from '@liftango/ops-client';
import styled from 'styled-components';
import { selectTheme, Theme } from '../../../store/reducers/themeReducer';
import { useAppSelector } from '../../../hooks/storeHooks';
import useRunningShiftSolutions from '../../../hooks/useShiftSolutionLocation';
import { BLURRED_MAP_DETAIL_COLOUR, MAP_DARKENING_OVERLAY_COLOUR } from '../../../constants/map';
import { PRODUCT_TOUR_DONE_KEY } from '../Map/ProductTour/ProductTour';
import { useMapContext } from '../../../context/MapProvider';

type RouteFilteringMenuProps = {
  routeShapes: FixedRoute.ServiceRouteShape[];
  disabled: boolean;
  showRouteFilter: () => void;
  className?: string;
};

type Colours = string[];

const RouteFilteringMenu = (props: RouteFilteringMenuProps) => {
  const theme: Theme = useAppSelector(selectTheme);
  const { dispatch: dispatchMapAction } = useMapContext();
  const [_, __, checkIsRouteActive] = useRunningShiftSolutions();

  const getRouteColourByActivity = (routeVersionId: string, routeColour: string): string => {
    return checkIsRouteActive(routeVersionId) ? routeColour : BLURRED_MAP_DETAIL_COLOUR;
  };

  const createColouredCirclesData = (): Colours[] => {
    // We always want to have 4 circle inside the info button, two arrays of two colours
    // If there are less then 4 route shapes, we'll just use a default colour for some of the circles
    const rows: Colours[] = [[], []];
    const [firstRoute, secondRoute, thirdRoute, fourthRoute] = props.routeShapes;

    // Default circle colour will be the first route colour (if the first route is defined) or theme secondary colour
    const defaultColour: string = firstRoute
      ? getRouteColourByActivity(firstRoute.routeVersionId, firstRoute.colour)
      : theme.colors.secondary;

    // We'll fill the rows with default colours
    rows[0].push(defaultColour, defaultColour);
    rows[1].push(defaultColour, defaultColour);

    // If the second route is not defined, it means that either no routes are defined or only the first one is,
    // so we can safely return the arrays filled with default colours
    if (!secondRoute) {
      return rows;
    }

    // If second route is defined, we'll update some colours
    if (secondRoute) {
      rows[0][1] = getRouteColourByActivity(secondRoute.routeVersionId, secondRoute.colour);
      rows[1][0] = getRouteColourByActivity(secondRoute.routeVersionId, secondRoute.colour);
    }

    // If third route is defined,  we'll update some colours
    if (thirdRoute) {
      rows[0][1] = getRouteColourByActivity(thirdRoute.routeVersionId, thirdRoute.colour);
    }

    // If fourth route is defined,  we'll update some colours
    if (fourthRoute) {
      rows[0][1] = getRouteColourByActivity(fourthRoute.routeVersionId, fourthRoute.colour);
      rows[1][1] = getRouteColourByActivity(thirdRoute.routeVersionId, thirdRoute.colour);
    }

    return rows;
  };

  const onClick = () => {
    if (window.localStorage.getItem(PRODUCT_TOUR_DONE_KEY) !== 'true') {
      dispatchMapAction({
        type: 'OPEN_PRODUCT_TOUR',
      });
    } else {
      props.showRouteFilter();
    }
  };

  return (
    <>
      <StyledRouteMenuContainer className={props.className} onClick={onClick} data-testid="route-filtering-menu-icon">
        <StyledRouteMenuIcon>
          {createColouredCirclesData().map((colours: Colours, index: number) => (
            <StyledRouteMenuIconRow key={`route-menu-icon-row-${index}`}>
              {colours.map((colour: string, index: number) => (
                <StyledRouteMenuIconIndicator key={`route-menu-icon-indicator-${index}`} colour={colour} />
              ))}
            </StyledRouteMenuIconRow>
          ))}
        </StyledRouteMenuIcon>
        {props.disabled && <StyledRouteMenuIconOverlay colour={MAP_DARKENING_OVERLAY_COLOUR} />}
      </StyledRouteMenuContainer>
    </>
  );
};

const StyledRouteMenuContainer = styled.div`
  top: 0;
  left: 0;
  position: relative;
`;

const StyledRouteMenuIcon = styled.div`
  z-index: 3;

  height: 36px;
  width: 36px;
  border-radius: 36px;
  background-color: white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);

  display: flex;
  align-items: center;
  justify-content: center;
`;

const StyledRouteMenuIconOverlay = styled.div<{ colour: string }>`
  position: absolute;
  top: 0;
  left: 0;
  z-index: 4;
  background-color: ${({ colour }) => colour};
  border-radius: 36px;
  height: 36px;
  width: 36px;
`;

const StyledRouteMenuIconRow = styled.div``;

const StyledRouteMenuIconIndicator = styled.div<{ colour: string }>`
  height: 6px;
  width: 6px;
  border-radius: 6px;
  background-color: ${({ colour }) => colour};
  margin: 2px;
`;

export default RouteFilteringMenu;
