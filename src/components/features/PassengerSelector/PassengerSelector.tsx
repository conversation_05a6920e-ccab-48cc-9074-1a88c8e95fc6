import React, { useState, useMemo } from 'react';
import { personOutline } from 'ionicons/icons';
import styled from 'styled-components';
import Popover from '../../elements/Popover/Popover';
import PassengerSelectorPopup from './PassengerSelectorPopup';
import { Companion } from '../../../machines/Kiosk/kioskBooking';
import { BREAKPOINT_MD } from '../../../constants/breakpoints';
import PopupButton from '../../components/PopupButton/PopupButton';
import { selectServices } from '../../../store/reducers/serviceReducer';
import { useAppSelector } from '../../../hooks/storeHooks';
import { OnDemandServiceSettingsPayload } from '@liftango/liftango-client';
import { getFlattenedServiceSettings, ServiceSettingPayload } from '@liftango/service-toolkit';

const DEFAULT_MAX_COMPANIONS = 7;

export type PassengerSelectorProps = {
  companions: Companion[];
  onSubmit: (passengers: Companion[]) => void;
  className?: string;
};

const PassengerSelector: React.FC<PassengerSelectorProps> = (props: PassengerSelectorProps) => {
  const { className } = props;
  const [isToggled, setToggled] = useState<boolean>(false);
  const [companions, setCompanions] = useState<Companion[]>(props.companions);

  const serviceSettings = useAppSelector(selectServices);
  const serviceSetting: ServiceSettingPayload | null = useMemo<ServiceSettingPayload | null>(() => {
    return getFlattenedServiceSettings(serviceSettings);
  }, [serviceSettings]);

  // Type Guard
  const isOnDemandServiceSettingsPayload = (
    serviceSetting: ServiceSettingPayload | null,
  ): serviceSetting is OnDemandServiceSettingsPayload => {
    return (serviceSetting as OnDemandServiceSettingsPayload).maxCompanions !== undefined;
  };
  const maxCompanions: number = isOnDemandServiceSettingsPayload(serviceSetting)
    ? serviceSetting?.maxCompanions ?? DEFAULT_MAX_COMPANIONS
    : DEFAULT_MAX_COMPANIONS;

  const onSubmit = (): void => {
    props.onSubmit(companions);

    setToggled(false);
  };

  const onToggle = (prevState: boolean): boolean => !prevState;

  return (
    <StyledPassengerSelector className={className}>
      <StyledPopover isOpen={isToggled}>
        <PassengerSelectorPopup
          companions={companions}
          setCompanions={setCompanions}
          onSubmit={onSubmit}
          minPassengers={1}
          maxPassengers={maxCompanions}
        />
      </StyledPopover>

      <PopupButton onClick={() => setToggled(onToggle)} icon={personOutline} label={String(companions.length)} />
    </StyledPassengerSelector>
  );
};

const StyledPassengerSelector = styled.div`
  position: relative;
`;

const StyledPopover = styled(Popover)`
  .popover-wrapper .popover-content {
    background: white;
    --backdrop-opacity: 0.3;
    width: 282px;
    border-radius: 24px;

    @media all and (min-width: ${BREAKPOINT_MD}px) {
      left: 105px !important;
      top: 300px !important;
    }
  }
`;

export default PassengerSelector;
