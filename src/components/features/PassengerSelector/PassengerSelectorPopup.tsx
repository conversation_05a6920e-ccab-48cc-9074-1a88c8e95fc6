import React, { Dispatch, ReactNode, SetStateAction } from 'react';
import { personOutline, removeCircleOutline } from 'ionicons/icons';
import styled from 'styled-components';
import Button from '../../elements/Button/Button';
import CountSelector from '../../elements/CountSelector/CountSelector';
import PassengerCard from './PassengerCard';
import { Companion } from '../../../machines/Kiosk/kioskBooking';
import { useTranslation } from 'react-i18next';

export type PassengerSelectorPopupProps = {
  companions: Companion[];
  setCompanions: Dispatch<SetStateAction<Companion[]>>;
  onSubmit: () => void;
  minPassengers: number;
  maxPassengers: number;
  className?: string;
};

const PassengerSelectorPopup: React.FC<PassengerSelectorPopupProps> = (props: PassengerSelectorPopupProps) => {
  const { t } = useTranslation<['common', 'booking']>(['common', 'booking']);
  const { className, companions, minPassengers, maxPassengers, onSubmit, setCompanions } = props;

  const handleCountChange = (value: number) => {
    setCompanions((prevState: Companion[]) => {
      const countedCompanions: Companion[] = [];

      for (let i = 0; i < value; i++) {
        if (prevState[i]) {
          countedCompanions.push(prevState[i]);
        } else {
          countedCompanions.push({
            id: i === 0 ? 'Me' : `Passenger ${i}`,
            seatType: 'standard',
            luggage: [],
            concessionType: null,
          });
        }
      }

      return countedCompanions;
    });
  };

  const handleToggleLuggage = (index: number) => {
    setCompanions((prevState: Companion[]) =>
      prevState.map<Companion>((companion: Companion, i: number) => {
        if (index === i) {
          return {
            ...companion,
            luggage:
              companion.luggage.length === 0
                ? [
                    {
                      type: 'luggage',
                      label: 'Luggage',
                      description: '',
                      count: 1,
                    },
                  ]
                : [],
          };
        }

        return companion;
      }),
    );
  };

  const handleRemovePassenger = (index: number) => {
    setCompanions((prevState: Companion[]) =>
      prevState.reduce<Companion[]>((acc: Companion[], companion: Companion, i: number) => {
        if (index !== i) {
          acc.push(companion);
        }

        return acc;
      }, []),
    );
  };

  return (
    <StyledWrapper className={className}>
      <StyledTitle>{t('booking:Passengers')}</StyledTitle>
      <StyledCountSelector count={companions.length} onCountChange={handleCountChange} minCount={minPassengers} maxCount={maxPassengers} />

      <StyledPassengersWrapper>
        {companions.map<ReactNode>((companion: Companion, index: number) => (
          <PassengerCard
            key={companion.id}
            title={companion.id}
            luggage={companion.luggage.length}
            icon={index === 0 ? personOutline : removeCircleOutline}
            iconColor={index === 0 ? 'dark' : 'danger'}
            handleRemovePassenger={index === 0 ? undefined : () => handleRemovePassenger(index)}
            handleToggleLuggage={() => handleToggleLuggage(index)}
          />
        ))}
      </StyledPassengersWrapper>

      <StyledButton onClick={onSubmit}>{t('common:Confirm')}</StyledButton>
    </StyledWrapper>
  );
};

const StyledButton = styled(Button)`
  color: blue;

  ion-button {
    color: white;
    width: 100%;
  }
`;

const StyledTitle = styled.p`
  text-align: center;
  font: normal normal normal 14px/17px Rubik;
  letter-spacing: 0;
`;

const StyledCountSelector = styled(CountSelector)`
  width: 100%;
  justify-content: center;
`;

const StyledWrapper = styled.div`
  justify-content: center;
  padding: 15px;
  overflow-y: hidden;
  max-height: 600px;
`;

const StyledPassengersWrapper = styled.div`
  > div {
    margin-bottom: 20px;
  }
  max-height: 400px;
  overflow-y: auto;
`;

export default PassengerSelectorPopup;
