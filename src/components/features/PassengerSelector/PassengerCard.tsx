import { IonIcon, IonTitle } from '@ionic/react';
import { chevronDown, chevronUp } from 'ionicons/icons';
import React, { useState } from 'react';
import styled from 'styled-components';
import Toggle from '../../elements/Toggle/Toggle';
import { useTranslation } from 'react-i18next';
import Text from '../../elements/Text/Text';
import { useAppSelector } from '../../../hooks/storeHooks';
import { selectApplicationFeatureToggles } from '../../../store/reducers/applicationReducer';

export type PassengerCardProp = {
  title: string;
  icon: string;
  iconColor: string;
  luggage: number;
  handleToggleLuggage: () => void;
  handleRemovePassenger?: () => void;
  className?: string;
};

const PassengerCard: React.FC<PassengerCardProp> = (props: PassengerCardProp) => {
  const { t } = useTranslation<['booking']>(['booking']);
  const { icon, title, iconColor, className } = props;

  const [isOpen, toggleOpen] = useState<boolean>(false);

  const featureToggles = useAppSelector(selectApplicationFeatureToggles);

  return (
    <StyledWrapper className={className} isToggled={isOpen}>
      <StyledRowWrapper>
        <StyledPassengerContent>
          <StyledIcon
            icon={icon}
            size="large"
            color={iconColor}
            onClick={props.handleRemovePassenger}
            disabled={!props.handleRemovePassenger}
          />
          <StyledPassengerLabel weight={500}>{title}</StyledPassengerLabel>
        </StyledPassengerContent>

        {featureToggles.enableLuggage && (
          <StyledIcon icon={isOpen ? chevronUp : chevronDown} onClick={() => toggleOpen((prevState: boolean): boolean => !prevState)} />
        )}
      </StyledRowWrapper>

      {isOpen && (
        <StyledExpandedRowWrapper>
          <Text weight={400}>{t('booking:Luggage')}</Text>
          <Toggle isToggled={props.luggage > 0} handleToggle={props.handleToggleLuggage} mode="ios" />
        </StyledExpandedRowWrapper>
      )}
    </StyledWrapper>
  );
};

const StyledFlexRow = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

const StyledPassengerContent = styled(StyledFlexRow)`
  justify-content: flex-start;
`;

const StyledRowWrapper = styled(StyledFlexRow)`
  ion-icon {
    font-size: 24px;
    --ionicon-stroke-width: 50px;
  }

  ion-toggle {
    height: 20px;
    --handle-width: 16px;
    width: 35px;
    color: var(--ion-color-success);
  }
`;

const StyledPassengerLabel = styled(Text)`
  margin-left: 10px;
`;

const StyledExpandedRowWrapper = styled(StyledFlexRow)`
  margin: 10px 0;
`;

const StyledIcon = styled(IonIcon)<{ disabled?: boolean }>`
  cursor: ${(props) => (props.disabled ? 'initial' : 'pointer')};
`;

const StyledWrapper = styled.div<{ isToggled: boolean }>`
  padding: 15px;
  border-radius: 20px;
  width: 100%;
  box-sizing: border-box;
  overflow-y: hidden;
  max-height: ${(props) => (props.isToggled ? '103px' : '62px')};
  transition: max-height 0.25s ease;
  background-color: rgb(var(--color-lime-green-rgb), 0.2);
`;

export default PassengerCard;
