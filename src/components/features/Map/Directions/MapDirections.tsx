import React, { useCallback, useMemo, useState } from 'react';
import { DirectionsRenderer, DirectionsService, OverlayView } from '@react-google-maps/api';
import styled from 'styled-components';
import { selectTheme, Theme } from '../../../../store/reducers/themeReducer';
import { useAppSelector } from '../../../../hooks/storeHooks';
import WarningIcon from '../../../elements/Icon/WarningIcon';
import { LONG_WALK_WARNING_THRESHOLD_IN_METERS } from '../../../../constants/map';
import Tooltip from '../../../elements/ToolTip/Tooltip';
import Text from '../../../elements/Text/Text';
import { useTranslation } from 'react-i18next';
import { getPixelPositionOffset } from '../Map';

type Location = {
  latitude: number;
  longitude: number;
};

type MapDirectionsProps = {
  places: Location[];
  renderDirections?: boolean;
  color?: string;
  solid?: boolean;
  region?: string;
  departureTime?: string;
  travelMode?: google.maps.TravelMode;
  directionsOptions?: google.maps.DirectionsRequest | null;
};

const MapDirections = ({
  places,
  color,
  departureTime = '',
  region = 'AU',
  travelMode = google.maps.TravelMode.DRIVING,
  directionsOptions = null,
  solid = false,
  renderDirections = true,
}: MapDirectionsProps) => {
  const theme: Theme = useAppSelector(selectTheme);
  const [directions, setDirections] = useState<google.maps.DirectionsResult | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState<google.maps.LatLng | null>(null);
  const [error, setError] = useState<boolean>(false);

  const { t } = useTranslation<['trip']>(['trip']);

  const directionsCallback = useCallback((result: google.maps.DirectionsResult, status: google.maps.DirectionsStatus) => {
    if (status === google.maps.DirectionsStatus.OK) {
      setDirections(result);

      if (travelMode === google.maps.TravelMode.WALKING) {
        const distance: number = result.routes[0].legs[0].distance.value;
        if (distance > LONG_WALK_WARNING_THRESHOLD_IN_METERS) {
          const path: google.maps.LatLng[] = result.routes[0].legs[0].steps.flatMap(({ path }) => path);
          const lat: number = path[Math.floor(path.length / 2)].lat();
          const lng: number = path[Math.floor(path.length / 2)].lng();
          setTooltipPosition(new google.maps.LatLng(lat, lng));
        } else {
          setTooltipPosition(null);
        }
      }

      setError(false);
    } else {
      setError(true);
    }
  }, []);

  const directionServiceRequest: google.maps.DirectionsRequest = useMemo<google.maps.DirectionsRequest>((): google.maps.DirectionsRequest => {
    const waypoints: google.maps.DirectionsWaypoint[] = places.map<google.maps.DirectionsWaypoint>((place: Location) => ({
      location: {
        location: {
          lat: place.latitude,
          lng: place.longitude,
        },
      },
      stopover: true,
    }));

    const origin = waypoints.shift()?.location;
    const destination = waypoints.pop()?.location;

    const now: Date = new Date();
    const departureDate: Date = new Date(departureTime);

    return {
      origin: origin,
      destination: destination,
      travelMode: travelMode,
      waypoints: waypoints,
      region: region.slice(-2).toUpperCase(),
      drivingOptions: {
        departureTime: departureDate < now ? now : departureDate,
      },
      ...directionsOptions,
    };
  }, [places, directionsOptions, travelMode, region, departureTime]);

  if (error || !directionServiceRequest.origin || !directionServiceRequest.destination) {
    return null;
  }

  const renderLongWalkTooltip = () => {
    return (
      <StyledLongWalkTooltip colour={'warning-contrast'} bubbleBorderRadius={15} bubblePadding={'8px 8px 8px 0'} withShadow>
        <StyledLongWalkTooltipContents>
          <WarningIcon iconSize={24} />
          <StyledLongWalkTooltipText size={0.8} weight={700} fontFamily={'body'} lineHeight={1.1} align={'left'}>
            {/* @ts-ignore TS wants explicit keys passed to t so it can check if translation exists */}
            {t(theme.longWalkTranslationKey ?? 'trip:longWalkWarningTooltipShort')}
          </StyledLongWalkTooltipText>
        </StyledLongWalkTooltipContents>
      </StyledLongWalkTooltip>
    );
  };

  return (
    <React.Fragment>
      <DirectionsService options={directionServiceRequest} callback={directionsCallback} />

      {directions && renderDirections && (
        <DirectionsRenderer
          options={{
            directions,
            preserveViewport: true,
            suppressMarkers: true,
            polylineOptions: {
              strokeColor: color || theme.colors.primary,
              strokeOpacity: solid ? 1 : 0,
              strokeWeight: 2,
              icons: solid
                ? undefined
                : [
                    {
                      icon: {
                        path: 'M 0,-1 0,1',
                        strokeOpacity: 1,
                        scale: 3,
                      },
                      offset: '0',
                      repeat: '20px',
                    },
                  ],
            },
          }}
        />
      )}
      {tooltipPosition && renderDirections && (
        <OverlayView
          mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
          position={tooltipPosition}
          getPixelPositionOffset={getPixelPositionOffset}
        >
          {renderLongWalkTooltip()}
        </OverlayView>
      )}
    </React.Fragment>
  );
};

const StyledLongWalkTooltip = styled(Tooltip)`
  width: 240px;
  position: relative;
  top: -5px;
  display: flex;
  flex-direction: column;
`;

const StyledLongWalkTooltipContents = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-around;
`;

const StyledLongWalkTooltipText = styled(Text)`
  max-width: 160px;
`;

export default React.memo(MapDirections);
