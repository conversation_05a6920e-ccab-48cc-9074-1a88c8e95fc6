import React, { useEffect, useMemo, useRef, useState } from 'react';
import styled, { css } from 'styled-components';
import { useWindowWidth } from '@react-hook/window-size';
import { useTranslation } from 'react-i18next';
import { BREAKPOINT_MD } from '../../../../constants/breakpoints';
import Text from '../../../elements/Text/Text';
import Button from '../../../../components/elements/Button/Button';
import { useMapContext } from '../../../../context/MapProvider';
import RouteIcon from '../../../../assets/routes.svg?react';
import MapWithPinIcon from '../../../../assets/map-with-pin.svg?react';
import SearchHeartIcon from '../../../../assets/search-heart.svg?react';
import { IonIcon, IonSlide, IonSlides } from '@ionic/react';
import { ProductTourPage } from '../mapReducer';
import { closeOutline } from 'ionicons/icons';
import { useAppSelector } from '../../../../hooks/storeHooks';
import { selectNetworkEnforced } from '../../../../store/reducers/networkReducer';

export const PRODUCT_TOUR_DONE_KEY = 'productTourDone';

type ProductTourProps = {
  routeFilterRef: React.RefObject<HTMLDivElement>;
  firstStopRef: React.RefObject<HTMLButtonElement>;
  routePlannerRef: React.RefObject<HTMLDivElement>;
};

type PageKey = {
  key: ProductTourPage;
  title: string;
  detail: string;
  mobileVideo: string;
  desktopVideo: string;
  icon: JSX.Element;
  arrow: 'left' | 'right';
  offsetTop: number;
  offsetLeft: number;
  anchorElementRef: React.RefObject<HTMLDivElement | HTMLButtonElement>;
};

type ArrowDirection = 'left' | 'right';
type Position = { top: number; left: number; arrow: ArrowDirection };
const ProductTour = (props: ProductTourProps) => {
  const network = useAppSelector(selectNetworkEnforced);
  const { dispatch: dispatchMapAction } = useMapContext();
  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;
  const { t, ready } = useTranslation<['home', 'common']>(['home', 'common']);

  const [currentPageIndex, setCurrentPageIndex] = useState<number>(0);
  const [currentPosition, setCurrentPosition] = useState<Position | null>(null);
  const [isLoadingVideo, setIsLoadingVideo] = useState<boolean>(true);

  if (!ready) {
    console.log('not ready');
  }

  const pageKeys: PageKey[] = [
    {
      key: 'route-filter',
      title: t('home:productTour.filtering.title'),
      detail: t('home:productTour.filtering.detail'),
      mobileVideo: network.settings.productTourVideoUrls.mobile.routeFiltering,
      desktopVideo: network.settings.productTourVideoUrls.desktop.routeFiltering,
      icon: <RouteIcon />,
      arrow: 'right',
      offsetTop: -16,
      offsetLeft: -430,
      anchorElementRef: props.routeFilterRef,
    },
    {
      key: 'shuttles-stops',
      title: t('home:productTour.shuttlesStops.title'),
      detail: t('home:productTour.shuttlesStops.detail'),
      mobileVideo: network.settings.productTourVideoUrls.mobile.stops,
      desktopVideo: network.settings.productTourVideoUrls.desktop.stops,
      icon: <MapWithPinIcon />,
      arrow: 'left',
      offsetTop: -20,
      offsetLeft: 55,
      anchorElementRef: props.firstStopRef,
    },
    {
      key: 'plan-journey',
      title: t('home:productTour.planJourney.title'),
      detail: t('home:productTour.planJourney.detail'),
      mobileVideo: network.settings.productTourVideoUrls.mobile.planJourney,
      desktopVideo: network.settings.productTourVideoUrls.desktop.planJourney,
      icon: <SearchHeartIcon />,
      arrow: 'left',
      offsetTop: 5,
      offsetLeft: 440,
      anchorElementRef: props.routePlannerRef,
    },
  ];

  const isLastPage: boolean = useMemo(() => currentPageIndex === pageKeys.length - 1, [currentPageIndex]);

  useEffect(() => {
    // not relevant to mobile
    if (isMobile) {
      return;
    }

    const page = pageKeys[currentPageIndex];
    const elemPosition = page.anchorElementRef.current?.getBoundingClientRect();
    if (!elemPosition?.top || !elemPosition?.left) {
      setCurrentPosition(null);
    } else {
      setCurrentPosition({ top: elemPosition.top + page.offsetTop, left: elemPosition.left + page.offsetLeft, arrow: page.arrow });
    }
  }, [currentPageIndex, isMobile]);

  // Keep tour page in map reducer in sync.
  useEffect(() => {
    dispatchMapAction({
      type: 'SET_PRODUCT_TOUR_PAGE',
      payload: pageKeys[currentPageIndex].key,
    });
  }, [currentPageIndex]);

  const slideElement: React.RefObject<HTMLIonSlidesElement> = useRef<HTMLIonSlidesElement>(null);

  const next = () => {
    if (isLastPage) {
      close();
    } else {
      slideElement.current?.getSwiper().then((swiper: any) => swiper.slideNext(isMobile ? undefined : 0));
    }
  };

  const close = () => {
    dispatchMapAction({
      type: 'SET_SELECTED_STOP',
      payload: null,
    });
    dispatchMapAction({
      type: 'SET_PRODUCT_TOUR_PAGE',
      payload: null,
    });
    window.localStorage.setItem(PRODUCT_TOUR_DONE_KEY, 'true');
  };

  const videoLoadingDone = () => {
    setIsLoadingVideo(false);
  };

  return (
    <>
      {/* Hiding window while video loads to prevent flickering. */}
      <StyledProductTour hide={isLoadingVideo} top={currentPosition?.top} left={currentPosition?.left} data-testid="product-tour">
        <StyledClose onClick={close}>
          <IonIcon icon={closeOutline} />
        </StyledClose>

        <StyledTriangle direction={currentPosition?.arrow} />

        <StyledIonSlides
          pager={true}
          ref={slideElement}
          onIonSlidePrevEnd={() => setCurrentPageIndex((prevVal) => prevVal - 1)}
          onIonSlideNextEnd={() => setCurrentPageIndex((prevVal) => prevVal + 1)}
        >
          {pageKeys.map((page: PageKey, index: number) => (
            <IonSlide key={page.key}>
              <StyledTourPage>
                <StyledSummary>
                  {page.icon}
                  <StyledHeading data-testid="title-product-tour">{page.title}</StyledHeading>
                </StyledSummary>

                <Text weight={500} lineHeight={1.2}>
                  {page.detail}
                </Text>
                <StyledVideoContainer>
                  {/* Loading 'done' regardless of success or not - controls tour window hiding so must resolve */}
                  <video onError={videoLoadingDone} onLoadedData={videoLoadingDone} autoPlay loop width="100%" playsInline>
                    <source src={isMobile ? page.mobileVideo : page.desktopVideo} />
                  </video>
                </StyledVideoContainer>
              </StyledTourPage>
            </IonSlide>
          ))}
        </StyledIonSlides>

        <StyledNextButton onClick={next} data-testid="button-product-tour">
          {t(isLastPage ? 'common:close' : 'common:next')}
        </StyledNextButton>
      </StyledProductTour>
    </>
  );
};

const StyledTriangle = styled.div<{ direction?: ArrowDirection }>`
  position: absolute;
  width: 0;
  height: 0;
  top: 18px;
  border-top: 16px solid transparent;
  border-bottom: 16px solid transparent;

  ${({ direction }) =>
    direction === 'left'
      ? css`
          border-right: 22px solid white;
          left: -17px;
        `
      : direction === 'right'
      ? css`
          border-left: 22px solid white;
          right: -17px;
        `
      : ''}
`;

const StyledProductTour = styled.div<{ hide: boolean; left?: number; top?: number }>`
  ${({ hide }) =>
    hide
      ? css`
          // DO NOT USE display:none or it will cause intermittent Ion-Slides bugs that will waste hours of your life tracking down.
          visibility: hidden;
        `
      : ''}
  position: fixed;
  z-index: 104;
  background-color: white;
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;

  // desktop
  @media all and (min-width: ${BREAKPOINT_MD}px) {
    width: 400px;
    left: ${({ left }) => (left ? `${left}px` : '50vh')};
    top: ${({ top }) => (top ? `${top}px` : 'calc(50vh - 200px)')};
    border-bottom-left-radius: 25px;
    border-bottom-right-radius: 25px;
  }

  // mobile
  @media all and (max-width: ${BREAKPOINT_MD}px) {
    width: 100%;
    bottom: 0;
  }
`;

const StyledClose = styled.button`
  position: absolute;
  top: 17px;
  right: 17px;
  background-color: #e3e3e3;
  border-radius: 20px;
  font-size: 25px;
  padding: 5px;
  line-height: 1px;
  z-index: 2;
`;

const StyledIonSlides = styled(IonSlides)`
  padding-bottom: 48px;
  height: fit-content;
  z-index: 0; // to ensure next/close button is clickable and on top of slides
  & .swiper-pagination {
    width: 50%;
  }
`;

const StyledNextButton = styled(Button)`
  width: 50%;
  float: right;
  margin: -42px 22px 12px 0;
`;

const StyledVideoContainer = styled.div`
  padding-top: 20px;
`;

const StyledSummary = styled.div`
  padding: 20px 8px;
  display: flex;
  align-items: center;
`;

const StyledHeading = styled(Text)`
  font-size: 22px;
  font-weight: bold;
  padding-left: 12px;
  padding-bottom: 5px;
`;

const StyledTourPage = styled.div`
  display: flex;
  flex-direction: column;
  padding: 0 26px;
`;

export default ProductTour;
