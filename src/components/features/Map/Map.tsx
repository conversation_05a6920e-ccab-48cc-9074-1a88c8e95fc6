import React, { Dispatch, ReactNode, SetStateAction, useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { GoogleMap, OverlayView, OverlayViewF, Polyline } from '@react-google-maps/api';
import { useWindowWidth } from '@react-hook/window-size';
import { IonIcon } from '@ionic/react';
import { OnDemandServiceHub } from '@liftango/liftango-client';
import { FixedRoute } from '@liftango/ops-client';
import { busOutline, carOutline, chevronForward } from 'ionicons/icons';
import styled from 'styled-components';
import { selectTheme, Theme } from '../../../store/reducers/themeReducer';
import { useAppSelector } from '../../../hooks/storeHooks';
import MapMarker from './Marker/MapMarker';
import Vehicle from './Marker/Vehicle';
import HubMarker from './Marker/HubMarker';
import MapDirections from './Directions/MapDirections';
import { SchedulePositionsDictionary } from '../../../hooks/useScheduleLocation';
import { BREAKPOINT_MD } from '../../../constants/breakpoints';
import useRunningShiftSolutions from '../../../hooks/useShiftSolutionLocation';
import FixedRouteVehicleDetails from './FixedRouteVehicleDetails/FixedRouteVehicleDetails';
import Text from '../../elements/Text/Text';
import Tooltip from '../../elements/ToolTip/Tooltip';
import FixedRouteStopDetails from './FixedRouteStopDetails/FixedRouteStopDetails';
import FixedRouteShiftSolutionDetails from './FixedRouteShiftSolutionDetails/FixedRouteShiftSolutionDetails';
import FixedRouteRoutesDetails from './FixedRouteRouteDetails/FixedRouteRoutesDetails';
import { useMapContext } from '../../../context/MapProvider';
import { useTranslation } from 'react-i18next';
import { BLURRED_MAP_DETAIL_COLOUR, MAP_DARKENING_OVERLAY_COLOUR } from '../../../constants/map';
import LocationMarker from './Marker/LocationMarker';
import { ABOVE_MAP_Z_INDEX } from '../../../constants/positioning';
import { LocalStorage } from '../../../storage/local-storage';
import { Persistence } from '../../../storage/persistence';
import useNetworkFeatures from '../../../hooks/useNetworkFeatures';
import { getPointHighlightEffect } from '../get-point-highlight-effect';

type ReactNativeWebView = {
  postMessage: (data: string) => void;
};

declare global {
  interface Window {
    ReactNativeWebView: ReactNativeWebView;
  }
}

const containerStyle: React.CSSProperties = {
  width: '100%',
  height: '100%',
};

export const getPixelPositionOffset = (offsetWidth: number, offsetHeight: number): { x: number; y: number } => ({
  x: -offsetWidth / 2,
  y: -offsetHeight,
});

const fitMapCardToPositions = (
  mapInstance: google.maps.Map,
  positions: { latitude: number; longitude: number }[],
  mapPadding?: MapPadding,
  onProgrammaticZoomStart?: () => void,
  onProgrammaticZoomEnd?: () => void,
  debugReason?: string,
  addDebugLog?: (message: string) => void,
) => {
  if (!positions || positions.length === 0 || !mapInstance || !window.google) {
    return;
  }

  const message = `🔴 AUTO-FIT CALLED! Reason: ${debugReason || 'unknown'}`;
  console.log(message);
  if (addDebugLog) {
    addDebugLog(message);
  }

  // Signal that we're starting a programmatic zoom
  if (onProgrammaticZoomStart) {
    onProgrammaticZoomStart();
  }

  // Transform the positions into a Google Map LatLng object
  const LatLngList: google.maps.LatLng[] = positions.map((position: any) => {
    return new window.google.maps.LatLng(position.latitude, position.longitude);
  });

  // Create a new viewport bound
  const bounds = new window.google.maps.LatLngBounds();

  for (let i: number = 0; i < LatLngList.length; i++) {
    // Increase the bounds to take this point
    bounds.extend(LatLngList[i]);
  }

  // Don't zoom in too far on only one marker, or when the markers are the same
  if (positions.length < 2 || bounds.getNorthEast()?.equals(bounds.getSouthWest())) {
    const extendPoint1 = new google.maps.LatLng(bounds.getNorthEast().lat() + 0.01, bounds.getNorthEast().lng() + 0.01);
    const extendPoint2 = new google.maps.LatLng(bounds.getNorthEast().lat() - 0.01, bounds.getNorthEast().lng() - 0.01);

    bounds.extend(extendPoint1);
    bounds.extend(extendPoint2);
  }

  //  Fit these bounds to the map
  mapInstance.fitBounds(bounds, mapPadding);

  // Signal that we're ending the programmatic zoom (with a delay to ensure all events fire)
  if (onProgrammaticZoomEnd) {
    setTimeout(onProgrammaticZoomEnd, 200);
  }
};

type WaypointPoint = {
  latitude: number;
  longitude: number;
};

type WaypointMarker = WaypointPoint & {
  icon?: string;
};

export type Waypoint = {
  id: string;
  origin: WaypointPoint;
  destination: WaypointPoint;
  marker?: WaypointMarker;
  color?: string;
};

export type JourneyPointType = 'arrival' | 'departure' | 'foot-departure' | 'foot-arrival' | 'bus-departure' | 'bus-arrival';

export type JourneyPoint<T extends JourneyPointType> = {
  type: T;
  icon: string;
  latitude: number;
  longitude: number;
  placeId: string;
  colour?: string;
  routeId?: string;
};

export type JourneyPointList =
  | []
  | [JourneyPoint<'departure'>, JourneyPoint<'arrival'>]
  | [JourneyPoint<'foot-departure'>, JourneyPoint<'bus-departure'>, JourneyPoint<'bus-arrival'>]
  | [JourneyPoint<'bus-departure'>, JourneyPoint<'bus-arrival'>, JourneyPoint<'foot-arrival'>]
  | [JourneyPoint<'foot-departure'>, JourneyPoint<'bus-departure'>, JourneyPoint<'bus-arrival'>, JourneyPoint<'foot-arrival'>];

export type MapPadding = {
  left: number;
  top: number;
  right: number;
  bottom: number;
};

type MapProps = {
  center?: {
    lat: number;
    lng: number;
  };
  initalCenter: {
    lat: number;
    lng: number;
  };
  regionCode: string;
  serviceHubs: OnDemandServiceHub[];
  stops: FixedRoute.ServiceRouteStop[];
  showTripPlanner: () => void;
  hideTripPlanner: () => void;
  routeShapes: FixedRoute.ServiceRouteShape[];
  isTripPlanerOpen?: boolean;
  onMapStopPressed?: (stop: FixedRoute.ServiceRouteStop) => void;
  renderDirections?: boolean;
  journeyPoints?: JourneyPointList[];
  mapPadding?: MapPadding;
  userPosition?: GeolocationPosition | null;
  schedulePositions?: SchedulePositionsDictionary;
  waypoints?: Waypoint[];
  zoom?: number;
  style?: React.CSSProperties;
  onMapItemSelected?: Dispatch<SetStateAction<boolean>>;
  firstStopRef?: React.ForwardedRef<HTMLButtonElement>;
};

// Enhanced gesture tracking types
type GestureState = 'idle' | 'single-touch' | 'multi-touch' | 'pinching' | 'dragging' | 'zooming';

interface TouchInfo {
  identifier: number;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
}

const useUserInteractionTracking = (isMobileWidth: boolean) => {
  const [isUserInteracting, setIsUserInteracting] = useState<boolean>(false);
  const [gestureState, setGestureState] = useState<GestureState>('idle');
  const userHasManuallyPositionedRef = useRef<boolean>(false);

  // Enhanced tracking refs
  const interactionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const activeTouchesRef = useRef<Map<number, TouchInfo>>(new window.Map());
  const lastPinchDistanceRef = useRef<number>(0);
  const gestureStartTimeRef = useRef<number>(0);
  const isGoogleMapsZoomingRef = useRef<boolean>(false);
  const lastZoomLevelRef = useRef<number>(0);
  const programmaticZoomInProgressRef = useRef<boolean>(false);
  const touchBasedZoomInProgressRef = useRef<boolean>(false);

  const clearInteractionTimeout = useCallback(() => {
    if (interactionTimeoutRef.current) {
      clearTimeout(interactionTimeoutRef.current);
      interactionTimeoutRef.current = null;
    }
  }, []);

  const setInteractionState = useCallback((interacting: boolean, reason?: string) => {
    if (interacting) {
      setIsUserInteracting(true);
      userHasManuallyPositionedRef.current = true;
      clearInteractionTimeout();
    } else {
      clearInteractionTimeout();
      // Use different timeouts based on gesture type and reason
      let timeout = 1000; // Default timeout

      if (gestureState === 'pinching' || gestureState === 'multi-touch') {
        timeout = 6000; // Extra long timeout for pinch gestures to prevent auto-fit
      } else if (gestureState === 'zooming' && touchBasedZoomInProgressRef.current) {
        timeout = 4000; // Long timeout for touch-based zoom operations
      } else if (gestureState === 'dragging') {
        timeout = 2000; // Medium timeout for dragging
      }

      interactionTimeoutRef.current = setTimeout(() => {
        setIsUserInteracting(false);
        setGestureState('idle');
        activeTouchesRef.current.clear();
        lastPinchDistanceRef.current = 0;
        isGoogleMapsZoomingRef.current = false;
        touchBasedZoomInProgressRef.current = false;
        programmaticZoomInProgressRef.current = false;
      }, timeout);
    }
  }, [gestureState, clearInteractionTimeout]);

  const calculateDistance = useCallback((touch1: TouchInfo, touch2: TouchInfo): number => {
    const dx = touch1.currentX - touch2.currentX;
    const dy = touch1.currentY - touch2.currentY;
    return Math.sqrt(dx * dx + dy * dy);
  }, []);

  const handleTouchStart = useCallback((event: TouchEvent, addDebugLog?: (message: string) => void) => {
    if (!isMobileWidth) return;

    const message = `🟢 Touch start - ${event.touches.length} touches`;
    console.log(message);
    if (addDebugLog) addDebugLog(message);

    gestureStartTimeRef.current = Date.now();

    // Update active touches
    for (let i = 0; i < event.touches.length; i++) {
      const touch = event.touches[i];
      activeTouchesRef.current.set(touch.identifier, {
        identifier: touch.identifier,
        startX: touch.clientX,
        startY: touch.clientY,
        currentX: touch.clientX,
        currentY: touch.clientY,
      });
    }

    const touchCount = activeTouchesRef.current.size;

    if (touchCount === 1) {
      const singleMessage = `🟢 Single touch detected`;
      console.log(singleMessage);
      if (addDebugLog) addDebugLog(singleMessage);
      setGestureState('single-touch');
      setInteractionState(true, 'touch-start-single');
    } else if (touchCount >= 2) {
      const multiMessage = `🟢 PINCH START - Multi-touch detected`;
      console.log(multiMessage);
      if (addDebugLog) addDebugLog(multiMessage);
      setGestureState('multi-touch');
      setInteractionState(true, 'touch-start-multi');
      touchBasedZoomInProgressRef.current = true;

      // Calculate initial pinch distance for multi-touch
      const touches = Array.from(activeTouchesRef.current.values()) as TouchInfo[];
      if (touches.length >= 2) {
        lastPinchDistanceRef.current = calculateDistance(touches[0], touches[1]);
      }
    }
  }, [isMobileWidth, setInteractionState, calculateDistance]);

  const handleTouchMove = useCallback((event: TouchEvent, addDebugLog?: (message: string) => void) => {
    if (!isMobileWidth || activeTouchesRef.current.size === 0) return;

    // Update current positions
    for (let i = 0; i < event.touches.length; i++) {
      const touch = event.touches[i];
      const touchInfo = activeTouchesRef.current.get(touch.identifier);
      if (touchInfo) {
        touchInfo.currentX = touch.clientX;
        touchInfo.currentY = touch.clientY;
      }
    }

    const touches = Array.from(activeTouchesRef.current.values()) as TouchInfo[];

    if (touches.length >= 2) {
      // Multi-touch gesture - check if it's a pinch
      const currentDistance = calculateDistance(touches[0], touches[1]);
      const distanceChange = Math.abs(currentDistance - lastPinchDistanceRef.current);

      // If distance changed significantly, it's likely a pinch gesture
      if (distanceChange > 10 && lastPinchDistanceRef.current > 0) {
        const pinchMessage = `🟢 PINCH MOVE - distance: ${currentDistance.toFixed(0)}px`;
        console.log(pinchMessage);
        if (addDebugLog) addDebugLog(pinchMessage);

        setGestureState('pinching');
        setInteractionState(true, 'pinch-gesture');
        touchBasedZoomInProgressRef.current = true;
      }

      lastPinchDistanceRef.current = currentDistance;
    } else if (touches.length === 1) {
      // Single touch - could be dragging
      const touch = touches[0];
      const moveDistance = Math.sqrt(
        Math.pow(touch.currentX - touch.startX, 2) +
        Math.pow(touch.currentY - touch.startY, 2)
      );

      if (moveDistance > 5) {
        setGestureState('dragging');
        setInteractionState(true, 'drag-gesture');
      }
    }
  }, [isMobileWidth, setInteractionState, calculateDistance]);

  const handleTouchEnd = useCallback((event: TouchEvent, addDebugLog?: (message: string) => void) => {
    if (!isMobileWidth) return;

    const endMessage = `🟢 TOUCH END - remaining: ${event.touches.length}`;
    console.log(endMessage);
    if (addDebugLog) addDebugLog(endMessage);

    // Remove ended touches
    const remainingTouchIds = Array.from(event.touches).map(t => t.identifier);
    const allTouchIds = Array.from(activeTouchesRef.current.keys());

    allTouchIds.forEach(id => {
      if (!remainingTouchIds.includes(id)) {
        activeTouchesRef.current.delete(id);
      }
    });

    const remainingTouches = activeTouchesRef.current.size;

    if (remainingTouches === 0) {
      // All touches ended - gesture complete
      const completeMessage = `🟢 PINCH COMPLETE - ${gestureState}`;
      console.log(completeMessage);
      if (addDebugLog) addDebugLog(completeMessage);

      // For pinch gestures, use a longer timeout to prevent premature auto-fit
      if (gestureState === 'pinching' || gestureState === 'multi-touch') {
        setInteractionState(false, 'pinch-complete');
      } else {
        setInteractionState(false, 'gesture-complete');
      }
    } else if (remainingTouches === 1 && gestureState === 'multi-touch') {
      // Transitioned from multi-touch to single-touch
      setGestureState('single-touch');
    }
  }, [isMobileWidth, gestureState, setInteractionState]);

  // Track previous zoom level to detect dramatic jumps
  const previousZoomRef = useRef<number | null>(null);

  const handleGoogleMapsZoomStart = useCallback((map: google.maps.Map, addDebugLog?: (message: string) => void) => {
    const currentZoom = map.getZoom();
    const previousZoom = previousZoomRef.current;

    const message = `🟡 ZOOM EVENT! Level: ${currentZoom?.toFixed(1) || 'unknown'}`;
    console.log(message);
    if (addDebugLog) addDebugLog(message);

    // Check for dramatic zoom jumps that indicate Google Maps internal issues
    if (previousZoom !== null && currentZoom !== null && currentZoom !== undefined) {
      const zoomDifference = Math.abs(currentZoom - previousZoom);

      // If zoom jumped more than 8 levels (e.g., 13.0 -> 1.0), it's likely a Google Maps bug
      if (zoomDifference > 8 && touchBasedZoomInProgressRef.current) {
        const jumpMessage = `🚨 ZOOM JUMP DETECTED! ${previousZoom.toFixed(1)} -> ${currentZoom.toFixed(1)} (diff: ${zoomDifference.toFixed(1)})`;
        console.log(jumpMessage);
        if (addDebugLog) addDebugLog(jumpMessage);

        // Restore the previous zoom level to prevent the dramatic jump
        const restoreMessage = `🔧 RESTORING zoom to ${previousZoom.toFixed(1)}`;
        console.log(restoreMessage);
        if (addDebugLog) addDebugLog(restoreMessage);

        // Set zoom back to previous level
        map.setZoom(previousZoom);
        return; // Don't process this as a user interaction
      }
    }

    // Update previous zoom for next comparison
    if (currentZoom !== null && currentZoom !== undefined) {
      previousZoomRef.current = currentZoom;
    }

    // Only treat as user interaction if:
    // 1. We're not in the middle of a programmatic zoom (fitBounds)
    // 2. We have active touches OR it's a desktop zoom (wheel/button)
    if (!programmaticZoomInProgressRef.current) {
      if (touchBasedZoomInProgressRef.current || !isMobileWidth) {
        const userMessage = `🟡 User zoom detected`;
        console.log(userMessage);
        if (addDebugLog) addDebugLog(userMessage);
        isGoogleMapsZoomingRef.current = true;
        setGestureState('zooming');
        setInteractionState(true, 'google-maps-zoom');
      } else {
        const ignoreMessage = `🟡 Zoom ignored - no touch`;
        console.log(ignoreMessage);
        if (addDebugLog) addDebugLog(ignoreMessage);
      }
    } else {
      const progMessage = `🟡 Programmatic zoom - ignored`;
      console.log(progMessage);
      if (addDebugLog) addDebugLog(progMessage);
    }
  }, [setInteractionState, isMobileWidth]);

  const handleGoogleMapsZoomEnd = useCallback(() => {
    // Don't immediately end interaction - let the timeout handle it
    // This prevents auto-fit from triggering too quickly after zoom
    isGoogleMapsZoomingRef.current = false;
    if (!programmaticZoomInProgressRef.current) {
      setInteractionState(false, 'google-maps-zoom-end');
    }
  }, [setInteractionState]);

  // Handler for when we're about to do a programmatic zoom (fitBounds)
  const handleProgrammaticZoomStart = useCallback(() => {
    programmaticZoomInProgressRef.current = true;
  }, []);

  const handleProgrammaticZoomEnd = useCallback(() => {
    // Use a short delay to ensure all zoom events have fired
    setTimeout(() => {
      programmaticZoomInProgressRef.current = false;
    }, 100);
  }, []);

  const handleMouseInteractionStart = useCallback(() => {
    if (!isMobileWidth) {
      setGestureState('dragging');
      setInteractionState(true, 'mouse-interaction');
    }
  }, [isMobileWidth, setInteractionState]);

  const handleMouseInteractionEnd = useCallback(() => {
    if (!isMobileWidth) {
      setInteractionState(false, 'mouse-interaction-end');
    }
  }, [isMobileWidth, setInteractionState]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearInteractionTimeout();
      activeTouchesRef.current.clear();
    };
  }, [clearInteractionTimeout]);

  return {
    isUserInteracting,
    gestureState,
    userHasManuallyPositionedRef,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    handleGoogleMapsZoomStart,
    handleGoogleMapsZoomEnd,
    handleMouseInteractionStart,
    handleMouseInteractionEnd,
    handleProgrammaticZoomStart,
    handleProgrammaticZoomEnd,
    touchBasedZoomInProgressRef,
    programmaticZoomInProgressRef,
  };
};



const Map: React.FC<MapProps> = (props: MapProps) => {
  const persistence: Persistence = new LocalStorage();
  const theme: Theme = useAppSelector(selectTheme);
  const { networkFeatures } = useNetworkFeatures();

  const windowWidth: number = useWindowWidth();
  const isMobileWidth: boolean = windowWidth < BREAKPOINT_MD;
  const { t } = useTranslation<['home', 'trip']>(['home', 'trip']);

  const { mapState, dispatch: dispatchMapAction } = useMapContext();
  const { selectedShiftSolutionId, selectedStop, selectedRouteId, productTourPage } = mapState;

  // Use ref for map instance instead of storing in reducer
  const mapRef = useRef<google.maps.Map | null>(null);
  const [bounds, setBounds] = useState<google.maps.LatLngBounds | null>(null);
  const [isMapOverlayVisible, setIsMapOverlayVisible] = useState<boolean>(false);

  // Refs to store event listener cleanup functions
  const eventListenersRef = useRef<(() => void)[]>([]);

  // Debug state for mobile testing - moved here to be available for onLoad
  const [debugLogs, setDebugLogs] = useState<string[]>([]);
  const addDebugLog = useCallback((message: string) => {
    setDebugLogs(prev => {
      const newLogs = [...prev, `${new Date().toLocaleTimeString()}: ${message}`];
      return newLogs.slice(-10); // Keep only last 10 logs
    });
  }, []);

  const {
    isUserInteracting,
    userHasManuallyPositionedRef,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    handleGoogleMapsZoomStart,
    handleMouseInteractionStart,
    handleMouseInteractionEnd,
    handleProgrammaticZoomStart,
    handleProgrammaticZoomEnd,
  } = useUserInteractionTracking(isMobileWidth);

  // Additional refs for auto-fit bounds functionality
  const mapLoadedRef = useRef<boolean>(false);

  const [runningShiftSolutions, filteredShiftSolutions, checkIsRouteActive] = useRunningShiftSolutions();
  const hubMarkerRefList: React.MutableRefObject<HTMLDivElement[]> = useRef([]);

  useEffect(() => {
    if (productTourPage) {
      // Select a random stop if on the stops page of the product tour
      if (productTourPage === 'shuttles-stops') {
        dispatchMapAction({
          type: 'SELECT_STOP',
          payload: props.stops[0],
        });
      } else if (selectedStop) {
        // if product tour open on a different page, un-select stop as it's not the correct thing to highlight
        dispatchMapAction({
          type: 'SET_SELECTED_STOP',
          payload: null,
        });
      }
    }
  }, [productTourPage]);

  const selectedShiftSolution: FixedRoute.RunningShiftSolution | null =
    selectedShiftSolutionId && filteredShiftSolutions && filteredShiftSolutions[selectedShiftSolutionId]
      ? filteredShiftSolutions[selectedShiftSolutionId]
      : null;

  const hasNoShuttlesToDisplay: boolean = Boolean(
    !filteredShiftSolutions ||
      (filteredShiftSolutions &&
        (!Object.entries(filteredShiftSolutions).length ||
          (Object.entries(filteredShiftSolutions).length && !Object.entries(filteredShiftSolutions)[0][1].location))),
  );

  const cleanupEventListeners = useCallback(() => {
    eventListenersRef.current.forEach((cleanup) => cleanup());
    eventListenersRef.current = [];
  }, []);

  const onLoad = React.useCallback(
    (map: google.maps.Map) => {
      mapRef.current = map;
      mapLoadedRef.current = true;
      addDebugLog('🗺️ MAP LOADED');

      // Backward compatibility - still dispatch to context for other components that might need it
      dispatchMapAction({
        type: 'SET_MAP',
        payload: map,
      });

      // Clean up any existing listeners before adding new ones
      cleanupEventListeners();

      // Add comprehensive event listeners for user interaction detection
      if (map && window.google) {
        // Google Maps listeners (these are cleaned up automatically by Google Maps)
        const zoomListener = map.addListener('zoom_changed', () => handleGoogleMapsZoomStart(map, addDebugLog));
        const dragStartListener = map.addListener('dragstart', handleMouseInteractionStart);
        const dragEndListener = map.addListener('dragend', handleMouseInteractionEnd);

        // Store Google Maps listeners cleanup functions
        eventListenersRef.current.push(
          () => google.maps.event.removeListener(zoomListener),
          () => google.maps.event.removeListener(dragStartListener),
          () => google.maps.event.removeListener(dragEndListener),
        );

        // Add listeners for touch events via the map div for better pinch detection
        const mapDiv = map.getDiv();
        if (mapDiv) {
          // Touch events for pinch gestures - use our enhanced handlers
          const touchStartHandler = (e: TouchEvent) => handleTouchStart(e, addDebugLog);
          const touchMoveHandler = (e: TouchEvent) => handleTouchMove(e, addDebugLog);
          const touchEndHandler = (e: TouchEvent) => handleTouchEnd(e, addDebugLog);

          mapDiv.addEventListener('touchstart', touchStartHandler, { passive: true });
          mapDiv.addEventListener('touchmove', touchMoveHandler, { passive: true });
          mapDiv.addEventListener('touchend', touchEndHandler, { passive: true });

          // Mouse events for desktop
          mapDiv.addEventListener('wheel', handleMouseInteractionStart, { passive: true });
          mapDiv.addEventListener('mousedown', handleMouseInteractionStart);
          mapDiv.addEventListener('mouseup', handleMouseInteractionEnd);

          // Store DOM event listeners cleanup functions
          eventListenersRef.current.push(
            () => mapDiv.removeEventListener('touchstart', handleTouchStart),
            () => mapDiv.removeEventListener('touchmove', handleTouchMove),
            () => mapDiv.removeEventListener('touchend', handleTouchEnd),
            () => mapDiv.removeEventListener('wheel', handleMouseInteractionStart),
            () => mapDiv.removeEventListener('mousedown', handleMouseInteractionStart),
            () => mapDiv.removeEventListener('mouseup', handleMouseInteractionEnd),
          );
        }
      }
    },
    [handleTouchStart, handleTouchMove, handleTouchEnd, handleGoogleMapsZoomStart, handleMouseInteractionStart, handleMouseInteractionEnd, cleanupEventListeners, addDebugLog],
  );

  const onUnmount = React.useCallback(() => {
    // Clean up all event listeners
    cleanupEventListeners();

    // Clear the map reference
    mapRef.current = null;
    mapLoadedRef.current = false;

    // Backward compatibility - still dispatch to context
    dispatchMapAction({
      type: 'SET_MAP',
      payload: null,
    });
  }, [cleanupEventListeners]);

  const setSelectedShiftSolutionId = (shiftSolutionId: string | null) => {
    dispatchMapAction({
      type: 'SELECT_SHIFT_SOLUTION_ID',
      payload: shiftSolutionId,
    });
  };

  const handleRouteStopPress = (stopId: string) => {
    const pressedStop: FixedRoute.ServiceRouteStop | null =
      props.stops.find((stop: FixedRoute.ServiceRouteStop) => stop.id === stopId) || null;
    dispatchMapAction({
      type: 'SELECT_STOP',
      payload: pressedStop,
    });
  };

  const handleRoutePress = (routeVersionId: string | null) => {
    dispatchMapAction({
      type: 'SELECT_ROUTE_ID',
      payload: routeVersionId,
    });
  };

  const getStopColourBySelection = (stopId: string, stopColour: string) => {
    if ((selectedStop || productTourPage) && selectedStop?.id !== stopId) {
      return BLURRED_MAP_DETAIL_COLOUR;
    } else {
      return stopColour;
    }
  };

  const getRouteColourBySelection = (routeVersionId: string, routeColour: string) => {
    if (productTourPage || (selectedRouteId && selectedRouteId !== routeVersionId) || !checkIsRouteActive(routeVersionId)) {
      return BLURRED_MAP_DETAIL_COLOUR;
    } else {
      return routeColour;
    }
  };

  const getShiftSolutionColourBySelection = (shiftSolutionId: string, shiftSolutionColour: string) => {
    if (selectedShiftSolutionId && selectedShiftSolutionId !== shiftSolutionId) {
      return BLURRED_MAP_DETAIL_COLOUR;
    } else {
      return shiftSolutionColour;
    }
  };

  const onMapItemSelected = (isSelected: boolean) => {
    if (isMobileWidth) {
      setIsMapOverlayVisible(isSelected);
    }
    if (props.onMapItemSelected) {
      props.onMapItemSelected(isSelected);
    }
    if (window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(`${isSelected}`);
    }
  };

  // Track if this is the initial load and data is ready
  const initialDataLoadedRef = useRef<boolean>(false);
  const hasEverAutoFittedRef = useRef<boolean>(false);

  // Check if initial data is ready for auto-fit
  const isInitialDataReady = mapLoadedRef.current &&
    (props.routeShapes.length > 0 || props.stops.length > 0 || props.serviceHubs.length > 0);

  useEffect(() => {
    // Only auto-fit on the very first load when all data is ready
    // This is the ONLY scenario where auto-fit should happen automatically

    // Don't auto-fit if:
    // 1. User is currently interacting
    // 2. User has ever manually positioned the map
    // 3. We've already done the initial auto-fit
    // 4. Initial data is not ready yet
    if (isUserInteracting ||
        userHasManuallyPositionedRef.current ||
        hasEverAutoFittedRef.current ||
        !isInitialDataReady) {
      return;
    }

    // Mark initial data as loaded
    if (!initialDataLoadedRef.current && isInitialDataReady) {
      initialDataLoadedRef.current = true;

      // Wait a bit for all data to settle, then do the initial auto-fit
      const initialFitTimeout = setTimeout(() => {
        // Final check - only auto-fit if user hasn't interacted
        if (!userHasManuallyPositionedRef.current && !isUserInteracting && mapRef.current) {
          const positions: { latitude: number; longitude: number }[] = [];

          // Fit to all available data for initial load
          for (const shape of props.routeShapes) {
            positions.push(...shape.orderedCoordinates);
          }
          for (const stop of props.stops) {
            positions.push({ latitude: stop.latitude, longitude: stop.longitude });
          }
          for (const hub of props.serviceHubs) {
            positions.push({ latitude: hub.latitude, longitude: hub.longitude });
          }

          if (positions.length > 0) {
            hasEverAutoFittedRef.current = true;
            fitMapCardToPositions(
              mapRef.current,
              positions,
              props.mapPadding,
              handleProgrammaticZoomStart,
              handleProgrammaticZoomEnd,
              'INITIAL_LOAD',
              addDebugLog
            );
          }
        }
      }, 500); // Small delay to ensure all data is rendered

      return () => clearTimeout(initialFitTimeout);
    }
  }, [
    isInitialDataReady,
    isUserInteracting,
    props.routeShapes,
    props.stops,
    props.serviceHubs,
    props.mapPadding,
    handleProgrammaticZoomStart,
    handleProgrammaticZoomEnd,
  ]);



  // Cleanup event listeners on unmount
  useEffect(() => {
    return () => {
      cleanupEventListeners();
    };
  }, [cleanupEventListeners]);

  useEffect(() => {
    const isMapItemSelected: boolean = !!selectedStop || !!selectedRouteId || !!selectedShiftSolutionId;
    onMapItemSelected(isMapItemSelected);
    if (isMapItemSelected && !productTourPage) {
      props.hideTripPlanner();
    } else {
      props.showTripPlanner();
    }
  }, [selectedShiftSolutionId, selectedStop, selectedRouteId, props.isTripPlanerOpen, productTourPage]);

  useEffect(() => {
    hubMarkerRefList.current.forEach((element) => {
      if (element.parentElement) {
        //sets zIndex to 4 so markers would be above route stops and below long walk warning tooltip
        element.parentElement.style.zIndex = '4';
      }
    });
  }, [hubMarkerRefList.current.length]);

  const isShowingJourneyInstructions: boolean = Array.isArray(props.journeyPoints) && props.journeyPoints.length > 0;



  // If we're showing the instructions we want to hide all route shapes that aren't part of the trip
  const filteredShapes: FixedRoute.ServiceRouteShape[] = useMemo(() => {
    return props.routeShapes.filter((routeShape: FixedRoute.ServiceRouteShape) => {
      return (
        !isShowingJourneyInstructions ||
        props.journeyPoints?.find(
          (journeyPoint) =>
            journeyPoint[0]?.type === 'bus-departure' && journeyPoint[0].routeId && journeyPoint[0].routeId === routeShape.routeVersionId,
        )
      );
    });
  }, [props.routeShapes, props.journeyPoints]);

  return (
    <GoogleMap
      mapContainerStyle={props.style ?? containerStyle}
      center={props.initalCenter}
      zoom={props.zoom ?? 13}
      onLoad={onLoad}
      onUnmount={onUnmount}
      onBoundsChanged={() => setBounds(mapRef.current?.getBounds() || null)}
      options={{
        styles: theme.map,
        streetViewControl: false,
        mapTypeControl: false,
        zoomControl: false,
        keyboardShortcuts: false,
        fullscreenControl: false,
        clickableIcons: false,
        gestureHandling: 'greedy', // Allow all gestures without requiring modifier keys
        zoomControlOptions: {
          position: isMobileWidth ? google.maps.ControlPosition.TOP_RIGHT : undefined,
        },
        minZoom: isMobileWidth ? 3 : 3, // Temporarily increased from 1 to 3 to test zoom snap issue
      }}
      onClick={() => {
        dispatchMapAction({
          type: 'RESET_SELECTIONS',
        });
      }}
    >
      {(productTourPage || (isMapOverlayVisible && !props.isTripPlanerOpen)) && (
        <OverlayViewF
          mapPaneName={OverlayView.MAP_PANE}
          position={!bounds ? mapRef.current?.getCenter() : undefined}
          bounds={bounds || undefined}
          getPixelPositionOffset={(offsetWidth, offsetHeight) => getPixelPositionOffset(offsetWidth, offsetHeight / 2)}
        >
          <StyledMapOverlay colour={MAP_DARKENING_OVERLAY_COLOUR} />
        </OverlayViewF>
      )}

      {filteredShapes.map((routeShape: FixedRoute.ServiceRouteShape) => (
        <Polyline
          key={`map-route-shape-${routeShape.id}`}
          onClick={() => {
            onMapItemSelected(true);
            handleRoutePress(routeShape.routeVersionId);
          }}
          path={routeShape.orderedCoordinates.map<google.maps.LatLng>(
            (coordinate) => new google.maps.LatLng(Number(coordinate.latitude), Number(coordinate.longitude)),
          )}
          options={{
            strokeColor: getRouteColourBySelection(routeShape.routeVersionId, routeShape.colour),
            strokeOpacity: 1,
            strokeWeight: 5,
          }}
        />
      ))}

      {Array.isArray(props.waypoints)
        ? props.waypoints.map((waypoint: Waypoint) => (
            <React.Fragment key={waypoint.id}>
              {waypoint.marker && (
                <OverlayViewF
                  position={{ lat: waypoint.marker.latitude, lng: waypoint.marker.longitude }}
                  mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
                  getPixelPositionOffset={getPixelPositionOffset}
                >
                  <HubMarker icon={waypoint.marker.icon ?? ''} color={waypoint.color || 'primary'} />
                </OverlayViewF>
              )}

              <MapDirections
                renderDirections={props.renderDirections}
                places={[waypoint.origin, waypoint.destination]}
                region={props.regionCode}
                color={waypoint.color}
              />
            </React.Fragment>
          ))
        : null}

      {Array.isArray(props.journeyPoints) && props.journeyPoints.length > 0 ? (
        <>
          {props.journeyPoints.map<ReactNode[]>((journeyPointSegment: JourneyPointList, journeyPointIndex: number) =>
            journeyPointSegment.map<ReactNode>((point: JourneyPoint<JourneyPointType>, journeyPointSegmentIndex: number) => (
              <OverlayViewF
                key={point.placeId}
                position={{ lat: point.latitude, lng: point.longitude }}
                mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
                getPixelPositionOffset={getPixelPositionOffset}
              >
                <LocationMarker
                  colour={point.colour}
                  placeId={point.placeId}
                  hasIcon={
                    journeyPointSegmentIndex === journeyPointSegment.length - 1 &&
                    !!props.journeyPoints &&
                    journeyPointIndex === props.journeyPoints.length - 1
                  }
                />
              </OverlayViewF>
            )),
          )}

          {props.journeyPoints.map<ReactNode>((journeyPointSegment: JourneyPointList) => {
            // We don't want to render google directions for bus segments
            const isBusSegment: boolean = journeyPointSegment[0]?.type === 'bus-departure';

            return (
              <MapDirections
                key={journeyPointSegment.toString()}
                places={isBusSegment ? [] : journeyPointSegment}
                region={props.regionCode}
                solid={false}
                travelMode={google.maps.TravelMode.WALKING}
                renderDirections={props.renderDirections}
              />
            );
          })}
        </>
      ) : (
        props.serviceHubs.map<ReactNode>((hub: OnDemandServiceHub) => (
          <OverlayViewF
            key={hub.name}
            position={{ lat: hub.latitude, lng: hub.longitude }}
            mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
            getPixelPositionOffset={getPixelPositionOffset}
          >
            <HubMarker icon={hub.icon} color="primary" />
          </OverlayViewF>
        ))
      )}

      {Array.isArray(props.stops) &&
        !isShowingJourneyInstructions &&
        props.stops.map<ReactNode>((stop: FixedRoute.ServiceRouteStop, index: number) => (
          <OverlayViewF
            key={stop.name}
            position={{ lat: stop.latitude, lng: stop.longitude }}
            mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
            getPixelPositionOffset={getPixelPositionOffset}
          >
            <StyledMapMarkerButton
              ref={index === 0 ? props.firstStopRef : undefined}
              onClick={(e) => {
                e.stopPropagation();
                onMapItemSelected(true);
                if (selectedStop?.id === stop.id) {
                  dispatchMapAction({
                    type: 'SET_SELECTED_STOP',
                    payload: null,
                  });
                } else {
                  dispatchMapAction({
                    type: 'SELECT_STOP',
                    payload: stop,
                  });
                }
              }}
            >
              <HubMarker icon={stop.icon} color={getStopColourBySelection(stop.id, stop.colour)} noCircle isMapIcon />
            </StyledMapMarkerButton>
          </OverlayViewF>
        ))}

      {selectedStop && (
        <OverlayViewF
          key={selectedStop.name}
          position={{ lat: selectedStop.latitude, lng: selectedStop.longitude }}
          mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
          getPixelPositionOffset={getPixelPositionOffset}
        >
          <StyledMapMarkerButton
            highlightColour={productTourPage === 'shuttles-stops' ? theme.colors.accent : undefined}
            // Stops onclick events propagating to the Map component
            ref={(ref) => ref && google.maps.OverlayView.preventMapHitsFrom(ref)}
            onClick={() => {
              if (props.onMapStopPressed) {
                props.onMapStopPressed(selectedStop);
                dispatchMapAction({
                  type: 'SET_SELECTED_STOP',
                  payload: null,
                });
                dispatchMapAction({
                  type: 'SET_SELECTED_ROUTE_ID',
                  payload: null,
                });
                props.showTripPlanner();
              }
            }}
          >
            {/* Don't show stop info if just being highlighted by product tour */}
            {!productTourPage && (
              <StyledStopTooltip colour={'dark-contrast'} bubbleBorderRadius={50} bubblePadding={'5px 10px'}>
                <StyledStopTooltipContents>
                  <Text size={0.6} weight={700} fontFamily={'body'}>
                    {selectedStop.name}
                  </Text>
                  <StyledForwardChevron icon={chevronForward} />
                </StyledStopTooltipContents>
              </StyledStopTooltip>
            )}
          </StyledMapMarkerButton>
        </OverlayViewF>
      )}

      {props.schedulePositions &&
        Object.entries(props.schedulePositions).map<ReactNode>(([scheduleId, schedulePosition]) => (
          <OverlayViewF
            key={scheduleId}
            position={{ lat: schedulePosition.latitude, lng: schedulePosition.longitude }}
            mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
            getPixelPositionOffset={getPixelPositionOffset}
          >
            <MapMarker icon={carOutline} colour="primary" />
          </OverlayViewF>
        ))}

      {selectedShiftSolution && selectedShiftSolution.location && selectedShiftSolution?.availableCapacities?.length && (
        <OverlayViewF
          key={selectedShiftSolutionId + 'selected'}
          position={{
            lat: selectedShiftSolution.location.latitude,
            lng: selectedShiftSolution.location.longitude,
          }}
          mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
          getPixelPositionOffset={getPixelPositionOffset}
        >
          <FixedRouteVehicleDetails
            routeLabel={selectedShiftSolution?.route.label}
            availableCapacity={selectedShiftSolution?.availableCapacities[0]?.amount}
            etaMins={selectedShiftSolution?.etaNextStop}
            colour={(runningShiftSolutions && selectedShiftSolution?.route?.colour) ?? 'primary'}
            onClick={() =>
              dispatchMapAction({
                type: 'SET_SELECTED_SHIFT_SOLUTION_ID',
                payload: null,
              })
            }
          />
        </OverlayViewF>
      )}

      {filteredShiftSolutions &&
        Object.entries(filteredShiftSolutions).map<ReactNode>(
          ([shiftSolutionId, shiftSolutionPosition], index: number) =>
            shiftSolutionPosition.location && (
              <OverlayViewF
                key={shiftSolutionId}
                position={{ lat: shiftSolutionPosition.location.latitude, lng: shiftSolutionPosition.location.longitude }}
                mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
                getPixelPositionOffset={getPixelPositionOffset}
              >
                <StyledMapMarkerButton
                  // Stops onclick events propagating to the Map component
                  ref={(ref) => ref && google.maps.OverlayView.preventMapHitsFrom(ref)}
                  onClick={() => {
                    onMapItemSelected(true);
                    if (selectedShiftSolutionId === shiftSolutionId) {
                      dispatchMapAction({
                        type: 'SET_SELECTED_SHIFT_SOLUTION_ID',
                        payload: null,
                      });
                    } else {
                      dispatchMapAction({
                        type: 'SELECT_SHIFT_SOLUTION_ID',
                        payload: shiftSolutionId,
                      });
                    }
                  }}
                >
                  {networkFeatures['show_vehicle_icon'] ? (
                    <Vehicle
                      position={{ lat: -32.922677, lng: 151.747167 }}
                      direction={runningShiftSolutions[shiftSolutionId]?.location?.heading! ?? 0}
                      colour={getShiftSolutionColourBySelection(
                        shiftSolutionId,
                        (filteredShiftSolutions && filteredShiftSolutions[shiftSolutionId]?.route?.colour) ?? 'primary',
                      )}
                    />
                  ) : (
                    <MapMarker
                      icon={theme.icons?.fixedRouteVehicleRounded ?? busOutline}
                      size={theme.icons?.fixedRouteVehicleRounded ? 'large' : 'small'}
                      colour={getShiftSolutionColourBySelection(
                        shiftSolutionId,
                        (filteredShiftSolutions && filteredShiftSolutions[shiftSolutionId]?.route?.colour) ?? 'primary',
                      )}
                      border={{ width: 1, colour: 'white' }}
                    />
                  )}
                </StyledMapMarkerButton>
              </OverlayViewF>
            ),
        )}

      {props.userPosition ? (
        <OverlayViewF
          position={{ lat: props.userPosition.coords.latitude, lng: props.userPosition.coords.longitude }}
          mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
          getPixelPositionOffset={getPixelPositionOffset}
        >
          <LocationMarker placeId="" />
        </OverlayViewF>
      ) : null}

      <StyledMapLogo src={theme.logo} width={90} />

      {/* Debug overlay for mobile testing */}
      {debugLogs.length > 0 && (
        <StyledDebugOverlay>
          <StyledDebugTitle>Debug Log</StyledDebugTitle>
          {debugLogs.slice(-5).map((log, index) => (
            <StyledDebugLog key={index}>{log}</StyledDebugLog>
          ))}
        </StyledDebugOverlay>
      )}

      {selectedRouteId && (
        <StyledFixedRouteRoutesDetails
          selectedRouteId={selectedRouteId}
          handleRouteSelect={handleRoutePress}
          onRouteStopPressed={handleRouteStopPress}
          isActive={checkIsRouteActive(selectedRouteId)}
        />
      )}

      {selectedShiftSolutionId && (
        <StyledFixedRouteShiftSolutionDetails
          shiftSolutionId={selectedShiftSolutionId}
          setSelectedShiftSolutionId={setSelectedShiftSolutionId}
        />
      )}

      {selectedStop && !productTourPage && (
        <StyledFixedRouteStopDetails
          onShiftSolutionPressed={setSelectedShiftSolutionId}
          stopName={selectedStop.name}
          stopAddressLabel={selectedStop.addressLabel}
          stopId={selectedStop.id}
        />
      )}
    </GoogleMap>
  );
};

const StyledMapOverlay = styled.div<{ colour: string }>`
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 300%;
  height: 300%;
  background-color: ${({ colour }) => colour};
`;

const StyledForwardChevron = styled(IonIcon)`
  margin-left: 8px;
  font-size: 10px;
`;

const StyledStopTooltipContents = styled.div`
  display: flex;
  align-items: center;
  flex-direction: row;
`;

const StyledStopTooltip = styled(Tooltip)`
  position: relative;
  top: -30px;
  z-index: ${ABOVE_MAP_Z_INDEX};
`;

const StyledFixedRouteStopDetails = styled(FixedRouteStopDetails)`
  z-index: ${ABOVE_MAP_Z_INDEX};
  display: flex;
  width: 100%;
`;

const StyledFixedRouteRoutesDetails = styled(FixedRouteRoutesDetails)`
  z-index: ${ABOVE_MAP_Z_INDEX};
  display: flex;
  width: 100%;
`;

const StyledFixedRouteShiftSolutionDetails = styled(FixedRouteShiftSolutionDetails)`
  z-index: ${ABOVE_MAP_Z_INDEX};
  display: flex;
  width: 100%;
`;

const StyledMapLogo = styled.img`
  position: absolute;
  bottom: 30px;
  right: 80px;
`;

const StyledMapMarkerButton = styled.button<{ highlightColour?: string }>`
  background: transparent;
  border: none;
  font-size: 0px;
  z-index: 6;
  ${({ highlightColour }) => (highlightColour ? getPointHighlightEffect(highlightColour, -25, -36) : '')};
`;

const StyledDebugOverlay = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 10px;
  max-width: 300px;
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
`;

const StyledDebugTitle = styled.div`
  font-weight: bold;
  margin-bottom: 5px;
  color: #00ff00;
`;

const StyledDebugLog = styled.div`
  margin-bottom: 2px;
  word-break: break-all;
  line-height: 1.2;
`;

export default React.memo(Map);
