import React from 'react';
import { IonIcon } from '@ionic/react';
import { locationSharp } from 'ionicons/icons';
import styled from 'styled-components';
import { FALLBACK_LIGHT_COLOUR, getColourName } from '../../../../helpers/colour.helpers';

type LocationMarkerProps = {
  colour?: string;
  placeId?: string;
  hasIcon?: boolean;
  className?: string;
};

const LocationMarker = (props: LocationMarkerProps) => {
  return (
    <StyledCircleIcon className={props.className} colour={props.colour} hasIcon={props.hasIcon} placeId={props.placeId}>
      {props.hasIcon ? <IonIcon icon={locationSharp} color={props.colour} /> : null}
    </StyledCircleIcon>
  );
};

const StyledCircleIcon = styled.div<{ colour?: string; hasIcon?: boolean; placeId?: string }>`
  height: 32px;
  width: 32px;
  border-radius: 32px;
  border: ${({ hasIcon }) => (hasIcon ? 4 : 6)}px solid ${({ placeId, colour }) => (placeId === '' ? FALLBACK_LIGHT_COLOUR : colour)};
  background-color: ${({ placeId }) => (placeId === '' ? 'var(--ion-color-location)' : FALLBACK_LIGHT_COLOUR)};
  font-size: 18px;
  color: ${({ colour }) => (colour ? getColourName(colour) : 'var(--ion-color-dark)')};

  display: flex;
  align-items: center;
  justify-content: center;

  // When placeId is an empty string it means it's a current location(blue dot) and should go above other location markers with z-index: 8
  z-index: ${({ placeId }) => (placeId === '' ? 8 : 7)};

  position: absolute;
  margin-top: -30px;
  margin-left: -15px;

  box-shadow: ${({ placeId }) => (placeId === '' ? '0 0 5px 0 rgba(0, 0, 0, 0.75)' : null)};
`;

export default LocationMarker;
