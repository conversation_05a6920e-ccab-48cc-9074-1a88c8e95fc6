import React from 'react';
import styled from 'styled-components';
import HubIcon from '../../../elements/Icon/HubIcon';

type HubMarkerProps = {
  icon: string;
  color: string;
  id?: string;
  className?: string;
  noCircle?: boolean;
  isMapIcon?: boolean;
};

const HubMarker = React.forwardRef((props: HubMarkerProps, ref: React.ForwardedRef<HTMLDivElement>) => {
  return (
    <StyledMarker id={props.id} className={props.className} ref={ref}>
      {props.noCircle ? (
        <StyledMarkerCenterNoCircle>
          <HubIcon icon={props.icon} color={props.color} isMapIcon />
        </StyledMarkerCenterNoCircle>
      ) : (
        <StyledMarkerCenter>
          <HubIcon icon={props.icon} color={props.color} isMapIcon />
        </StyledMarkerCenter>
      )}
    </StyledMarker>
  );
});

const StyledMarker = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const StyledMarkerCenter = styled.div`
  width: 46px;
  height: 46px;
  border-radius: 30px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 6px #00000029;

  ion-icon {
    font-size: 26px;
  }
`;

const StyledMarkerCenterNoCircle = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  ion-icon {
    font-size: 26px;
  }
`;

export default HubMarker;
