import React from 'react';
import styled from 'styled-components';
import MapMarker from './MapMarker';
import EstimateTooltip from '../../../elements/ToolTip/EstimateTooltip';

type MapMarkerWithEstimateProps = {
  icon: string;
  color: string;
  label: string;
  className?: string;
};

const MapMarkerWithEstimate: React.FC<MapMarkerWithEstimateProps> = (props: MapMarkerWithEstimateProps) => {
  return (
    <StyledMapMarkerWithEstimate className={props.className}>
      <StyledMapMarkerEstimate label={props.label} />
      <MapMarker icon={props.icon} colour={props.color} />
    </StyledMapMarkerWithEstimate>
  );
};

const StyledMapMarkerWithEstimate = styled.div``;

const StyledMapMarkerEstimate = styled(EstimateTooltip)`
  margin-bottom: 5px;
`;

export default MapMarkerWithEstimate;
