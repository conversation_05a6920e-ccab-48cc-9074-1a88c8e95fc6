import React, { useEffect, useState } from 'react';
import VehicleWithDirection from '../../../../assets/VehicleWithDirection';

type Position = {
  lat: number;
  lng: number;
};

type VehicleIconProps = {
  colour: string; // ion colour var name (eg. primary) or hex code
  direction: number;
  position: Position;
};

const VehicleIcon: React.FC<VehicleIconProps> = (props: VehicleIconProps) => {
  const [direction, setDirection] = useState<number>(props.direction);
  const [lastPosition, setLastPosition] = useState<Position>(props.position);

  useEffect(() => {
    if (props.direction >= 0) {
      // the locations table *should* contain a 'heading' property which we use
      // as a 'direction' prop here. If that exists, we just use that for the direction state
      setDirection(props.direction);
    } else {
      /* 
         Otherwise we need to use Pythagoras:
         We can create a right angled triangle with a height based on the current and previous lng values
         and a width based on the current and previous lat values.
         From there we can calculate the angle created between these 2 points

         We do this using atan2 (https://en.wikipedia.org/wiki/Atan2)
         Math.atan2 will return a value between -π and π radians
         π radians == 180°. so atan2 returns a value equal to between -180° and 180°
        
         This angle goes from due north to due south.
         A positive value means you're heading east, negative means you're heading west 
         
         We want an angle between 0° and 360°
         If radians are negative, it means we're heading west
         So we need to add 2π radians (360°) to the result
      */
      if (lastPosition.lat !== props.position.lat || lastPosition.lng !== props.position.lng) {
        const dx: number = lastPosition.lat - props.position.lat; // width of right angled triangle
        const dy: number = lastPosition.lng - props.position.lng; // height of right angled triangle
        let radians: number = Math.atan2(dx, dy);

        if (radians < 0) {
          radians += 2 * Math.PI;
        }
        setDirection((radians * 180) / Math.PI); // convert radians to degrees to set direction in degrees
      }
      setLastPosition(props.position);
    }
  }, [props.position, props.direction]);

  return <VehicleWithDirection colour={props.colour} direction={direction} />;
};

export default VehicleIcon;
