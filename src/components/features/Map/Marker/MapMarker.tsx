import React from 'react';
import styled from 'styled-components';
import { IonIcon } from '@ionic/react';
import { getColourNameWithOpacity } from '../../../../helpers/colour.helpers';

type MapMarkerBorderProps = {
  width: number;
  colour: string;
};

type MapMarkerProps = {
  icon: string; // fontawesome icon name or path to svg
  colour: string; // ion colour var name (eg. primary) or hex code
  className?: string;
  size?: 'small' | 'large';
  border?: MapMarkerBorderProps;
};

const MapMarker: React.FC<MapMarkerProps> = (props: MapMarkerProps) => {
  return (
    <StyledMarker className={props.className}>
      <StyledMarkerOuter backgroundColour={props.colour}>
        <StyledMarkerInner backgroundColour={props.colour}>
          <StyledMarkerCenter backgroundColour={props.colour} border={props.border}>
            <IonIcon icon={props.icon} color={props.colour} size={props.size ?? 'small'} />
          </StyledMarkerCenter>
        </StyledMarkerInner>
      </StyledMarkerOuter>
      <StyledMarkerTail backgroundColour={props.colour} />
    </StyledMarker>
  );
};

const StyledMarker = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const StyledMarkerOuter = styled.div<{ backgroundColour: string }>`
  width: 66px;
  height: 66px;
  border-radius: 66px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ backgroundColour }) => getColourNameWithOpacity(backgroundColour, 0.2)};
`;

const StyledMarkerInner = styled.div<{ backgroundColour: string }>`
  width: 50px;
  height: 50px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ backgroundColour }) => getColourNameWithOpacity(backgroundColour, 0.4)};
`;

const StyledMarkerCenter = styled.div<{ backgroundColour: string; border?: MapMarkerBorderProps }>`
  width: 30px;
  height: 30px;
  border-radius: 30px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: ${(props) => props.border?.width ?? 0}px solid ${(props) => props.border?.colour ?? 'transparent'};
  box-shadow: 0 0 5px -2px ${({ backgroundColour }) => getColourNameWithOpacity(backgroundColour, 1)};
  color: ${({ backgroundColour }) => getColourNameWithOpacity(backgroundColour, 1)};
`;

const StyledMarkerTail = styled.div<{ backgroundColour: string }>`
  width: 3px;
  border-radius: 3px;
  margin-top: -20px;
  height: 60px;
  z-index: -1;
  background-color: ${({ backgroundColour }) => getColourNameWithOpacity(backgroundColour, 1)};
`;

export default MapMarker;
