import React, { useEffect, useState } from 'react';
import { IonIcon } from '@ionic/react';
import { busOutline, chevronForwardOutline } from 'ionicons/icons';
import styled from 'styled-components';
import Text from '../../../elements/Text/Text';
import { useTranslation } from 'react-i18next';
import { capacityColor } from '../../../../helpers/capacity.helpers';
import CapacitySvg from '../../../../assets/chair-icon.svg?react';
import { calculateStopEta } from '../../../../helpers/date-format.helpers';
import RoundedIcon from '../../../components/RoundedIcon/RoundedIcon';
import { selectTheme, Theme } from '../../../../store/reducers/themeReducer';
import { useAppSelector } from '../../../../hooks/storeHooks';

type FixedRouteStopDetailProps = {
  routeColour: string;
  routeName: string;
  stopEta: number | null;
  busTowards: string;
  availableCapacity: number;
  shiftSolutionId: string;
  onShiftSolutionPressed: (shiftSolutionId: string) => void;
};

const FixedRouteStopDetail = (props: FixedRouteStopDetailProps) => {
  const theme: Theme = useAppSelector(selectTheme);
  const { t } = useTranslation<['common', 'trip']>(['common', 'trip']);
  const [stopEta, setStopEta] = useState<string>('');

  useEffect(() => {
    if (props.stopEta !== null) {
      setStopEta(calculateStopEta(props.stopEta, t('common:min'), t('common:mins')));
    }
  }, [props.stopEta]);

  return (
    <StyledFixedRouteStopDetailWrapper onClick={() => props.onShiftSolutionPressed(props.shiftSolutionId)}>
      <FirstTwoColumnsWrapper>
        <RoundedIcon
          icon={theme.icons?.fixedRouteVehicleRounded ?? busOutline}
          colour={theme.icons?.fixedRouteVehicleRounded ? props.routeColour : 'light'}
          backgroundColour={props.routeColour}
          iconWidth={40}
          iconSize={theme.icons?.fixedRouteVehicleRounded ? 'large' : 'small'}
          padding={theme.icons?.fixedRouteVehicleRounded ? 0 : 8}
        />
        <StyledMiddleColumn>
          <StyledVehicleNameWrapper>
            <Text weight={600} size={1}>
              {props.routeName}
            </Text>
            <StyledChevronIonIcon icon={chevronForwardOutline} color={'primary'} />
          </StyledVehicleNameWrapper>

          <StyledBusTowardsWrapper>
            <Text size={0.9} weight={500}>
              {t('trip:busTowards', { destination: props.busTowards })}
            </Text>
          </StyledBusTowardsWrapper>

          <StyledCapacityWrapper>
            <StyledImagesAndCapacityWrapper>
              <StyledCapacitySvg height={25} color={capacityColor(props.availableCapacity).themeValue} />
              <Text size={0.8} weight={600} align="center" color={capacityColor(props.availableCapacity).namedValue}>
                {props.availableCapacity
                  ? t('trip:numFree', { count: props.availableCapacity }).toUpperCase()
                  : t('trip:full').toUpperCase()}
              </Text>
            </StyledImagesAndCapacityWrapper>
          </StyledCapacityWrapper>
        </StyledMiddleColumn>
      </FirstTwoColumnsWrapper>
      {props.stopEta !== null && props.stopEta >= 0 && (
        <StyledStopEtaText weight={600} size={0.9} fontFamily={'body'}>
          {stopEta}
        </StyledStopEtaText>
      )}
    </StyledFixedRouteStopDetailWrapper>
  );
};

const StyledStopEtaText = styled(Text)`
  flex: 1;
  margin-left: 10px;
  white-space: nowrap;
`;

const StyledVehicleNameWrapper = styled.div`
  background: transparent;
  border: none;
  flex-direction: row;
  display: flex;
  align-items: center;
`;

const StyledChevronIonIcon = styled(IonIcon)`
  font-size: 14px;
  margin-left: 10px;
`;

const FirstTwoColumnsWrapper = styled.div`
  display: flex;
  flex: 4;
  flex-direction: row;
  align-items: flex-start;
`;

const StyledImagesAndCapacityWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`;

const StyledCapacityWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`;

const StyledCapacitySvg = styled(CapacitySvg)`
  padding: 0 5px 0 2px;
`;

const StyledBusTowardsWrapper = styled.div`
  flex-direction: column;
  align-content: flex-start;
  margin: 5px 0 8px;
`;

const StyledFixedRouteStopDetailWrapper = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 25px;
`;

const StyledMiddleColumn = styled.div`
  margin-left: 16px;
  flex: 1;
`;

export default FixedRouteStopDetail;
