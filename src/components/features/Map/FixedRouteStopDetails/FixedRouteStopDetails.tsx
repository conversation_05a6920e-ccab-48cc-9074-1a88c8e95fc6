import React, { useContext, useEffect, useRef, useState } from 'react';
import styled, { css, FlattenSimpleInterpolation } from 'styled-components';
import { IonIcon } from '@ionic/react';
import { chevronDown, locationSharp } from 'ionicons/icons';
import { useWindowWidth } from '@react-hook/window-size';
import { useTranslation } from 'react-i18next';
import { FixedRoute } from '@liftango/ops-client';
import { BREAKPOINT_MD } from '../../../../constants/breakpoints';
import useStopDetails from '../../../../hooks/useStopDetails';
import Text from '../../../elements/Text/Text';
import FixedRouteStopDetail from './FixedRouteStopDetail';
import BottomSwipeUpCard from '../../../components/BottomSwipeUpCard/BottomSwipeUpCard';
import Button from '../../../../components/elements/Button/Button';
import { useMapContext } from '../../../../context/MapProvider';
import useRunningShiftSolutions from '../../../../hooks/useShiftSolutionLocation';
import InfoLabel from '../../../components/InfoLabel/InfoLabel';
import { ABOVE_MAP_Z_INDEX } from '../../../../constants/positioning';
import { ConfigContext } from '../../../../context/ConfigProvider';

type FixedRouteStopDetailsProps = {
  stopId: string;
  stopName: string;
  stopAddressLabel: string;
  onShiftSolutionPressed: (shiftSolutionId: string) => void;
  className?: string;
};

const FixedRouteStopDetails = (props: FixedRouteStopDetailsProps) => {
  const { t } = useTranslation<['trip', 'common']>(['trip', 'common']);

  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;
  const { networkId } = useContext(ConfigContext);
  const stopDetails: FixedRoute.RunningStopDetails[] = useStopDetails(networkId, props.stopId);
  const [seeMoreButtonShown, setSeeMoreButtonShown] = useState<boolean>(false);
  const [isContentExpanded, setIsContentExpanded] = useState<boolean>(false);
  const [contentSummaryHeight, setContentSummaryHeight] = useState<number>(0);
  const [showStopInactiveInfo, setShowStopInactiveInfo] = useState<boolean>(false);

  const routeStopDetailWrapper = useRef() as React.MutableRefObject<HTMLDivElement>;
  const selectedStopSummary = useRef() as React.MutableRefObject<HTMLDivElement>;
  const infoLabelRef = useRef() as React.MutableRefObject<HTMLDivElement>;

  const { dispatch: dispatchMapAction } = useMapContext();
  const [_, __, checkIsRouteActive] = useRunningShiftSolutions();

  useEffect(() => {
    if (routeStopDetailWrapper.current && routeStopDetailWrapper.current.scrollHeight > routeStopDetailWrapper.current.clientHeight) {
      if (!seeMoreButtonShown && !isContentExpanded) {
        setSeeMoreButtonShown(true);
        setIsContentExpanded(false);
      }
    }
  }, [routeStopDetailWrapper.current?.scrollHeight, stopDetails]);

  useEffect(() => {
    if (selectedStopSummary.current) {
      setContentSummaryHeight(
        selectedStopSummary.current.clientHeight +
          (showStopInactiveInfo && infoLabelRef.current?.clientHeight ? infoLabelRef.current.clientHeight + 10 : 0),
      );
    }
  }, [selectedStopSummary.current?.clientHeight, showStopInactiveInfo]);

  useEffect(() => {
    if (stopDetails.length) {
      //only show if every route associated with the stop is inactive
      setShowStopInactiveInfo(stopDetails.every(({ route: { id } }) => !checkIsRouteActive(id)));
    }
  }, [stopDetails]);

  const StopSummary: JSX.Element | null = stopDetails ? (
    <StyledSelectedRouteSummaryWrapper ref={selectedStopSummary}>
      <StyledStopNameWrapper>
        <IonIcon icon={locationSharp} size="medium" color={'primary'} />
        <StyledStopNameText size={1.0} fontFamily={'header'}>
          {props.stopName.toUpperCase()}
        </StyledStopNameText>
      </StyledStopNameWrapper>
      <Text size={0.8} fontFamily={'body'} align={'center'}>
        {props.stopAddressLabel}
      </Text>
    </StyledSelectedRouteSummaryWrapper>
  ) : null;

  const StopDetails: JSX.Element | null = stopDetails ? (
    <StyledSelectedRoutePointsWrapper isMobile={isMobile}>
      <StyledNextShuttlesText fontFamily={'body'} color={'secondary'} size={isMobile ? 0.8 : 0.9} isMobile={isMobile}>
        {t('trip:nextShuttles').toUpperCase()}
      </StyledNextShuttlesText>

      {showStopInactiveInfo && <InfoLabel ref={infoLabelRef}>{t('common:serviceInactive')}</InfoLabel>}

      <StyledFixedRouteStopDetailWrapper ref={routeStopDetailWrapper} isMobile={isMobile} isContentExpanded={isContentExpanded}>
        {stopDetails.map<JSX.Element | null>((stopDetail: FixedRoute.RunningStopDetails) =>
          stopDetail.stopEta !== null && stopDetail.stopEta >= 0 ? (
            <FixedRouteStopDetail
              key={`${stopDetail.shiftSolutionId}_${stopDetail.stopEta}`}
              shiftSolutionId={stopDetail.shiftSolutionId}
              onShiftSolutionPressed={props.onShiftSolutionPressed}
              routeColour={stopDetail.route.colour}
              routeName={stopDetail.route.label}
              stopEta={stopDetail.stopEta}
              busTowards={stopDetail.route.busTowards}
              availableCapacity={stopDetail.availableCapacities[0].amount}
            />
          ) : null,
        )}
      </StyledFixedRouteStopDetailWrapper>
    </StyledSelectedRoutePointsWrapper>
  ) : null;

  return (
    stopDetails &&
    (isMobile ? (
      <StyledMobileOverviewWrapper>
        <BottomSwipeUpCard isExpandedInitially={false} summary={StopSummary} details={StopDetails} />
      </StyledMobileOverviewWrapper>
    ) : (
      <StyledSelectedStopWrapperDesktop>
        {StopSummary}
        <StyledTopFadingContent contentSummaryHeight={contentSummaryHeight} />
        {StopDetails}
        <StyledBottomFadingContent seeMoreButtonShown={seeMoreButtonShown} />
        {seeMoreButtonShown && (
          <StyledSeeMoreButton
            onClick={() => {
              setSeeMoreButtonShown(false);
              setIsContentExpanded(true);
            }}
          >
            {t('common:seeMore').toUpperCase()}
            <StyledIonIcon icon={chevronDown} />
          </StyledSeeMoreButton>
        )}
        <StyledBackButton
          fill={'solid'}
          block
          onClick={() =>
            dispatchMapAction({
              type: 'SET_SELECTED_STOP',
              payload: null,
            })
          }
        >
          {t('common:back')}
        </StyledBackButton>
      </StyledSelectedStopWrapperDesktop>
    ))
  );
};

const StyledMobileOverviewWrapper = styled.div`
  height: 100%;
  width: 100%;
  padding-bottom: env(safe-area-inset-bottom);
  flex-direction: column;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
`;

const StyledFixedRouteStopDetailWrapper = styled.div<{ isMobile: boolean; isContentExpanded: boolean }>`
  margin-top: 20px;
  width: 100%;

  ${({ isMobile, isContentExpanded }) =>
    !isMobile &&
    css`
      padding: 20px 20px 0 10px;
      overflow: ${isContentExpanded ? 'scroll' : 'hidden'};
      margin-top: 0;
      max-height: calc(${isContentExpanded ? 80 : 40}vh - 112px);
      flex: 1;
    `};
`;

const StyledNextShuttlesText = styled(Text)<{ isMobile: boolean }>`
  display: flex;
  align-items: center;
  width: 100%;
  flex-direction: column;
  margin-bottom: ${({ isMobile }) => (isMobile ? '15px' : 0)};
`;

const StyledSelectedRoutePointsWrapper = styled.div<{ isMobile: boolean }>`
  padding: 15px 0 ${({ isMobile }) => (isMobile ? '10px' : 0)};
  width: 100%;
`;

const StyledStopNameText = styled(Text)`
  margin-left: 5px;
`;

const StyledStopNameWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
  width: 100%;
`;

const StyledSelectedRouteSummaryWrapper = styled.div`
  padding: 10px;
  border-radius: 30px;
  background-color: #f0f0f0;
  flex: 1;
  width: 100%;
  align-content: center;
  justify-content: center;
`;

const StyledSelectedStopWrapperDesktop = styled.div`
  max-height: 90%;
  width: 400px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: unset;
  z-index: ${ABOVE_MAP_Z_INDEX};
  display: flex;
  flex-direction: column;
  margin: 30px;
  border-radius: 30px;
  background-color: white;
  padding: 24px 16px 16px;
`;

const styledFadingContentBaseStyle: FlattenSimpleInterpolation = css`
  position: absolute;
  left: 16px;
  right: 30px;
  height: 30px;
  z-index: 3;
`;

//StyledSelectedStopWrapperDesktop.padding-bottom + StyledBackButton = 64
//StyledSelectedStopWrapperDesktop.padding-bottom + StyledBackButton + StyledSeeMoreButton = 112
const StyledBottomFadingContent = styled.div<{ seeMoreButtonShown: boolean }>`
  ${styledFadingContentBaseStyle}
  bottom: ${({ seeMoreButtonShown }) => (seeMoreButtonShown ? 112 : 64)}px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
`;

//StyledSelectedStopWrapperDesktop.padding-top + StopDetails.padding-top + StyledNextShuttlesText.font-size = 53px
const StyledTopFadingContent = styled.div<{ contentSummaryHeight: number }>`
  ${styledFadingContentBaseStyle}
  top: ${({ contentSummaryHeight }) => contentSummaryHeight + 53}px;
  background: linear-gradient(to top, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
`;

const StyledBackButton = styled(Button)`
  ion-button {
    font-size: 16px !important;
  }
`;

const StyledSeeMoreButton = styled.div`
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  color: gray;
`;

const StyledIonIcon = styled(IonIcon)`
  font-size: 16px;
  padding-left: 5px;
`;

export default FixedRouteStopDetails;
