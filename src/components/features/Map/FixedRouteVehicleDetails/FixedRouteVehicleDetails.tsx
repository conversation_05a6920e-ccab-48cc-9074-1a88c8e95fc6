import React from 'react';
import { useTranslation } from 'react-i18next';
import Tooltip from '../../../elements/ToolTip/Tooltip';
import Text from '../../../elements/Text/Text';
import styled from 'styled-components';
import { calculateStopEta, minutesSingularPlural } from '../../../../helpers/date-format.helpers';
import useNetworkFeatures from '../../../../hooks/useNetworkFeatures';

type FixedRouteVehicleDetailsProps = {
  routeLabel: string;
  availableCapacity: number;
  etaMins: number | null;
  colour: string;
  onClick: () => void;
};

const FixedRouteVehicleDetails: React.FC<FixedRouteVehicleDetailsProps> = (props) => {
  const { t } = useTranslation<['common', 'trip']>(['common', 'trip']);
  const { networkFeatures } = useNetworkFeatures();

  return (
    <StyledVehicleDetails onClick={props.onClick}>
      <Tooltip colour={props.colour} bubblePadding={'0px'} bubbleBorderRadius={40}>
        <StyledTooltipContents>
          {!networkFeatures?.hide_etas && props.etaMins && props.etaMins >= 0 && (
            <StyledEtaInformation>
              <StyledText weight={600} size={0.8} align={'center'}>
                {calculateStopEta(props.etaMins, t('common:min'), t('common:mins'), props.etaMins > 60)}
              </StyledText>
              {props.etaMins <= 60 && (
                <StyledText size={0.6} align={'center'}>
                  {minutesSingularPlural(props.etaMins, t('common:min'), t('common:mins'))}
                </StyledText>
              )}
            </StyledEtaInformation>
          )}
          <StyledRouteInformation>
            <Text size={0.8} weight={600} align={'center'} color={'light'} noWrap={true} maxWidth={'100px'}>
              {props.routeLabel}
            </Text>
          </StyledRouteInformation>
        </StyledTooltipContents>
      </Tooltip>
    </StyledVehicleDetails>
  );
};

const StyledVehicleDetails = styled.button`
  margin-bottom: 95px;
  background-color: transparent;
`;

const StyledTooltipContents = styled.div`
  display: flex;
  justify-content: center;
  align-items: stretch;
  min-height: 30px;
`;

const StyledEtaInformation = styled.div`
  padding: 5px 10px;
  background-color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const StyledRouteInformation = styled.div`
  padding: 5px 10px;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const StyledText = styled(Text)`
  padding-left: 5px;
`;

export default FixedRouteVehicleDetails;
