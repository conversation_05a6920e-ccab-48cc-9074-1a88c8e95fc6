import React from 'react';
import styled, { css } from 'styled-components';
import { useWindowWidth } from '@react-hook/window-size';
import { useTranslation } from 'react-i18next';
import ShiftSolutionSummary from '../../../components/ShiftSolution/ShiftSolutionSummary/ShiftSolutionSummary';
import ShiftSolutionStopPoints from '../../../components/ShiftSolution/ShiftSolutionStopPoints/ShiftSolutionStopPoints';
import useShiftSolutionDetails from '../../../../hooks/useShiftSolutionDetails';
import BottomSwipeUpCard from '../../../components/BottomSwipeUpCard/BottomSwipeUpCard';
import { BREAKPOINT_MD } from '../../../../constants/breakpoints';
import Button from '../../../../components/elements/Button/Button';
import { ABOVE_MAP_Z_INDEX } from '../../../../constants/positioning';

type FixedRouteShiftSolutionDetailsProps = {
  shiftSolutionId: string;
  setSelectedShiftSolutionId: (shiftSolutionId: string | null) => void;
  className?: string;
};

const FixedRouteShiftSolutionDetails = (props: FixedRouteShiftSolutionDetailsProps) => {
  const { t } = useTranslation<['common']>(['common']);

  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  const shiftSolutionDetails = useShiftSolutionDetails(props.shiftSolutionId);

  const SelectedShiftSolutionSummary: JSX.Element | null = shiftSolutionDetails ? (
    <StyledSelectedShiftSolutionSummaryWrapper isMobile={isMobile}>
      <ShiftSolutionSummary
        routeColour={shiftSolutionDetails.route.colour}
        capacity={shiftSolutionDetails.availableCapacities[0].amount}
        routeName={shiftSolutionDetails.route.label}
        driverName={shiftSolutionDetails.driver.name}
        driverImage={shiftSolutionDetails.driver.picture}
        registrationPlate={shiftSolutionDetails.vehicle.registration}
        vehicleName={shiftSolutionDetails.vehicle.name}
        vehicleImage={shiftSolutionDetails.vehicle.picture}
      />
    </StyledSelectedShiftSolutionSummaryWrapper>
  ) : null;

  const SelectedShiftSolutionStopPoints: JSX.Element | null = shiftSolutionDetails ? (
    <StyledStopPoints isMobile={isMobile}>
      <ShiftSolutionStopPoints points={shiftSolutionDetails.stopDetails} routeColour={shiftSolutionDetails.route.colour} />
    </StyledStopPoints>
  ) : null;

  return shiftSolutionDetails ? (
    isMobile ? (
      <StyledMobileOverviewWrapper>
        <BottomSwipeUpCard isExpandedInitially={false} summary={SelectedShiftSolutionSummary} details={SelectedShiftSolutionStopPoints} />
      </StyledMobileOverviewWrapper>
    ) : (
      <StyledShiftSolutionWrapperDesktop>
        {SelectedShiftSolutionSummary}
        {SelectedShiftSolutionStopPoints}
        <StyledBackButton fill={'solid'} onClick={() => props.setSelectedShiftSolutionId(null)} block>
          {t('common:back')}
        </StyledBackButton>
      </StyledShiftSolutionWrapperDesktop>
    )
  ) : null;
};

const StyledMobileOverviewWrapper = styled.div`
  height: 100%;
  width: 100%;
  padding-bottom: env(safe-area-inset-bottom);
  flex-direction: column;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
`;

const StyledSelectedShiftSolutionSummaryWrapper = styled.div<{ isMobile: boolean }>`
  padding: 10px;
  border-radius: ${(props) => (props.isMobile ? 30 : 20)}px;
  background-color: #f0f0f0;
  flex: 1;
  width: 100%;
`;

const StyledStopPoints = styled.div<{ isMobile: boolean }>`
  padding: ${(props) => (props.isMobile ? '30px 0 10px' : '15px 10px 8px 8px')};

  ${(props) =>
    !props.isMobile &&
    css`
      margin-top: 17px;
      margin-right: 6px;
      overflow: auto;
    `}
`;

const StyledShiftSolutionWrapperDesktop = styled.div`
  max-height: 80vh;
  width: 400px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: unset;
  z-index: ${ABOVE_MAP_Z_INDEX};
  display: flex;
  flex-direction: column;
  margin: 30px;
  border-radius: 30px;
  background-color: white;
  padding: 24px 16px 16px;
  box-shadow: 5px 10px 25px rgba(0, 0, 0, 0.1);
`;

const StyledBackButton = styled(Button)`
  padding-top: 10px;
`;

export default FixedRouteShiftSolutionDetails;
