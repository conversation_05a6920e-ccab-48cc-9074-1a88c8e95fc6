import React, { useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FixedRoute } from '@liftango/ops-client';
import { useWindowWidth } from '@react-hook/window-size';
import BottomSwipeUpCard from '../../../components/BottomSwipeUpCard/BottomSwipeUpCard';
import RouteDetailsSummary from '../../../components/Route/RouteDetails/RouteDetailsSummary/RouteDetailsSummary';
import RouteStops from '../../../components/Route/RouteDetails/RouteStops/RouteStops';
import { useAppSelector } from '../../../../hooks/storeHooks';
import { selectRouteById } from '../../../../store/reducers/routesReducer';
import RouteInfo from '../../../components/Route/RouteDetails/RouteInformation/RouteInfo';
import { BREAKPOINT_MD } from '../../../../constants/breakpoints';
import Button from '../../../../components/elements/Button/Button';
import { RouteStopItem } from '../../../components/Route/RouteDetails/RouteStops/RouteStop';
import { ABOVE_MAP_Z_INDEX } from '../../../../constants/positioning';

type FixedRouteRoutesDetailsProps = {
  selectedRouteId: string;
  handleRouteSelect: (routeVersionId: string | null) => void;
  onRouteStopPressed: (stopId: string) => void;
  isActive: boolean;
  className?: string;
};

const FixedRouteRoutesDetails = (props: FixedRouteRoutesDetailsProps) => {
  const { t } = useTranslation<['common']>(['common']);

  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  const [isSwipeUpExpanded, setIsSwipeUpExpanded] = useState<boolean>(false);

  const selectedRoute: FixedRoute.ServiceRoutesAndStopsPayload | null = useAppSelector(selectRouteById(props.selectedRouteId));

  let mappedStops: RouteStopItem[] = [];

  if (selectedRoute) {
    mappedStops = selectedRoute.stops.map<RouteStopItem>((stop: FixedRoute.ServiceRouteStop) => ({
      id: stop.id,
      label: stop.label,
      description: stop.description,
      address: stop.addressLabel,
    }));
  }

  const SwipeUpSummaryMobile: JSX.Element | null = selectedRoute ? (
    <StyledSelectedRouteSummaryWrapperMobile>
      <RouteDetailsSummary
        selectedRouteId={props.selectedRouteId}
        handleRouteSelect={props.handleRouteSelect}
        dropDownSize={isSwipeUpExpanded ? 300 : 120}
      />
    </StyledSelectedRouteSummaryWrapperMobile>
  ) : null;

  const SwipeUpDetailsMobile: JSX.Element | null = selectedRoute ? (
    <StyledSelectedRouteStopsWrapperMobile>
      <RouteStops routeColour={selectedRoute.colour} stops={mappedStops} onRouteStopPressed={props.onRouteStopPressed} />
      <RouteInfo route={selectedRoute} isRouteActive={props.isActive} />
    </StyledSelectedRouteStopsWrapperMobile>
  ) : null;

  return selectedRoute ? (
    isMobile ? (
      <StyledMobileOverviewWrapper>
        <BottomSwipeUpCard
          isExpandedInitially={false}
          summary={SwipeUpSummaryMobile}
          details={SwipeUpDetailsMobile}
          handleExpand={setIsSwipeUpExpanded}
        />
      </StyledMobileOverviewWrapper>
    ) : (
      <StyledSelectedRouteWrapperDesktop>
        <StyledRouteSummaryWrapperDesktop>
          <RouteDetailsSummary selectedRouteId={props.selectedRouteId} handleRouteSelect={props.handleRouteSelect} dropDownSize={368} />
        </StyledRouteSummaryWrapperDesktop>
        <StyledRouteStopsWrapperDesktop>
          <RouteStops routeColour={selectedRoute.colour} stops={mappedStops} onRouteStopPressed={props.onRouteStopPressed} />
        </StyledRouteStopsWrapperDesktop>
        <StyledRouteInfoDesktopWrapper>
          <RouteInfo route={selectedRoute} isRouteActive={props.isActive} />
        </StyledRouteInfoDesktopWrapper>

        <StyledBackButton fill={'solid'} onClick={() => props.handleRouteSelect(null)} block>
          {t('common:back')}
        </StyledBackButton>
      </StyledSelectedRouteWrapperDesktop>
    )
  ) : null;
};

const StyledMobileOverviewWrapper = styled.div`
  height: 100%;
  width: 100%;
  padding-bottom: env(safe-area-inset-bottom);
  flex-direction: column;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
`;

const StyledSelectedRouteStopsWrapperMobile = styled.div`
  padding: 30px 0 10px;
  width: 100%;
`;

const StyledSelectedRouteSummaryWrapperMobile = styled.div`
  padding: 0 10px 10px;
  flex: 1;
  width: 100%;
`;

const StyledSelectedRouteWrapperDesktop = styled.div`
  max-height: 80vh;
  width: 400px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: unset;
  z-index: ${ABOVE_MAP_Z_INDEX};
  display: flex;
  flex-direction: column;
  margin: 30px;
  border-radius: 30px;
  background-color: white;
  padding: 24px 16px 16px;
  box-shadow: 5px 10px 25px rgba(0, 0, 0, 0.1);
`;

const StyledRouteSummaryWrapperDesktop = styled.div`
  padding-bottom: 16px;
`;

const StyledRouteStopsWrapperDesktop = styled.div`
  padding: 15px 10px 8px 8px;
  overflow: auto;
`;

const StyledRouteInfoDesktopWrapper = styled.div`
  border-top: 1px solid #eeeeee;
  padding-bottom: 8px;
  padding-top: 8px;
  margin-top: 16px;
`;

const StyledBackButton = styled(Button)``;

export default FixedRouteRoutesDetails;
