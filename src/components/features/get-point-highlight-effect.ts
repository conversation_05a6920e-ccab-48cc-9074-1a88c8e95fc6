import { css } from 'styled-components';

// CSS animation applied in :before, pulses a ring to highlight the item. Takes colour and position information.
export const getPointHighlightEffect = (highlightColour: string, marginLeft: number, marginTop: number, size: number = 48) => css`
  &:before {
    content: '';
    pointer-events: none;
    position: absolute;
    display: block;
    width: ${size}px;
    height: ${size}px;
    box-sizing: border-box;
    margin-left: ${marginLeft}px;
    margin-top: ${marginTop}px;
    border-radius: 100%;
    border: 2px solid ${highlightColour};
    animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
  }

  @keyframes pulse-ring {
    0% {
      transform: scale(1);
    }
    100% {
      transform: scale(1.5);
      opacity: 0;
    }
  }
`;
