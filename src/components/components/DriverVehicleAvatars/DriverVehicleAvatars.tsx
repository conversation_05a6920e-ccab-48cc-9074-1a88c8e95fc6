import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';

type DriverVehicleAvatarsProps = {
  driverImage: string;
  vehicleImage: string;
  width?: number;
};

const DriverVehicleAvatars = (props: DriverVehicleAvatarsProps) => {
  const { t } = useTranslation<['common', 'trip']>(['common', 'trip']);

  return (
    <StyledDriverVehicleAvatars>
      {props.vehicleImage && <StyledImageLeft src={props.vehicleImage} alt={t('trip:busImageAlt')} width={props.width ?? 45} />}
      {props.driverImage && <StyledImageRight src={props.driverImage} alt={t('trip:driverImageAlt')} width={props.width ?? 45} />}
    </StyledDriverVehicleAvatars>
  );
};

const StyledDriverVehicleAvatars = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
`;

const StyledImageLeft = styled.img<{ width: number }>`
  width: ${(props) => props.width}px;
  height: ${(props) => props.width}px;
  border: 4px solid white;
  border-radius: ${(props) => props.width / 2}px;
  position: relative;
`;

const StyledImageRight = styled.img<{ width: number }>`
  width: ${(props) => props.width}px;
  height: ${(props) => props.width}px;
  border: 4px solid white;
  border-radius: ${(props) => props.width / 2}px;
  margin-left: -7%;
  position: relative;
`;

export default DriverVehicleAvatars;
