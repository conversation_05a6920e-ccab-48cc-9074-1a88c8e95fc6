import React from 'react';
import styled from 'styled-components';
import { IonIcon } from '@ionic/react';
import { addOutline, personOutline, removeOutline } from 'ionicons/icons';
import Text from '../../elements/Text/Text';

type PassengerIncrementorProps = {
  value: number;
  onChange: (count: number) => void;
  step?: number;
  min?: number;
  max?: number;
  className?: string;
};

const PassengerIncrementor = (props: PassengerIncrementorProps) => {
  const step: number = props.step ?? 1;

  const handleChange = (newValue: number) => {
    if ((typeof props.min === 'undefined' || newValue >= props.min) && (typeof props.max === 'undefined' || newValue <= props.max)) {
      props.onChange(newValue);
    }
  };

  const handleDecrement = () => {
    handleChange(props.value - step);
  };

  const handleIncrement = () => {
    handleChange(props.value + step);
  };

  return (
    <StyledPassengerIncrementor className={props.className}>
      <StyledPassengerIncrementorButtonWrapper onClick={handleDecrement}>
        <IonIcon icon={removeOutline} />
      </StyledPassengerIncrementorButtonWrapper>

      <StyledPassengerIncrementorTextWrapper>
        <IonIcon icon={personOutline} style={{ fontSize: '23px' }} />
        <StyledPassengerIncrementorText weight={600}>{props.value}</StyledPassengerIncrementorText>
      </StyledPassengerIncrementorTextWrapper>

      <StyledPassengerIncrementorButtonWrapper onClick={handleIncrement}>
        <IonIcon icon={addOutline} />
      </StyledPassengerIncrementorButtonWrapper>
    </StyledPassengerIncrementor>
  );
};

const StyledPassengerIncrementor = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-column-gap: 3px;

  ion-icon {
    font-size: 24px;
  }
`;

const StyledPassengerIncrementorButtonWrapper = styled.div`
  cursor: pointer;
  padding: 3px;
  display: flex;
  align-items: center;

  &:hover {
    color: black;
  }
`;

const StyledPassengerIncrementorTextWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row;
  min-width: 38px;
`;

const StyledPassengerIncrementorText = styled(Text)`
  margin-left: 1px;
`;

export default PassengerIncrementor;
