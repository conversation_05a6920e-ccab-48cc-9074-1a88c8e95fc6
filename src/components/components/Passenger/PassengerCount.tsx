import React from 'react';
import styled from 'styled-components';
import { IonIcon } from '@ionic/react';
import { useTranslation } from 'react-i18next';
import { personOutline } from 'ionicons/icons';
import Text from '../../elements/Text/Text';

type PassengerCountProps = {
  count: number;
  className?: string;
};

const PassengerCount = (props: PassengerCountProps) => {
  const { t } = useTranslation<['trip']>(['trip']);

  return (
    <StyledPassengerCount className={props.className}>
      <IonIcon icon={personOutline} size="small" />
      <StyledPassengerCountText size={0.8} weight={500} align="left">
        {t('trip:passengerWithCount', { count: props.count })}
      </StyledPassengerCountText>
    </StyledPassengerCount>
  );
};

const StyledPassengerCount = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
`;

const StyledPassengerCountText = styled(Text)`
  margin-left: 13px;
`;

export default PassengerCount;
