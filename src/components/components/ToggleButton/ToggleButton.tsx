import React from 'react';
import styled from 'styled-components';
import Text from '../../elements/Text/Text';
import { IonIcon } from '@ionic/react';
import ToggleButtonWrapper from './ToggleButtonWrapper';
import { IonicReactProps } from '@ionic/react/dist/types/components/IonicReactProps';

type ToggleButtonProps = {
  label: string;
  onClick: () => void;
  isActive: boolean;
  icon?: string;
  iconSize?: string;
  textSize?: number;
  className?: string;
  style?: IonicReactProps['style'];
};

const ToggleButton = (props: ToggleButtonProps) => {
  const size: string = props.iconSize || 'small';
  return (
    <StyledToggleButton className={props.className} onClick={props.onClick} isActive={props.isActive}>
      {props.icon && <IonIcon icon={props.icon} size={size} style={props.style} />}
      <StyledToggleButtonText size={props.textSize ?? 0.9} weight={600} align="left" hasIcon={!!props.icon}>
        {props.label}
      </StyledToggleButtonText>
    </StyledToggleButton>
  );
};

const StyledToggleButton = styled(ToggleButtonWrapper)`
  cursor: pointer;
`;

const StyledToggleButtonText = styled(Text)<{ hasIcon: boolean }>`
  letter-spacing: 0;
  margin-left: 8px;
  font-family: var(--ion-font-family-header);
  ${({ hasIcon }) => !hasIcon && `margin-right: 8px;`}
`;

export default ToggleButton;
