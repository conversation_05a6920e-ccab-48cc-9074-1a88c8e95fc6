import React, { ReactNode } from 'react';
import styled from 'styled-components';

type ToggleWrapperProps = {
  children: ReactNode;
  isActive: boolean;
  onClick?: () => void;
  className?: string;
};

const ToggleWrapper = (props: ToggleWrapperProps) => {
  return (
    <StyledToggleWrapper className={props.className} onClick={props.onClick} isActive={props.isActive}>
      {props.children}
    </StyledToggleWrapper>
  );
};

const StyledToggleWrapper = styled.div<{ isActive: boolean }>`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 41px;
  background: #ffffff 0% 0% no-repeat padding-box;
  padding: 0 10px;

  ${({ isActive }) =>
    isActive &&
    `
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.29);
    border-radius: 19px;
    transform: scale(1.2);
  `}

  &:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.5);
    border-radius: 19px;
  }

  ion-icon {
    --ionicon-stroke-width: 40px;
  }
`;

export default ToggleWrapper;
