import React from 'react';
import ReactDOM from 'react-dom';

class AutoCompleteSuggestions extends React.Component {
  el: any;

  constructor(props: any) {
    super(props);
    this.el = document.createElement('div');

    this.el.style = 'overflow: auto;';
  }

  componentDidMount() {
    // The portal element is inserted in the DOM tree after
    // the Modal's children are mounted, meaning that children
    // will be mounted on a detached DOM node. If a child
    // component requires to be attached to the DOM tree
    // immediately when mounted, for example to measure a
    // DOM node, or uses 'autoFocus' in a descendant, add
    // state to Modal and only render the children when Modal
    // is inserted in the DOM tree.
    const autoCompleteSuggestionsRoot = document.getElementById('autocomplete-suggestions-root');
    if (autoCompleteSuggestionsRoot) {
      autoCompleteSuggestionsRoot.appendChild(this.el);
    }
  }

  componentWillUnmount() {
    const autoCompleteSuggestionsRoot = document.getElementById('autocomplete-suggestions-root');
    if (autoCompleteSuggestionsRoot && this.el.parentElement === autoCompleteSuggestionsRoot) {
      autoCompleteSuggestionsRoot.removeChild(this.el);
    }
  }

  render() {
    return ReactDOM.createPortal(this.props.children, this.el);
  }
}

export default AutoCompleteSuggestions;
