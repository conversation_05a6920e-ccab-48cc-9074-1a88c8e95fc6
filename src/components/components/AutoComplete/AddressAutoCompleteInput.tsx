import React, { useMemo, useEffect, useState, useRef, ReactNode, useContext } from 'react';
import PlacesAutocomplete, { geocodeByAddress, geocodeByPlaceId, getLatLng } from 'react-places-autocomplete';
import { useWindowWidth } from '@react-hook/window-size';
import styled, { css } from 'styled-components';
import { closeOutline } from 'ionicons/icons';
import { IonIcon } from '@ionic/react';

import { useAppSelector } from '../../../hooks/storeHooks';
import { selectNetworkEnforced } from '../../../store/reducers/networkReducer';
import AutoCompleteSuggestions from './AutoCompleteSuggestions';
import SuggestedAddress from '../SuggestedAddress/SuggestedAddress';
import { BREAKPOINT_MD } from '../../../constants/breakpoints';
import { ConfigContext } from '../../../context/ConfigProvider';
import useQueryParam from '../../../hooks/useQueryParam';

export type AutoCompleteAddress = {
  formatted_address: string;
  place_id: string;
};

type LatLng = {
  lat: number;
  lng: number;
};

export type AutoCompleteLatLng = {
  latitude: number;
  longitude: number;
};

export type AutoCompleteSetAddress = AutoCompleteLatLng & {
  label: string;
  placeId: string;
};

export type AutoCompleteExtraAddress = AutoCompleteSetAddress & {
  icon: string;
  name: string;
  colour?: string;
};

type AutoCompleteInputProps = {
  value: string;
  onSetAddress: (address: AutoCompleteSetAddress) => void;
  hasLabel: boolean;
  setHasLabel: (hasLabel: boolean) => void;
  extraAddresses?: AutoCompleteExtraAddress[];
  forwardedRef?: React.RefObject<PlacesAutocomplete>;
};

export const HEIGHT_OF_THE_TOP_PART_OF_THE_SCREEN_MOBILE = 297;
const HEIGHT_OF_THE_TOP_PART_OF_THE_SCREEN_DESKTOP = 258;

const AddressAutoCompleteInput = ({
  value,
  onSetAddress,
  extraAddresses,
  forwardedRef: autoCompleteRef,
  hasLabel,
  setHasLabel,
  ...props
}: AutoCompleteInputProps) => {
  const { useGoogleMapsAddresses } = useContext(ConfigContext);
  const network = useAppSelector(selectNetworkEnforced);
  const [address, setAddress] = useState<string>(value);
  const [showCrossIcon, setShowCrossIcon] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const inputRef = useRef() as React.MutableRefObject<HTMLInputElement>;
  const [numberOfSuggestions, setNumberOfSuggestions] = useState<number>(0);
  const [isScrollBarVisible, setIsScrollBarVisible] = useState<boolean>(false);
  const autocomplete = document.getElementById('autocomplete-suggestions-root');
  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  const topQueryParam: string = useQueryParam('top');
  const paramMarginTop: number = topQueryParam ? Number(topQueryParam) : 0;

  useEffect(() => {
    setAddress(value);
  }, [value]);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (inputRef.current?.value && isFocused) {
      setShowCrossIcon(true);
      setHasLabel(true);
      return;
    }
    if (!inputRef.current?.value && isFocused) {
      setShowCrossIcon(false);
      setHasLabel(false);
      return;
    }
    if (!inputRef.current?.value && !isFocused) {
      setHasLabel(false);
      return;
    }
    timeoutId = setTimeout(() => {
      setShowCrossIcon(false);
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [inputRef.current, inputRef.current?.value, isFocused]);

  useEffect(() => {
    if (autocomplete) {
      if (isMobile) {
        autocomplete.style.height = numberOfSuggestions
          ? `calc(100vh - ${HEIGHT_OF_THE_TOP_PART_OF_THE_SCREEN_MOBILE + paramMarginTop}px)`
          : '0px';
      } else {
        autocomplete.style.maxHeight = numberOfSuggestions ? `calc(100vh - ${HEIGHT_OF_THE_TOP_PART_OF_THE_SCREEN_DESKTOP}px)` : '0px';
      }
    }
  }, [numberOfSuggestions]);

  useEffect(() => {
    if (
      !isScrollBarVisible &&
      autocomplete &&
      autocomplete.firstElementChild &&
      autocomplete.firstElementChild.scrollHeight > autocomplete.firstElementChild.clientHeight
    ) {
      setIsScrollBarVisible(true);
    }
    if (
      isScrollBarVisible &&
      autocomplete &&
      autocomplete.firstElementChild &&
      autocomplete.firstElementChild.scrollHeight === autocomplete.firstElementChild.clientHeight
    ) {
      setIsScrollBarVisible(false);
    }
  }, [autocomplete?.firstElementChild?.scrollHeight]);

  // if there is a network address, bias the Places API
  const searchOptions = useMemo(() => {
    return {
      location: new google.maps.LatLng(network.address.latitude, network.address.longitude),
      radius: 25000, // metres
    };
  }, [network.address]);

  const handleChange = (address: string) => {
    setAddress(address);
  };

  const setPlaceResults = async (results: google.maps.GeocoderResult[]) => {
    const result: google.maps.GeocoderResult = results[0];
    const latLng: LatLng = await getLatLng(result);

    setAddress(result.formatted_address);
    onSetAddress({
      label: result.formatted_address,
      placeId: result.place_id,
      latitude: latLng.lat,
      longitude: latLng.lng,
    });
  };

  const findAddressDetails = (selectedAddress: string) => {
    if (selectedAddress) {
      geocodeByAddress(selectedAddress)
        .then(setPlaceResults)
        .catch((error: Error) => console.error('Error', error));
    }
  };

  const findPlaceDetails = (selectedAddress: string, placeId: string) => {
    if (placeId) {
      geocodeByPlaceId(placeId)
        .then(setPlaceResults)
        .catch(() => findAddressDetails(selectedAddress));
    } else if (selectedAddress) {
      findAddressDetails(selectedAddress);
    }
  };

  const handleSelect = (selectedAddress: string, placeId: string) => {
    if (useGoogleMapsAddresses) {
      findPlaceDetails(selectedAddress, placeId);
    }
  };

  const onError = (status: any) => {
    console.error('Google Maps API returned error with status: ', status);
  };

  const handleClearInput = (callBack: (event: { target: { value: string } }) => void) => {
    inputRef.current?.focus();
    callBack({ target: { value: '' } });
  };

  const searchFilter = (extraAddress: string): boolean => extraAddress.toLowerCase().includes(address.toLowerCase());

  // Extra addresses are stops and hubs which don't come from the google maps api
  const filteredExtraAddresses: AutoCompleteExtraAddress[] =
    extraAddresses?.filter((extraAddress) => searchFilter(extraAddress.name)) ?? [];

  return (
    <PlacesAutocomplete
      highlightFirstSuggestion
      ref={autoCompleteRef}
      value={address}
      onChange={handleChange}
      onSelect={handleSelect}
      onError={onError}
      searchOptions={searchOptions}
      shouldFetchSuggestions={useGoogleMapsAddresses}
    >
      {({ getInputProps, suggestions, getSuggestionItemProps }) => {
        const inputProps = getInputProps(props);

        setNumberOfSuggestions(suggestions.length + (isFocused ? filteredExtraAddresses.length : 0));

        return (
          <StyledAutoComplete>
            <StyledAutoCompleteInputWrapper>
              <StyledAutoCompleteInput
                {...inputProps}
                ref={inputRef}
                onBlur={() => setIsFocused(false)}
                onFocus={() => {
                  setIsFocused(true);
                  // @ts-ignore onFocus is passed down
                  props.onFocus();
                }}
                showCrossIcon={showCrossIcon}
                hasLabel={hasLabel}
              />
              {showCrossIcon && (
                <StyledCrossIconWrapper onClick={() => handleClearInput(inputProps.onChange)}>
                  <IonIcon icon={closeOutline} />
                </StyledCrossIconWrapper>
              )}
            </StyledAutoCompleteInputWrapper>

            <AutoCompleteSuggestions>
              {isFocused && (
                <StyledAutoCompleteWrapper isMobile={isMobile} isScrollBarVisible={isScrollBarVisible}>
                  {suggestions.map((suggestion, index) => {
                    return (
                      <StyledAutoCompleteSuggestion
                        {...getSuggestionItemProps(suggestion)}
                        key={suggestion.placeId}
                        icon="defaultAlt"
                        title={suggestion.formattedSuggestion.mainText}
                        address={suggestion.description}
                        isMobile={isMobile}
                        isFirstElement={!index}
                        isLastElement={index === filteredExtraAddresses.length - 1}
                        isScrollBarVisible={isScrollBarVisible}
                      />
                    );
                  })}

                  {Array.isArray(extraAddresses)
                    ? filteredExtraAddresses.map<ReactNode>((extraAddress: AutoCompleteExtraAddress, index) => (
                        <StyledAutoCompleteSuggestion
                          isMobile={isMobile}
                          isFirstElement={!index}
                          isLastElement={index === filteredExtraAddresses.length - 1}
                          isScrollBarVisible={isScrollBarVisible}
                          icon={extraAddress.icon}
                          colour={extraAddress.colour}
                          title={extraAddress.name}
                          address={extraAddress.label}
                          onClick={() => {
                            onSetAddress(extraAddress);
                            // @ts-ignore - this exists
                            if (autoCompleteRef.current && typeof autoCompleteRef.current.clearSuggestions === 'function') {
                              // @ts-ignore
                              autoCompleteRef.current.clearSuggestions();
                            }
                          }}
                          key={extraAddress.placeId}
                        />
                      ))
                    : null}
                </StyledAutoCompleteWrapper>
              )}
            </AutoCompleteSuggestions>
          </StyledAutoComplete>
        );
      }}
    </PlacesAutocomplete>
  );
};

const StyledAutoComplete = styled.div``;

const StyledAutoCompleteWrapper = styled.div<{ isMobile: boolean; isScrollBarVisible: boolean }>`
  flex: 1;

  ${({ isMobile, isScrollBarVisible }) =>
    isMobile
      ? css`
          height: 300px;
          padding-bottom: 500px;
        `
      : css`
          margin: 15px ${isScrollBarVisible ? 0 : '15px'} 15px 15px;
        `};
  overflow: scroll;
`;

const StyledAutoCompleteInputWrapper = styled.div`
  position: relative;
`;

const StyledAutoCompleteInput = styled.input<{ showCrossIcon: boolean; hasLabel: boolean }>`
  border: none;
  font-family: var(--lifty-input--font-family);
  padding: 0;
  padding-right: ${(props) => (props.showCrossIcon ? 20 : 0)}px;
  color: #000;
  font-size: ${(props) => (props.hasLabel ? 'inherit' : '24px')};
  font-style: inherit;
  font-weight: inherit;
  letter-spacing: inherit;
  text-decoration: inherit;
  text-indent: inherit;
  text-overflow: inherit;
  text-transform: inherit;
  text-align: inherit;
  white-space: inherit;
  display: inline-block;
  flex: 1;
  width: 100%;
  max-width: 100%;
  max-height: 100%;
  border: 0;
  outline: none;
  background: transparent;
  box-sizing: border-box;
  appearance: none;

  &::placeholder {
    color: var(--ion-color-primary);
    font-family: inherit;
    font-style: inherit;
    font-weight: inherit;
    opacity: 1;
  }

  &:focus {
    &::placeholder {
      opacity: 0.5;
    }
  }
`;

const StyledCrossIconWrapper = styled.div`
  position: absolute;
  height: 100%;
  width: 16px;
  top: 0;
  right: 0;
`;

const StyledAutoCompleteSuggestion = styled(SuggestedAddress)<{
  isFirstElement: boolean;
  isLastElement: boolean;
  isMobile: boolean;
  isScrollBarVisible: boolean;
}>`
  width: 100%;
  padding: 10px ${({ isMobile }) => (isMobile ? '50px' : '10px')};
  cursor: pointer;
  border-top: 1px solid ${({ isFirstElement }) => (isFirstElement ? 'white' : 'var(--ion-border-color)')};

  &:hover {
    background-color: var(--ion-border-color);
    border-top-left-radius: ${({ isFirstElement }) => (isFirstElement ? '15px' : 0)};
    border-bottom-left-radius: ${({ isLastElement }) => (isLastElement ? '15px' : 0)};
    border-top-right-radius: ${({ isFirstElement, isScrollBarVisible }) => (isFirstElement && !isScrollBarVisible ? '15px' : 0)};
    border-bottom-right-radius: ${({ isLastElement, isScrollBarVisible }) => (isLastElement && !isScrollBarVisible ? '15px' : 0)};
  }

  &:active {
    opacity: 0.8;
  }
`;

export default AddressAutoCompleteInput;
