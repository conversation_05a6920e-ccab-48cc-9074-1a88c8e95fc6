import React, { useState, useRef, useEffect } from 'react';
import { SwipeableHandlers, useSwipeable } from 'react-swipeable';
import styled, { keyframes, Keyframes } from 'styled-components';
import { BREAKPOINT_MD } from '../../../constants/breakpoints';
import { ABOVE_MAP_Z_INDEX } from '../../../constants/positioning';

type BottomSwipeUpCardProps = {
  isExpandedInitially: boolean;
  summary: JSX.Element | null;
  details: JSX.Element | null;
  className?: string;
  handleExpand?: (isExpanded: boolean) => void;
};

const COLLAPSED_MAX_HEIGHT: number = 250;

const DRAG_INDICATOR_WRAPPER_PADDING = {
  TOP: 10,
  BOTTOM: 20,
};

const DRAG_INDICATOR_WRAPPER_HEIGHT = DRAG_INDICATOR_WRAPPER_PADDING.BOTTOM + DRAG_INDICATOR_WRAPPER_PADDING.BOTTOM;

const BottomSwipeUpCard = (props: BottomSwipeUpCardProps) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(props.isExpandedInitially);
  const [isExpandable, setIsExpandable] = useState<boolean>(false);
  const [cardHeight, setCardHeight] = useState<number>(0);

  const summaryWrapperRef: React.RefObject<HTMLDivElement> = useRef<HTMLDivElement>(null);
  const detailsWrapperRef: React.RefObject<HTMLDivElement> = useRef<HTMLDivElement>(null);

  const dragIndicatorHandlers: SwipeableHandlers = useSwipeable({
    onSwipedUp: () => {
      if (!isExpanded && isExpandable) {
        setIsExpanded(true);
      }
    },
    onSwipedDown: () => {
      if (isExpanded && isExpandable) {
        setIsExpanded(false);
      }
    },
  });

  useEffect(() => {
    if (summaryWrapperRef.current?.clientHeight && detailsWrapperRef.current?.scrollHeight) {
      const contentHeight = summaryWrapperRef.current?.clientHeight + detailsWrapperRef.current?.scrollHeight;

      if (contentHeight + DRAG_INDICATOR_WRAPPER_HEIGHT > COLLAPSED_MAX_HEIGHT) {
        setIsExpandable(true);
      } else {
        setIsExpandable(false);
      }

      setCardHeight(contentHeight + DRAG_INDICATOR_WRAPPER_HEIGHT);
    }
  }, [props.summary, isExpanded]);

  useEffect(() => {
    if (props.handleExpand) {
      props.handleExpand(isExpanded);
    }
  }, [isExpanded]);

  return (
    <StyledCardWrapper isExpanded={isExpanded} contentHeight={cardHeight}>
      <StyledDragIndicatorWrapper {...dragIndicatorHandlers}>
        <StyledDragIndicator />
      </StyledDragIndicatorWrapper>

      <StyledCardContentWrapper isExpanded={isExpanded}>
        <StyledSummaryWrapper ref={summaryWrapperRef}>{props.summary}</StyledSummaryWrapper>
        <StyledDetailsWrapper ref={detailsWrapperRef}>{props.details}</StyledDetailsWrapper>
      </StyledCardContentWrapper>
    </StyledCardWrapper>
  );
};

const onLoadAnimation = (): Keyframes => keyframes`
  0% {
    transform: translateY(calc(100%));
  }
  100% {
    transform: translateY(0);
  }
`;

const StyledCardWrapper = styled.div<{ isExpanded: boolean; contentHeight: number }>`
  height: 100%;
  width: 100%;
  flex: 1;
  padding: 0 10px (30 + env(safe-area-inset-bottom)) px;

  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  background-color: white;
  max-height: ${({ isExpanded }) => (isExpanded ? '70%' : `250px`)};
  transition: all 150ms linear;
  overflow: hidden;
  animation: ${onLoadAnimation()} 300ms linear;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
  z-index: ${ABOVE_MAP_Z_INDEX};

  @media all and (min-width: ${BREAKPOINT_MD}px) {
    display: none;
  }
`;

const StyledDragIndicatorWrapper = styled.div`
  width: 100%;
  height: 5px;
  padding-top: ${DRAG_INDICATOR_WRAPPER_PADDING.TOP}px;
  padding-bottom: ${DRAG_INDICATOR_WRAPPER_PADDING.BOTTOM}px;
`;

const StyledDragIndicator = styled.div`
  width: 38px;
  height: 5px;
  background-color: #e3e3e3;
  border-radius: 30px;
  margin: 0 auto 0;
`;

const StyledCardContentWrapper = styled.div<{ isExpanded: boolean }>`
  padding: 0px 10px 0px 10px;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  width: 100%;
  height: 100%;
  overflow-y: ${(props) => (props.isExpanded ? 'auto' : 'hidden')};
  overflow-x: hidden;
`;

const StyledSummaryWrapper = styled.div``;

const StyledDetailsWrapper = styled.div``;

export default BottomSwipeUpCard;
