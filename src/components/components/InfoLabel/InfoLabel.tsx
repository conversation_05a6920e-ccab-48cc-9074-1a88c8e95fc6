import React from 'react';
import styled from 'styled-components';
import Text from '../../elements/Text/Text';
import { IonIcon } from '@ionic/react';
import { informationCircle } from 'ionicons/icons';

type InfoLabelProps = {
  children: string | number;
};

const InfoLabel = React.forwardRef((props: InfoLabelProps, ref: React.ForwardedRef<HTMLDivElement>) => {
  return (
    <StyledInactiveInfoLabelBox ref={ref}>
      <StyledInfoIcon icon={informationCircle} />
      <StyledInactiveInfoLabelBoxLabel weight={600} size={0.9}>
        {props.children}
      </StyledInactiveInfoLabelBoxLabel>
    </StyledInactiveInfoLabelBox>
  );
});

const StyledInactiveInfoLabelBox = styled.div`
  width: 100%;
  height: 48px;
  background-color: var(--ion-color-info-tint);
  margin-top: 10px;
  display: flex;
  align-items: center;
`;

const StyledInactiveInfoLabelBoxLabel = styled(Text)`
  padding: 5px;
`;

const StyledInfoIcon = styled(IonIcon)`
  color: var(--ion-color-info);
  font-size: 24px;
  padding: 8px;
`;

export default InfoLabel;
