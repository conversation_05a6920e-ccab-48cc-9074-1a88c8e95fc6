import React, { createRef, ReactNode, useEffect, useMemo, useRef } from 'react';
import styled from 'styled-components';
import { addDays, isSameDay } from 'date-fns';
import StripCalendarDay from './StripCalendarDay';
import useScrollable from '../../../hooks/useScrollable';
import useLocalisedDate from '../../../hooks/useLocalisedDate';

type StripCalendarListItem = {
  ref: React.RefObject<HTMLDivElement>;
  key: string;
  node: ReactNode;
};

type StripCalendarProps = {
  calendarDaysAhead: number;
  calendarDaysBehind: number;
  selectedDate: Date;
  setSelectedDate: (selectedDateTime: Date) => void;
  enabledDates?: string[];
  className?: string;
};

const today: Date = new Date();

const StripCalendar = (props: StripCalendarProps) => {
  const { localiseDate } = useLocalisedDate();
  const sliderRef = useRef() as React.MutableRefObject<HTMLDivElement>;

  const calendarDays: StripCalendarListItem[] = useMemo<StripCalendarListItem[]>((): StripCalendarListItem[] => {
    const days: StripCalendarListItem[] = [];

    for (let i = props.calendarDaysBehind; i <= props.calendarDaysAhead; i++) {
      const currentDay: Date = addDays(today, i);

      const key: string = currentDay.toISOString();
      const ref: React.RefObject<HTMLDivElement> = createRef<HTMLDivElement>();

      const isDateAllowed: boolean = Array.isArray(props.enabledDates)
        ? props.enabledDates.some((date: string) => isSameDay(new Date(date), currentDay))
        : true;

      days.push({
        ref,
        key,
        node: (
          <StyledStripCalendarDay
            ref={ref}
            key={key}
            title={localiseDate(currentDay, 'd')}
            label={localiseDate(currentDay, 'eee')}
            status={!isDateAllowed ? 'disabled' : isSameDay(currentDay, props.selectedDate) ? 'active' : undefined}
            onSelectDay={() => props.setSelectedDate(currentDay)}
          />
        ),
      });
    }

    return days;
  }, [props.selectedDate]);

  useEffect(() => {
    const key: string = props.selectedDate.toISOString();
    const dayItem: StripCalendarListItem | undefined = calendarDays.find((day: StripCalendarListItem) => day.key === key);

    if (dayItem?.ref.current && sliderRef.current) {
      sliderRef.current.scrollTo({
        left: dayItem.ref.current.offsetLeft - 110,
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [props.selectedDate, calendarDays]);

  // useEffect(() => {
  //   let isDown = false;
  //   let startX: number = 0;
  //   let scrollLeft: number = 0;
  //
  //   if (sliderRef.current) {
  //     sliderRef.current.addEventListener('mousedown', (e: any) => {
  //       isDown = true;
  //       sliderRef.current.classList.add('active');
  //       startX = e.pageX - 0;
  //       scrollLeft = sliderRef.current.scrollLeft;
  //     });
  //     sliderRef.current.addEventListener('mouseleave', () => {
  //       isDown = false;
  //       sliderRef.current.classList.remove('active');
  //     });
  //     sliderRef.current.addEventListener('mouseup', () => {
  //       isDown = false;
  //       sliderRef.current.classList.remove('active');
  //     });
  //     sliderRef.current.addEventListener('mousemove', (e: any) => {
  //       if (!isDown) return;
  //       e.preventDefault();
  //       const x = e.pageX - 0;
  //       const walk = (x - startX) * 3;
  //       sliderRef.current.scrollLeft = scrollLeft - walk;
  //     });
  //   }
  // }, []);

  useScrollable(sliderRef, undefined, { horizontal: true });

  return (
    <StyledStripCalendar ref={sliderRef} className={props.className}>
      {calendarDays.map((day: StripCalendarListItem) => day.node)}
    </StyledStripCalendar>
  );
};

const StyledStripCalendar = styled.div`
  margin: 0 20px;
  padding: 20px 10px;

  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  transition: all 0.2s;
  will-change: transform;
  user-select: none;
  cursor: pointer;

  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge add Firefox */
  & {
    -ms-overflow-style: none;
    scrollbar-width: none; /* Firefox */
  }
`;

const StyledStripCalendarDay = styled(StripCalendarDay)`
  margin: 0 5px;
`;

export default StripCalendar;
