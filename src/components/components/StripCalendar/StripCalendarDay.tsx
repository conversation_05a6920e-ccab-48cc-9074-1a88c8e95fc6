import React from 'react';
import styled, { css } from 'styled-components';
import Text from '../../elements/Text/Text';

type StripCalendarDayProps = {
  title: string;
  label: string;
  onSelectDay: () => void;
  status?: 'active' | 'disabled';
  className?: string;
};

const StripCalendarDay = React.forwardRef((props: StripCalendarDayProps, ref: React.ForwardedRef<HTMLDivElement>) => {
  const handleDayClick = () => {
    if (props.status !== 'disabled') {
      props.onSelectDay();
    }
  };

  return (
    <StyledStripCalendarDay ref={ref} className={props.className} status={props.status} onClick={handleDayClick}>
      <StyledStripCalendarTitle size={1} weight={700} color={props.status === 'disabled' ? 'step-800' : undefined}>
        {props.title}
      </StyledStripCalendarTitle>
      <StyledStripCalendarLabel size={0.8} weight={500} color={props.status === 'disabled' ? 'step-800' : undefined}>
        {props.label}
      </StyledStripCalendarLabel>
    </StyledStripCalendarDay>
  );
});

const StyledStripCalendarDay = styled.div<{ status?: 'active' | 'disabled' }>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border-radius: 10px;
  padding: 5px 10px;

  ${(props) =>
    props.status === 'active'
      ? css`
          transform: scale(1.2);
          background: #ffffff 0% 0% no-repeat padding-box;
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.29);
          border-radius: 9px;
        `
      : props.status === 'disabled'
      ? css`
          color: #d0d0d0;
        `
      : ''}
`;

const StyledStripCalendarTitle = styled(Text)``;

const StyledStripCalendarLabel = styled(Text)``;

export default StripCalendarDay;
