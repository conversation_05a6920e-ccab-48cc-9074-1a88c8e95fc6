import React from 'react';
import styled from 'styled-components';
import { IonSpinner } from '@ionic/react';
import LottiePlayer from 'lottie-react';
import { selectTheme, Theme } from '../../../store/reducers/themeReducer';
import { useAppSelector } from '../../../hooks/storeHooks';

type LoadingMaskProps = {
  className?: string;
};

const LoadingMask = (props: LoadingMaskProps) => {
  const theme: Theme = useAppSelector(selectTheme);

  return (
    <StyledLoadingMask className={props.className}>
      {theme.loadingMask ? (
        <StyledLoaderWrapper>
          <LottiePlayer animationData={JSON.parse(theme.loadingMask)} />
        </StyledLoaderWrapper>
      ) : (
        <IonSpinner name="crescent" color="primary" />
      )}
    </StyledLoadingMask>
  );
};

const StyledLoadingMask = styled.div`
  flex: 1;
  padding: 5rem;
  display: flex;
  align-items: center;
  justify-content: center;

  ion-spinner {
    width: 3rem;
    height: 3rem;
  }
`;

const StyledLoaderWrapper = styled.div`
  width: 3rem;
  height: 3rem;
`;

export default LoadingMask;
