import React from 'react';
import styled from 'styled-components';
import RouteSummaryVehicleLabel from '../Route/RouteSummary/RouteSummaryVehicleLabel';
import Button from '../../elements/Button/Button';
import Text from '../../elements/Text/Text';
import { IonCard, IonIcon } from '@ionic/react';
import { FALLBACK_DARK_COLOUR, getColourName } from '../../../helpers/colour.helpers';
import WarningIcon from '../../elements/Icon/WarningIcon';
import { ABOVE_MAP_Z_INDEX } from '../../../constants/positioning';

type DirectionsSlideProps = {
  title: string;
  icon?: string;
  subtitle?: string;
  vehicle?: {
    image: string;
    number: string;
    displayName: string;
    route: {
      label: string;
      colour: string;
    };
  };
  button?: {
    action: () => void;
    actionLabel: string;
  };
  className?: string;
  isLongWalk?: boolean;
};

const DirectionsSlide = (props: DirectionsSlideProps) => {
  return (
    <StyledDirectionsSlide className={props.className}>
      {props.vehicle?.image ? (
        <StyledDirectionsSlideImageContainer imgSrc={props.vehicle.image} />
      ) : props.icon ? (
        <StyledDirectionsSlideIcon colour={props.vehicle?.route.colour}>
          <IonIcon icon={props.icon} />
        </StyledDirectionsSlideIcon>
      ) : null}

      <StyledDirectionsSlideContent>
        <StyledDirectionsSlideTitleContainer>
          <StyledDirectionsSlideTitle size={1.35}>{props.title}</StyledDirectionsSlideTitle>

          {props.vehicle && (
            <StyledDirectionsSlideVehicleLabel
              labelExpanded={`${props.vehicle.displayName} - ${props.vehicle.number}`}
              labelSimplified={props.vehicle.number}
              routeColour={props.vehicle.route.colour}
            />
          )}
        </StyledDirectionsSlideTitleContainer>

        {props.subtitle && (
          <StyledSubtitleWrapper>
            <StyledDirectionsSlideSubtitle align="center" size={0.9}>
              {props.subtitle}
            </StyledDirectionsSlideSubtitle>
            {props.isLongWalk && <WarningIcon />}
          </StyledSubtitleWrapper>
        )}

        {props.button && (
          <StyledDirectionsAction fill="outline" onClick={props.button.action}>
            {props.button.actionLabel}
          </StyledDirectionsAction>
        )}
      </StyledDirectionsSlideContent>
    </StyledDirectionsSlide>
  );
};

const StyledDirectionsSlide = styled.div`
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding-bottom: 9px;
  padding-right: 12px;
  padding-left: 12px;
`;

const StyledDirectionsSlideContent = styled(IonCard)`
  width: calc(100% - 8px);
  max-width: 400px;
  min-height: 150px;
  margin: 40px;
  border-radius: 30px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
`;

const StyledDirectionsSlideIcon = styled.div<{ colour?: string }>`
  width: 80px;
  height: 80px;
  border: 8px solid white;
  margin-bottom: 8px;
  border-radius: 80px;
  position: absolute;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0;
  z-index: ${ABOVE_MAP_Z_INDEX};

  ion-icon {
    font-size: 40px;
    color: ${({ colour }) => (colour ? getColourName(colour) : FALLBACK_DARK_COLOUR)};
  }
`;

const StyledDirectionsSlideImageContainer = styled(StyledDirectionsSlideIcon)<{ imgSrc: string }>`
  background-image: url(${(props) => props.imgSrc});
  background-size: cover;
`;

const StyledDirectionsSlideTitleContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`;

const StyledDirectionsSlideVehicleLabel = styled(RouteSummaryVehicleLabel)`
  margin-left: 10px;
`;

const StyledDirectionsSlideTitle = styled(Text)`
  text-transform: uppercase;
  text-align: center;
  font-family: var(--ion-font-family-header);
`;

const StyledDirectionsSlideSubtitle = styled(Text)``;

const StyledDirectionsAction = styled(Button)`
  margin: 12px 0 18px;
`;

const StyledSubtitleWrapper = styled.div`
  display: flex;
  margin-top: 8px;
  align-items: center;
`;

export default DirectionsSlide;
