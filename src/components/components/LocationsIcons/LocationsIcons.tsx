import React, { ReactNode } from 'react';
import { IonIcon } from '@ionic/react';
import styled from 'styled-components';
import { swapVerticalOutline } from 'ionicons/icons';
import { BREAKPOINT_MD } from '../../../constants/breakpoints';

export type LocationLeg = {
  id: string;
  icon: string;
  content: ReactNode;
};

export type LocationsIconsProps = {
  locations: LocationLeg[];
  colors: string[];
  size: 'sm' | 'md' | 'lg';
  swapAction?: () => void;
  className?: string;
};

const SIZES: { [key in LocationsIconsProps['size']]: number } = {
  sm: 1,
  md: 1.5,
  lg: 2,
};

type LocationsItemsParser = {
  iconElements: ReactNode[];
  dashesElements: ReactNode[];
  contentElements: ReactNode[];
};

const LocationsIcons = (props: LocationsIconsProps) => {
  const size: number = SIZES[props.size];

  const { iconElements, dashesElements, contentElements } = props.locations.reduce<LocationsItemsParser>(
    (parser, location, index) => {
      const iconElement = (
        <StyledLocation key={location.id} size={size} index={index}>
          <StyledIconDashed size={size} color={props.colors[index]}>
            {location.icon ? <IonIcon icon={location.icon} /> : <StyledCircleIcon colour={props.colors[index]} size={size} />}
          </StyledIconDashed>
        </StyledLocation>
      );

      const dashesElement = props.locations[index + 1] ? (
        <StyledDashedLine color={props.colors[index]} size={size} order={index} key={location.id} />
      ) : null;

      const contentElement = (
        <StyledLocation key={location.id} size={size} index={index}>
          <StyledLocationContent size={size} color={props.colors[index]} isLast={index === props.locations.length - 1}>
            {location.content}
          </StyledLocationContent>
        </StyledLocation>
      );

      parser.iconElements.push(iconElement);
      parser.dashesElements.push(dashesElement);
      parser.contentElements.push(contentElement);

      return parser;
    },
    { iconElements: [], dashesElements: [], contentElements: [] },
  );

  return (
    <StyledLocationsIcons colors={props.colors} size={size} className={props.className}>
      <StyledLocationsItemsWrapper>
        <StyledLocationsIconsWrapper size={size}>
          {iconElements}
          {dashesElements}
        </StyledLocationsIconsWrapper>
        <StyledLocationsContentWrapper>{contentElements}</StyledLocationsContentWrapper>
      </StyledLocationsItemsWrapper>

      {props.swapAction ? (
        <StyledLocationsSwapWrapper onClick={props.swapAction} className="swap-icon">
          <IonIcon icon={swapVerticalOutline} style={SwipeIconStyle} />
        </StyledLocationsSwapWrapper>
      ) : null}
    </StyledLocationsIcons>
  );
};

const StyledLocationsIcons = styled.div<{ colors: string[]; size: number }>`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-radius: 30px;
  border: 1px solid white;

  &:focus-within {
    border-color: black;
  }

  @media all and (min-width: ${BREAKPOINT_MD}px) {
    border: 4px solid white;

    &:focus-within {
      border: 4px solid var(--ion-color-tertiary-tint);
    }
  }
`;

const StyledLocation = styled.div<{ size: number; index: number }>`
  display: flex;
  overflow: auto;
  order: ${(props) => props.index};
`;

const StyledLocationsItemsWrapper = styled.div`
  flex: 1;
  display: flex;
`;

const StyledLocationsIconsWrapper = styled.div<{ size: number }>`
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-right: ${(props) => 5 * props.size}px;
`;

const StyledCircleIcon = styled.div<{ colour: string; size: number }>`
  height: ${(props) => props.size * 12}px;
  width: ${(props) => props.size * 12}px;
  border-radius: ${(props) => (props.size * 12) / 2}px;
  border: 3px solid ${(props) => props.colour};
  background-color: white;
`;

const StyledLocationsContentWrapper = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const StyledLocationContent = styled.div<{ size: number; color: string; isLast: boolean }>`
  flex: 1;
  justify-content: center;
  ion-icon.dropdown-toggle,
  .dropdown-label {
    color: ${(props) => props.color};
  }

  > div {
    display: flex;
    flex-direction: column;
    justify-content: center;

    p {
      margin: 0;
    }
  }
`;

const StyledIconDashed = styled.div<{ size: number; color: string }>`
  display: flex;
  flex-direction: column;
  align-items: center;

  ion-icon {
    font-size: ${(props) => 16 * props.size}px;
    color: ${(props) => props.color};
  }
`;

const StyledDashedLine = styled.div<{ color: string; size: number; order: number }>`
  min-height: ${(props) => 10 * props.size}px;
  order: ${(props) => props.order};
  width: 3px;
  background-image: linear-gradient(black 60%, rgba(255, 255, 255, 0) 0%);
  background-position: center;
  background-size: 3px 7px;
  background-repeat: repeat-y;
`;

const StyledLocationsSwapWrapper = styled.div`
  padding: 5px;
  margin-left: 5px;
  cursor: pointer;

  &:hover {
    color: #999999;
  }
`;

const SwipeIconStyle = {
  fontSize: '24px',
  color: '#A7ABAA',
};

export default LocationsIcons;
