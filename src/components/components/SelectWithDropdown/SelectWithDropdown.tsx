import React from 'react';
import styled, { css } from 'styled-components';
import { IonIcon } from '@ionic/react';
import { chevronDown, chevronUp } from 'ionicons/icons';
import Dropdown from '../Dropdown/Dropdown';
import { DropdownOptions } from '../Dropdown/DropdownOption';
import useComponentVisible from '../../../hooks/useComponentVisible';
import Text, { TextFontFamily } from '../../elements/Text/Text';
import { BREAKPOINT_MD } from '../../../constants/breakpoints';

export type SelectWithDropdownProps = {
  defaultLabel: string;
  options: DropdownOptions<string, string>[];
  selectedOption: string | null;
  onSelect: (id: string) => void;
  labelSize?: number;
  labelIcon?: React.ReactNode;
  fontFamily?: TextFontFamily;
  className?: string;
};

const SelectWithDropdown: React.FC<SelectWithDropdownProps> = (props: SelectWithDropdownProps) => {
  const { ref, isComponentVisible, setIsComponentVisible } = useComponentVisible(false);

  const toggleIsDropdownOpen = () => {
    setIsComponentVisible((prevState: boolean): boolean => !prevState);
  };

  const handleSelect = (id: string) => {
    props.onSelect(id);

    setIsComponentVisible(false);
  };

  const selectedOption: DropdownOptions<string, string> | undefined = props.options.find(
    (option: DropdownOptions<string, string>) => option.id === props.selectedOption,
  );

  return (
    <StyledSelectWithDropdown ref={ref} className={props.className}>
      <StyledSelectLabelContent onClick={toggleIsDropdownOpen} isDropdownExpanded={isComponentVisible}>
        <StyledSelectLabelIconAndText>
          {props.labelIcon}
          <StyledSelectedLabel className="dropdown-label" weight={700} size={props.labelSize ?? 1} fontFamily={props.fontFamily}>
            {selectedOption ? selectedOption.title : props.defaultLabel}
          </StyledSelectedLabel>
        </StyledSelectLabelIconAndText>
        <StyledToggleIcon className="dropdown-toggle" icon={isComponentVisible ? chevronUp : chevronDown} />
      </StyledSelectLabelContent>

      {isComponentVisible ? <StyledDropdown options={props.options} onSelect={handleSelect} fontFamily={props.fontFamily} /> : null}
    </StyledSelectWithDropdown>
  );
};

const StyledSelectLabelIconAndText = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-left: 20px;
`;

export const StyledToggleIcon = styled(IonIcon)`
  margin-right: 0px;
  ion-icon {
    font-size: 22px;
  }
`;

const StyledSelectWithDropdown = styled.div``;

export const StyledSelectLabelContent = styled.div<{ isDropdownExpanded: boolean }>`
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 22px;
  font-weight: 700;
  @media all and (min-width: ${BREAKPOINT_MD}px) {
    border: 4px solid white;
    ${({ isDropdownExpanded }) =>
      isDropdownExpanded &&
      css`
        border: 4px solid var(--ion-color-tertiary-tint);
      `}
`;

export const StyledSelectedLabel = styled(Text)`
  margin: 3px 10px 0 0;
  text-transform: uppercase;
`;

export const StyledDropdown = styled(Dropdown)`
  position: absolute;
  z-index: 100;
  left: 40px;
  right: 40px;
  max-height: 400px;
  margin-top: 5px;
`;

export default SelectWithDropdown;
