import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import Text from '../../elements/Text/Text';
import { IonSpinner } from '@ionic/react';

type BookingCodeProps = {
  code?: string;
  className?: string;
};

const BookingCode = (props: BookingCodeProps) => {
  const { t } = useTranslation<['booking']>(['booking']);

  return (
    <StyledBookingCode className={props.className}>
      <StyledBookingCodeLabel weight={500} size={0.75}>
        {t('booking:bookingCode')}
      </StyledBookingCodeLabel>

      {props.code ? (
        <StyledBookingCodeWrapper>
          <StyledBookingCodeValue weight={700} size={1.1}>
            {props.code}
          </StyledBookingCodeValue>
        </StyledBookingCodeWrapper>
      ) : (
        <IonSpinner name="crescent" color="primary" />
      )}
    </StyledBookingCode>
  );
};

const StyledBookingCode = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
`;

const StyledBookingCodeLabel = styled(Text)`
  text-transform: uppercase;
  margin-right: 10px;
`;

const StyledBookingCodeWrapper = styled.div`
  padding: 5px 10px;
  border-radius: 50px;
  border: 2px solid #c9c9c9;
`;

const StyledBookingCodeValue = styled(Text)`
  letter-spacing: 0.1rem;
`;

export default BookingCode;
