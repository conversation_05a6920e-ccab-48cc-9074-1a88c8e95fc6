import React from 'react';
import styled from 'styled-components';
import { IonCard, IonIcon } from '@ionic/react';
import { FALLBACK_LIGHT_COLOUR, getColourName } from '../../../helpers/colour.helpers';

type RoundedIconProps = {
  icon: string;
  iconSize: 'small' | 'large';
  iconWidth: number;
  colour: string; // ion colour var name (eg. primary) or hex code
  backgroundColour?: string; // ion colour var name (eg. primary) or hex code
  padding: number;
};

const RoundedIcon = (props: RoundedIconProps) => {
  return (
    <StyledShiftSolutionStopPointIconWrapper
      width={props.iconWidth}
      backgroundColour={props.backgroundColour}
      colour={props.colour}
      padding={props.padding}
    >
      <StyledRoundedIcon icon={props.icon} size={props.iconSize ?? 'small'} />
    </StyledShiftSolutionStopPointIconWrapper>
  );
};

const StyledRoundedIcon = styled(IonIcon)`
  width: 100%;
  height: 100%;
`;

const StyledShiftSolutionStopPointIconWrapper = styled(IonCard)<{
  width: number;
  padding: number;
  backgroundColour?: string;
  colour?: string;
}>`
  margin: 0;
  padding: ${(props) => props.padding}px;
  background-color: ${(props) => (props.backgroundColour ? getColourName(props.backgroundColour) : FALLBACK_LIGHT_COLOUR)};
  width: ${(props) => props.width}px;
  height: ${(props) => props.width}px;
  border-radius: ${(props) => props.width / 2}px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgb(0 0 0 / 0.2);
  color: ${(props) => (props.colour ? getColourName(props.colour) : FALLBACK_LIGHT_COLOUR)};
`;

export default RoundedIcon;
