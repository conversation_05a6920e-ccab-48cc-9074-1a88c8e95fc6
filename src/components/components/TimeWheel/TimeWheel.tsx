import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import ChevronButton from '../../elements/Button/ChevronButton';
import useScrollable from '../../../hooks/useScrollable';
import Text from '../../elements/Text/Text';

type TimeWheelProps = {
  start: number;
  end: number;
  value: number;
  onChange: (value: number) => void;
  step?: number;
  min?: number;
  max?: number;
  isAnimated?: boolean;
  allowOverscroll?: boolean;
  leadingZero?: boolean;
  hasError?: boolean;
  className?: string;
};

const roundToStep = (value: number, step: number): number => Math.ceil(value / step) * step;
const floorToStep = (value: number, step: number): number => Math.floor(value / step) * step;

const SCROLL_AMOUNT: number = 40;

const TimeWheel = (props: TimeWheelProps) => {
  const {
    allowOverscroll,
    isAnimated,
    start,
    end,
    value,
    onChange,
    step = 1,
    min = props.start,
    max = props.end,
    leadingZero,
    hasError,
    className,
  } = props;

  const sliderRef = useRef() as React.MutableRefObject<HTMLDivElement>;

  const items: number[] = useMemo<number[]>(() => {
    const increments: number[] = [];
    let counter: number = start;
    while (counter <= end) {
      increments.push(counter);

      counter += step;
    }

    return increments;
  }, [start, end, step]);

  const handleChangeScrollPosition = (position: number | 'increment' | 'decrement') => {
    if (typeof position === 'string') {
      if (position === 'increment') {
        const newValue: number = roundToStep(value + step, step);
        if (allowOverscroll) {
          onChange(newValue > max ? min : newValue);
        } else {
          onChange(newValue > max ? max : newValue);
        }
      } else {
        const newValue: number = roundToStep(value - step, step);
        if (allowOverscroll) {
          onChange(newValue < min ? floorToStep(max, step) : newValue);
        } else {
          onChange(newValue < min ? min : newValue);
        }
      }
    } else {
      onChange(position);
    }
  };

  /**
   *  On mount the sliderRef wont have any height so there is no where for the
   *  initial scroll functions to go to for the default value. Here we get around that
   *  by queuing up a re-render 300ms later that will scroll the picker to it's starting position.
   */
  const [refreshKey, setRefreshKey] = useState<number>(0);
  useEffect(() => {
    setTimeout(() => {
      setRefreshKey(new Date().getTime());
    }, 300);
  }, [refreshKey]);

  useLayoutEffect(() => {
    if (sliderRef.current) {
      const itemIndex = items.findIndex((item: number) => item === roundToStep(value, step));

      const scrollPosition: number = itemIndex < 0 ? 0 : itemIndex * SCROLL_AMOUNT;

      if (isAnimated) {
        sliderRef.current.scrollTo({
          top: scrollPosition,
          left: 0,
          behavior: 'smooth',
        });
      } else {
        sliderRef.current.scrollTop = scrollPosition;
      }
    }
  }, [refreshKey, value, isAnimated, items, step]);

  const onScrollUpdate = useCallback(
    (value: number) => {
      handleChangeScrollPosition(roundToStep((value / SCROLL_AMOUNT) * step, step));
    },
    [SCROLL_AMOUNT, step],
  );

  useScrollable(sliderRef, onScrollUpdate, { allowOverscroll });

  const handleScrollUp = () => {
    if (sliderRef.current) {
      let hasReachedTop: boolean = sliderRef.current.scrollTop + SCROLL_AMOUNT >= sliderRef.current.scrollHeight - 1;
      if (allowOverscroll && hasReachedTop) {
        return handleChangeScrollPosition(min);
      }

      handleChangeScrollPosition('increment');
    }
  };

  const handleScrollDown = () => {
    if (sliderRef.current) {
      let hasReachedBottom: boolean = sliderRef.current.scrollTop - SCROLL_AMOUNT < 0;
      if (allowOverscroll && hasReachedBottom) {
        return handleChangeScrollPosition(floorToStep(max, step));
      }

      handleChangeScrollPosition('decrement');
    }
  };

  return (
    <StyledTimeWheel className={className}>
      <ChevronButton direction="up" onClick={handleScrollUp} />

      <StyledTimeWheelContainer ref={sliderRef}>
        {items.map((counter: number) => (
          <StyledText key={counter} color={hasError ? 'danger' : 'secondary'} align="center" weight={600}>
            {leadingZero && counter < 10 ? `0${counter}` : counter}
          </StyledText>
        ))}
      </StyledTimeWheelContainer>

      <ChevronButton direction="down" onClick={handleScrollDown} />
    </StyledTimeWheel>
  );
};

const StyledTimeWheel = styled.div`
  margin: 20px;
  height: 120px;
  width: 70px;
  background-color: #efefef;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
`;

const StyledTimeWheelContainer = styled.div`
  height: 40px;

  overflow-x: hidden;
  overflow-y: scroll;
  white-space: nowrap;
  transition: all 0.2s;
  will-change: transform;
  user-select: none;
  cursor: pointer;

  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge add Firefox */
  & {
    -ms-overflow-style: none;
    scrollbar-width: none; /* Firefox */
  }
`;

const StyledText = styled(Text)`
  padding-top: 5px;
  font-size: 24px;
  height: 40px;
  display: flex;
  flex-direction: column;
`;

export default TimeWheel;
