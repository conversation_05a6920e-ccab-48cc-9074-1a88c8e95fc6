import React, { ReactNode, useEffect, useRef } from 'react';

type DropdownWrapperProps = {
  children: ReactNode;
  onClose: () => void;
  isOpen: boolean;
  className?: string;
};

const DropdownWrapper = ({ children, onClose, isOpen, className }: DropdownWrapperProps) => {
  const dropdownElement = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const closeDropdown = (event: Event) => {
      if (isOpen && dropdownElement.current && !dropdownElement.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('click', closeDropdown);
    }

    return () => {
      document.removeEventListener('click', closeDropdown);
    };
  }, [isOpen]);

  return (
    <div className={className} ref={dropdownElement}>
      {children}
    </div>
  );
};

export default DropdownWrapper;
