import React from 'react';
import styled from 'styled-components';
import HubIcon from '../../elements/Icon/HubIcon';
import Text, { TextFontFamily } from '../../elements/Text/Text';

export type DropdownOptions<T, U> = {
  id: T;
  title: U;
  label?: string;
  icon?: string;
  iconColour?: string;
  fontFamily?: TextFontFamily;
};

type DropdownOptionProps = DropdownOptions<string, string> & {
  onSelect: (id: string) => void;
  className?: string;
};

const DropdownOption: React.FC<DropdownOptionProps> = (props: DropdownOptionProps) => {
  const handleSelect = () => {
    props.onSelect(props.id);
  };

  return (
    <StyledDropdownOption onClick={handleSelect} className={props.className}>
      <StyledContentWrappper>
        {props.icon ? (
          <StyledDropdownOptionIconWrapper>
            <HubIcon icon={props.icon} size="small" color={props.iconColour} />
          </StyledDropdownOptionIconWrapper>
        ) : null}

        <TextWrapper>
          <StyledTitle fontFamily={props.fontFamily}>{props.title}</StyledTitle>
          {props.label ? <StyledLabel>{props.label}</StyledLabel> : null}
        </TextWrapper>
      </StyledContentWrappper>
    </StyledDropdownOption>
  );
};

export const StyledDropdownOption = styled.div`
  padding: 5px 6px 5px 5px;
  background-color: white;
  cursor: pointer;

  :hover {
    ion-icon {
      color: var(--ion-color-primary);
    }
  }
`;

export const StyledContentWrappper = styled.div`
  display: flex;
  flex-direction: row;
  padding: 8px 33px 8px 8px;
  color: #3a3a48;

  :hover {
    border-radius: 10px;
    background-color: var(--color-mint-green-tint);
  }
`;

export const StyledDropdownOptionIconWrapper = styled.div`
  margin-right: 10px;
  margin-top: 5px;
`;

const TextWrapper = styled.div``;

export const StyledTitle = styled(Text)`
  margin: 0;
  font-size: 14px;
  font-weight: 700;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const StyledLabel = styled.p`
  margin: 0;
  margin-top: 3px;
  font-size: 12px;
  font-weight: 300;
  color: #737373;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export default DropdownOption;
