import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { TextFontFamily } from '../../elements/Text/Text';
import DropdownOption, { DropdownOptions } from './DropdownOption';

type DropdownProps = {
  options: DropdownOptions<string, string>[];
  onSelect: (id: string) => void;
  fontFamily?: TextFontFamily;
  className?: string;
};

const Dropdown = (props: DropdownProps) => {
  return (
    <StyledDropdown className={props.className}>
      {props.options.map<ReactNode>((item: DropdownOptions<string, string>) => (
        <StyledDropdownOption key={item.id} {...item} onSelect={props.onSelect} fontFamily={props.fontFamily} />
      ))}
    </StyledDropdown>
  );
};

const StyledDropdown = styled.div`
  background-color: white;
  border-radius: 17px;
  box-shadow: 0 3px 6px #00000029;
  height: auto;
  overflow: auto;
`;

export const StyledDropdownOption = styled(DropdownOption)``;

export default Dropdown;
