import React from 'react';
import styled from 'styled-components';
import { IonIcon } from '@ionic/react';
import { timeOutline } from 'ionicons/icons';
import Text from '../../elements/Text/Text';
import { FALLBACK_LIGHT_COLOUR, getColourName } from '../../../helpers/colour.helpers';

type TimeEstimateProps = {
  label: string;
  className?: string;
  colour?: string; // ion colour var name (eg. primary) or hex code
};

const TimeEstimate = (props: TimeEstimateProps) => {
  return (
    <StyledTimeEstimate className={props.className} colour={props.colour}>
      <IonIcon icon={timeOutline} size="small" />
      <StyledTimeEstimateLabel size={0.75} weight={500} color={props.colour ?? 'light'}>
        {props.label}
      </StyledTimeEstimateLabel>
    </StyledTimeEstimate>
  );
};

const StyledTimeEstimate = styled.div<{ colour?: string }>`
  color: ${({ colour }) => (colour ? getColourName(colour) : FALLBACK_LIGHT_COLOUR)};
  display: flex;
  align-items: center;
`;

const StyledTimeEstimateLabel = styled(Text)`
  margin-left: 0.2rem;
`;

export default TimeEstimate;
