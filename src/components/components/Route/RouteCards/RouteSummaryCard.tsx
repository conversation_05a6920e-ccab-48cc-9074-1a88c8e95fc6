import React from 'react';
import styled from 'styled-components';
import { FixedRouteMatch, FixedRouteMatchLeg } from '../../../../machines/FixedRoute/routePlanner';
import RouteSummary from '../RouteSummary/RouteSummary';
import { locationSharp } from 'ionicons/icons';
import { IonIcon } from '@ionic/react';
import Text from '../../../elements/Text/Text';
import { differenceInMinutes, format } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { DATE_FORMAT, TIME_FORMAT } from '../../../../constants/formatting';
import { FALLBACK_DARK_COLOUR, getColourName } from '../../../../helpers/colour.helpers';

type RouteSummaryCardProps = {
  trip: FixedRouteMatch;
  unitOfMeasurement: 'imperial' | 'metric';
  routePlannerDepartureDateTime: string | null;
  action?: () => void;
  className?: string;
};

const getRouteColourFromJourneyLegs = (journeyLegs: FixedRouteMatchLeg[]): string | undefined => {
  for (const journeyLeg of journeyLegs) {
    if (journeyLeg.travelType === 'bus') {
      return journeyLeg.vehicle.route.colour;
    }
  }
  return undefined;
};

const RouteSummaryCard = (props: RouteSummaryCardProps) => {
  const { t } = useTranslation<['common']>(['common']);

  const routeColour: string | undefined = getRouteColourFromJourneyLegs(props.trip.journeyData.journey.journeyLegs);

  const leftColumnDateFormatter = (date: string): string =>
    (props.routePlannerDepartureDateTime && new Date(props.routePlannerDepartureDateTime).getDay() !== new Date(date).getDay()
      ? format(new Date(date), DATE_FORMAT).toUpperCase() + ', '
      : '') + format(new Date(date), TIME_FORMAT);

  return (
    <StyledRouteSummaryCard className={props.className} onClick={props.action}>
      <StyledRouteSummaryCardContent>
        <StyledRouteSummaryCardRowGroup>
          <StyledRouteSummaryCardHeaderIcon>
            <StyledCircleIcon isCurrentLocation={props.trip.startAddress.label === '' && props.trip.startAddress.placeId === ''} />
          </StyledRouteSummaryCardHeaderIcon>

          <StyledRouteSummaryCardRowHeader>
            <Text size={0.8}>{props.trip.startAddress.label.split(',')[0] || t('common:currentLocation')}</Text>

            <StyledRouteSummaryCardRowGroup>
              <Text size={0.8} weight={700}>
                {differenceInMinutes(new Date(props.trip.endTime), new Date(props.trip.startTime))}
              </Text>
              &nbsp;
              <Text size={0.7}>{t('common:min')}</Text>
            </StyledRouteSummaryCardRowGroup>
          </StyledRouteSummaryCardRowHeader>
        </StyledRouteSummaryCardRowGroup>

        <StyledRouteSummaryCardRow>
          <StyledRouteSummaryLeftColumn>
            <StyledRouteSummaryStopBorder />
          </StyledRouteSummaryLeftColumn>

          <Text size={0.7} weight={700}>
            {leftColumnDateFormatter(props.trip.startTime)}
          </Text>
        </StyledRouteSummaryCardRow>

        <StyledRouteSummaryCardRowGroup>
          <StyledRouteSummaryCardHeaderIcon colour={routeColour}>
            <IonIcon icon={locationSharp} style={{ ...RouteSummaryIconsStyle }} />
          </StyledRouteSummaryCardHeaderIcon>

          <Text size={0.8}>{props.trip.endAddress.label.split(',')[0]}</Text>
        </StyledRouteSummaryCardRowGroup>

        <StyledRouteSummaryCardRowGroup>
          <StyledRouteSummaryLeftColumn />
          <Text size={0.7} weight={700}>
            {leftColumnDateFormatter(props.trip.endTime)}
          </Text>
        </StyledRouteSummaryCardRowGroup>
      </StyledRouteSummaryCardContent>

      <StyledRouteSummaryWrapper>
        <RouteSummary route={props.trip.journeyData.journey.journeyLegs} unitOfMeasurement={props.unitOfMeasurement} />
      </StyledRouteSummaryWrapper>
    </StyledRouteSummaryCard>
  );
};

const StyledRouteSummaryCard = styled.div`
  background-color: #f7f7f7;
  overflow: hidden;
`;

const StyledRouteSummaryCardContent = styled.div`
  padding: 15px;
`;

const StyledRouteSummaryCardRow = styled.div`
  display: flex;
  flex-direction: row;
`;

const StyledRouteSummaryCardRowGroup = styled(StyledRouteSummaryCardRow)`
  align-items: center;
`;

const StyledRouteSummaryLeftColumn = styled.div`
  width: 26px;
  display: flex;
  flex-direction: row;
  justify-content: center;
`;

const StyledRouteSummaryCardHeaderIcon = styled(StyledRouteSummaryLeftColumn)<{ colour?: string }>`
  margin-top: 2px;
  > ion-icon {
    font-size: 16px;
    color: ${({ colour }) => (colour ? getColourName(colour) : FALLBACK_DARK_COLOUR)};
  }
`;

const StyledRouteSummaryStopBorder = styled.div`
  border-left: 2px dashed black;
  height: 26px;
`;

const StyledRouteSummaryCardRowHeader = styled.div`
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

const StyledRouteSummaryWrapper = styled.div`
  background-color: #e3ebec;
`;

const RouteSummaryIconsStyle = {
  fontSize: '22px',
};

const StyledCircleIcon = styled.div<{ colour?: string; hasIcon?: boolean; isCurrentLocation: boolean }>`
  height: 18px;
  width: 18px;
  border-radius: 18px;
  border: 2px solid ${({ isCurrentLocation }) => (isCurrentLocation ? '#ffffff' : 'var(--ion-color-dark)')};
  background-color: ${({ isCurrentLocation }) => (isCurrentLocation ? 'var(--ion-color-location)' : null)};
  color: var(--ion-color-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: ${({ isCurrentLocation }) => (isCurrentLocation ? '0 0 5px 0 rgba(0,0,0,0.75)' : null)};
`;

export default RouteSummaryCard;
