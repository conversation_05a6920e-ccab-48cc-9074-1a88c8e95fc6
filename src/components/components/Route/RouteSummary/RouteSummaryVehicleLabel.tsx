import React from 'react';
import styled from 'styled-components';
import Text from '../../../elements/Text/Text';

export type RouteVehicleSummaryLabel = {
  labelExpanded: string;
  labelSimplified: string;
  simple?: boolean;
  routeColour?: string; // // ion colour var name (eg. primary) or hex code
};

type RouteSummaryVehicleLabelProps = RouteVehicleSummaryLabel & {
  className?: string;
};

const RouteSummaryVehicleLabel = (props: RouteSummaryVehicleLabelProps) => {
  return (
    <StyledRouteVehicleSummaryLabelWrapper simple={props.simple} className={props.className} routeColour={props.routeColour}>
      <Text size={0.7} weight={600} align="center" color="dark">
        {props.simple ? props.labelSimplified : props.labelExpanded}
      </Text>
    </StyledRouteVehicleSummaryLabelWrapper>
  );
};

RouteSummaryVehicleLabel.displayName = 'RouteSummaryVehicleLabel';

const StyledRouteVehicleSummaryLabelWrapper = styled.div<{ simple?: boolean; routeColour?: string }>`
  padding: 5px ${(props) => (props.simple ? 15 : 12)}px;
  border-radius: 20px;
`;

export default RouteSummaryVehicleLabel;
