import React, { Fragment } from 'react';
import styled from 'styled-components';
import { FixedRoute } from '@liftango/ops-client';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import RouteSummaryVehicleLabel, { RouteVehicleSummaryLabel } from './RouteSummaryVehicleLabel';
import Text from '../../../elements/Text/Text';
import CapacitySvg from '../../../../assets/chair-icon.svg?react';
import { useTranslation } from 'react-i18next';
import { capacityColor, RouteVehicleCapacityWrapper } from '../../../../helpers/capacity.helpers';
import { IonIcon } from '@ionic/react';
import { useAppSelector } from '../../../../hooks/storeHooks';
import { selectTheme, Theme } from '../../../../store/reducers/themeReducer';

type RouteVehicleSummaryProps = RouteVehicleSummaryLabel & {
  routeColour: string;
  vehicleImageSrc: string;
  availableCapacities: FixedRoute.AvailableCapacity[];
  vehicleCapacities: FixedRoute.AvailableCapacity[];
  className?: string;
  hideLabel?: boolean;
};

const RouteVehicleSummary = (props: RouteVehicleSummaryProps) => {
  const { t } = useTranslation<['trip']>(['trip']);
  const theme: Theme = useAppSelector(selectTheme);

  const availableRegularSeats: number | undefined = props.availableCapacities.find(({ capacityName }) => capacityName === 'standard')
    ?.amount;
  const regularSeatsCapacity: number | undefined = props.vehicleCapacities.find(({ capacityName }) => capacityName === 'standard')?.amount;
  const availableWheelchairSeats: number | undefined = props.availableCapacities.find(({ capacityName }) => capacityName === 'wheelchair')
    ?.amount;
  const wheelchairSeatsCapacity: number | undefined = props.vehicleCapacities.find(({ capacityName }) => capacityName === 'wheelchair')
    ?.amount;

  // @ts-ignore - Object is possibly 'undefined'
  const availableRegularSeatsPercentage: number = Math.floor((availableRegularSeats / regularSeatsCapacity) * 100);
  // @ts-ignore - Object is possibly 'undefined'
  const availableWheelchairSeatsPercentage: number = Math.floor((availableWheelchairSeats / wheelchairSeatsCapacity) * 100);

  return (
    <StyledRouteVehicleSummary className={props.className}>
      {props.vehicleImageSrc ? (
        <StyledImage src={props.vehicleImageSrc} alt={t('trip:busImageAlt')} />
      ) : theme.icons?.fixedRouteVehicle ? (
        <StyledShuttleIcon icon={theme.icons?.fixedRouteVehicle} iconColor={props.routeColour} />
      ) : (
        <FontAwesomeIcon icon="bus" color={props.routeColour} style={{ width: 30, height: 30 }} />
      )}
      {!props.hideLabel && (
        <StyledRouteVehicleSummaryLabel
          simple={props.simple}
          labelSimplified={props.labelSimplified}
          labelExpanded={props.labelExpanded}
          routeColour={props.routeColour}
        />
      )}

      {!isNaN(availableRegularSeatsPercentage) && (
        <RouteVehicleCapacityWrapper>
          <StyledCapacitySvg height={24} color={capacityColor(availableRegularSeatsPercentage).themeValue} />

          {availableRegularSeats ? (
            <Fragment>
              <Text size={0.6} weight={600} align="center" color={capacityColor(availableRegularSeatsPercentage).namedValue}>
                {t('trip:numFree', { count: availableRegularSeats })}
              </Text>
              <Text size={0.6} weight={600} align="center" color={capacityColor(availableRegularSeatsPercentage).namedValue}>
                {availableRegularSeats === 1 ? t('trip:seat') : t('trip:seats')}
              </Text>
            </Fragment>
          ) : (
            <Text size={0.6} weight={600} align="center" color={capacityColor(availableRegularSeatsPercentage).namedValue}>
              {t('trip:full')}
            </Text>
          )}
        </RouteVehicleCapacityWrapper>
      )}

      {!isNaN(availableWheelchairSeatsPercentage) && (
        <RouteVehicleCapacityWrapper>
          <StyledFontAwesomeIcon icon={['fab', 'accessible-icon']} color={capacityColor(availableWheelchairSeatsPercentage).themeValue} />

          <StyledCapacityText size={0.5} weight={600} align="center" color={capacityColor(availableWheelchairSeatsPercentage).namedValue}>
            {availableWheelchairSeats ? t('trip:numFree', { count: availableWheelchairSeats }) : t('trip:full')}
          </StyledCapacityText>
        </RouteVehicleCapacityWrapper>
      )}
    </StyledRouteVehicleSummary>
  );
};

RouteVehicleSummary.displayName = 'RouteVehicleSummary';

const StyledCapacitySvg = styled(CapacitySvg)`
  padding: 0 10px;
  display: none;
`;

const StyledImage = styled.img`
  width: 35px;
  height: 35px;
  border: 4px solid white;
  border-radius: 50%;
  left: 10px;
  margin-right: 20px;
  position: relative;
`;

const StyledShuttleIcon = styled(IonIcon)<{ iconColor: string }>`
  width: 35px;
  height: 35px;
  left: 10px;
  margin-right: 10px;
  padding: 5px 0;
  position: relative;
  color: ${(props) => props.iconColor};
`;

const StyledRouteVehicleSummary = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
`;

const StyledRouteVehicleSummaryLabel = styled(RouteSummaryVehicleLabel)`
  display: none;
`;

const StyledFontAwesomeIcon = styled(FontAwesomeIcon)`
  font-size: 24px !important;
`;

const StyledCapacityText = styled(Text)`
  white-space: nowrap;
`;

export default RouteVehicleSummary;
