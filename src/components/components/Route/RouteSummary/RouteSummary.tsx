import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { chevronForwardOutline, walkOutline } from 'ionicons/icons';
import { IonIcon } from '@ionic/react';
import RouteSummaryVehicle from './RouteSummaryVehicle';
import Text from '../../../elements/Text/Text';
import { DistanceMeasurement, formatDistanceMeasurment } from '../../../../helpers/journey.helpers';
import WarningIcon from '../../../elements/Icon/WarningIcon';
import { LONG_WALK_WARNING_THRESHOLD_IN_METERS } from '../../../../constants/map';
import { FixedRoute } from '@liftango/ops-client';

type RouteDetailWalk = {
  travelType: 'foot';
  distance: number;
};

type RouteDetailBus = {
  travelType: 'bus';
  availableCapacities: {
    amount: number;
    capacityName: string;
    capacityTypeId: string;
    displayOrder: number;
  }[];
  vehicle: {
    number: string;
    displayName: string;
    capacity: FixedRoute.AvailableCapacity[];
    image: string;
    route: {
      label: string;
      colour: string;
    };
  };
};

export type RouteDetail = RouteDetailWalk | RouteDetailBus;

type RouteSummaryProps = {
  route: RouteDetail[];
  unitOfMeasurement: 'imperial' | 'metric';
  simple?: boolean;
  className?: string;
};

const RouteSummary = (props: RouteSummaryProps) => {
  const hasTwoWalkingLegs: boolean = props.route.length === 3;

  return (
    <StyledRouteSummary className={props.className}>
      {props.route.map((r: RouteDetail, index: number) => {
        const items: ReactNode[] = [];

        if (index > 0) {
          items.push(
            <StyledRouteSummarySeparator key={`separator_${r.travelType}`}>
              <StyledChevronIonIcon icon={chevronForwardOutline} color={'secondary'} />
            </StyledRouteSummarySeparator>,
          );
        }

        if (r.travelType === 'foot') {
          const formattedDistance: DistanceMeasurement = formatDistanceMeasurment(r.distance, props.unitOfMeasurement);
          items.push(
            <StyledWalkingInfo key={`icon_${r.travelType}_${r.distance}`}>
              <IonIcon icon={walkOutline} size="large" />
              <Text size={0.7}>{`${formattedDistance.distance}${formattedDistance.unit}`}</Text>
              {r.distance > LONG_WALK_WARNING_THRESHOLD_IN_METERS && <WarningIcon iconSize={24} />}
            </StyledWalkingInfo>,
          );
        } else {
          items.push(
            <RouteSummaryVehicle
              key={`detail_${r.travelType}`}
              simple={props.simple}
              labelSimplified={r.vehicle.number}
              labelExpanded={`${r.vehicle.displayName} - ${r.vehicle.number}`}
              availableCapacities={r.availableCapacities}
              vehicleCapacities={r.vehicle.capacity}
              hideLabel={hasTwoWalkingLegs}
              routeColour={r.vehicle.route.colour}
              vehicleImageSrc={r.vehicle.image}
            />,
          );
        }

        return items;
      })}
    </StyledRouteSummary>
  );
};

RouteSummary.displayName = 'RouteSummary';

const StyledWalkingInfo = styled.div`
  padding: 5px 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`;

const StyledChevronIonIcon = styled(IonIcon)`
  font-size: 22px;
`;

const StyledRouteSummary = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
  padding: 10px 15px;
  flex-wrap: wrap;
`;

const StyledRouteSummarySeparator = styled.div`
  display: flex;
  flex: 1;
  justify-content: center;
  max-width: 30px;
`;

export default RouteSummary;
