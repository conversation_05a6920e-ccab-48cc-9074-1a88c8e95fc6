import React from 'react';
import styled from 'styled-components';
import RoutePoint, { RoutePointItem } from './RoutePoint';

type RoutePointsProps = {
  points: RoutePointItem[];
  unitOfMeasurement: 'metric' | 'imperial';
  className?: string;
};

const RoutePoints = (props: RoutePointsProps) => {
  return (
    <StyledRoutePoints className={props.className}>
      {props.points.map((point: RoutePointItem, index: number) => (
        <RoutePoint
          key={`${point.travelType}_${point.startTime}_${point.endTime}`}
          point={point}
          unitOfMeasurement={props.unitOfMeasurement}
          isFirstPoint={index === 0}
        />
      ))}

      <RoutePoint isLastPoint point={props.points[props.points.length - 1]} unitOfMeasurement={props.unitOfMeasurement} />
    </StyledRoutePoints>
  );
};

const StyledRoutePoints = styled.div`
  overflow: visible;
`;

export default RoutePoints;
