import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { IonIcon } from '@ionic/react';
import { locationSharp, walkOutline } from 'ionicons/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { TIME_FORMAT } from '../../../../constants/formatting';
import Text from '../../../elements/Text/Text';
import { FixedRouteMatchLeg } from '../../../../machines/FixedRoute/routePlanner';
import { FALLBACK_DARK_COLOUR, FALLBACK_LIGHT_COLOUR, getColourName } from '../../../../helpers/colour.helpers';
import { DistanceMeasurement, formatDistanceMeasurment } from '../../../../helpers/journey.helpers';
import WarningIcon from '../../../elements/Icon/WarningIcon';
import { LONG_WALK_WARNING_THRESHOLD_IN_METERS } from '../../../../constants/map';
import { selectTheme, Theme } from '../../../../store/reducers/themeReducer';
import { useAppSelector } from '../../../../hooks/storeHooks';
import { useWindowWidth } from '@react-hook/window-size';
import { BREAKPOINT_MD } from '../../../../constants/breakpoints';

export type RoutePointItem = FixedRouteMatchLeg;

type RoutePointProps = {
  point: RoutePointItem;
  unitOfMeasurement: 'metric' | 'imperial';
  isLastPoint?: boolean;
  isFirstPoint?: boolean;
  className?: string;
};

const RoutePoint = (props: RoutePointProps) => {
  const { t } = useTranslation<['common', 'trip']>(['common', 'trip']);
  const theme: Theme = useAppSelector(selectTheme);

  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  const formattedDistance: DistanceMeasurement = formatDistanceMeasurment(props.point.distance, props.unitOfMeasurement);

  return (
    <StyledRoutePoint className={props.className} isLastPoint={props.isLastPoint} isMobile={isMobile}>
      <StyledRoutePointLeftColumn>
        {props.isFirstPoint ? (
          <StyledCircleIcon isCurrentLocation={props.point.startAddress.label === '' && props.point.startAddress.placeId === ''} />
        ) : (
          <StyledCircleIcon
            colour={props.point.travelType === 'bus' ? props.point.vehicle.route.colour : undefined}
            hasIcon={props.isLastPoint}
          >
            {props.isLastPoint ? <IonIcon icon={locationSharp} /> : null}
          </StyledCircleIcon>
        )}
      </StyledRoutePointLeftColumn>

      <StyledRoutePointRightColumn
        as={props.point.travelType === 'bus' ? StyledRouteBusPointRightColumn : StyledRouteWalkPointRightColumn}
        isLastPoint={props.isLastPoint}
        colour={props.point.travelType === 'bus' ? props.point.vehicle.route.colour : FALLBACK_DARK_COLOUR}
      >
        <StyledRoutePointHeaderRow>
          {props.isFirstPoint || props.isLastPoint ? (
            <Text weight={600} size={0.8} color={'secondary'}>
              {props.isFirstPoint ? t('common:Start') : props.isLastPoint ? t('common:Arrive') : ''}
            </Text>
          ) : (
            <StyledRoutePointLegLabel weight={400} size={1}>
              {props.point.startAddress.label}
            </StyledRoutePointLegLabel>
          )}
          <Text weight={600} size={0.8} color={'secondary'}>
            {props.isLastPoint ? format(new Date(props.point.endTime), TIME_FORMAT) : format(new Date(props.point.startTime), TIME_FORMAT)}
          </Text>
        </StyledRoutePointHeaderRow>

        {(props.isFirstPoint || props.isLastPoint) && (
          <StyledRoutePointLegLabel weight={400} size={1}>
            {props.isLastPoint
              ? props.point.endAddress.label
              : props.point.startAddress.label === ''
              ? t('common:currentLocation')
              : props.point.startAddress.label}
          </StyledRoutePointLegLabel>
        )}

        {!props.isLastPoint ? (
          props.point.travelType === 'bus' ? (
            <>
              <StyledRoutePointScheduledRow>
                <StyledTakeShuttleLabel weight={600} size={0.8} color={'secondary'}>
                  {t('trip:takeShuttle', { time: format(new Date(props.point.startTime), TIME_FORMAT) })}
                </StyledTakeShuttleLabel>
              </StyledRoutePointScheduledRow>

              <StyledRouteSummaryVehicleLabel>
                {theme.icons?.fixedRouteVehicle ? (
                  <IonIcon icon={theme.icons?.fixedRouteVehicle} color="dark" size="large" style={{ marginRight: 5 }} />
                ) : (
                  <FontAwesomeIcon icon="bus" color={FALLBACK_DARK_COLOUR} style={{ width: 30, height: 30, marginRight: 5 }} />
                )}

                <Text size={0.7} weight={600} align="center" color="dark">
                  {`${props.point.vehicle.displayName} - ${props.point.vehicle.number}`}
                </Text>
              </StyledRouteSummaryVehicleLabel>

              <StyledRoutePointScheduledRow>
                <Text weight={500} size={0.8} color={'secondary'}>
                  {t('trip:shuttleJourneyTime', {
                    count: props.point.travelTime,
                  })}
                </Text>
              </StyledRoutePointScheduledRow>
            </>
          ) : props.point.travelType === 'foot' ? (
            <StyledWalkingLabel>
              <IonIcon icon={walkOutline} size="large" color="dark" />

              <Text weight={500} size={0.8}>
                {t('trip:footJourneyLeg', {
                  distance: `${formattedDistance.distance}${formattedDistance.unit}`,
                  count: props.point.travelTime,
                })}
              </Text>

              {props.point.distance > LONG_WALK_WARNING_THRESHOLD_IN_METERS && <WarningIcon iconSize={20} />}
            </StyledWalkingLabel>
          ) : null
        ) : null}
      </StyledRoutePointRightColumn>
    </StyledRoutePoint>
  );
};

RoutePoint.displayName = 'RoutePoint';

const StyledRoutePoint = styled.div<{ isMobile: boolean; isLastPoint?: boolean }>`
  display: flex;
  min-height: ${(props) => (props.isLastPoint ? 0 : 80)}px;
  flex-direction: row;
  padding-bottom: ${(props) => (props.isLastPoint && props.isMobile ? 10 : 0)}px;
`;

const StyledRoutePointLeftColumn = styled.div`
  width: 50px;
  margin-top: -10px;
  margin-right: -30px;
  z-index: 1;
`;

const StyledRoutePointRightColumn = styled.div<{ isLastPoint?: boolean }>`
  flex: 1;
  padding-left: 32px;
  padding-bottom: ${(props) => (props.isLastPoint ? 0 : 40)}px;
`;

const StyledRouteBusPointRightColumn = styled.div<{ isLastPoint?: boolean; colour: string }>`
  margin-left: -8px;
  border-left: ${(props) => (props.isLastPoint ? 0 : 8)}px solid ${({ colour }) => (colour ? getColourName(colour) : FALLBACK_DARK_COLOUR)};
`;

const StyledRouteWalkPointRightColumn = styled.div<{ isLastPoint?: boolean }>`
  margin-left: ${(props) => (props.isLastPoint ? -1 : -6)}px;
  border-left: ${(props) => (props.isLastPoint ? 0 : 4)}px dashed var(--ion-color-secondary);
  padding-bottom: ${(props) => (props.isLastPoint ? 0 : 30)}px;
`;

const StyledRoutePointHeaderRow = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

const StyledRoutePointLegLabel = styled(Text)`
  margin: 5px 0;
`;

const StyledRouteSummaryVehicleLabel = styled.div`
  margin-top: 8px;
  display: flex;
  align-items: center;
`;

const StyledRoutePointScheduledRow = styled.div`
  margin-top: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
`;

const StyledWalkingLabel = styled.div`
  display: flex;
  align-items: center;
  padding-top: 30px;
  margin-left: -5px;
`;

const StyledCircleIcon = styled.div<{ colour?: string; hasIcon?: boolean; isCurrentLocation?: boolean }>`
  height: 32px;
  width: 32px;
  border-radius: 32px;
  border: ${({ hasIcon }) => (hasIcon ? 4 : 6)}px solid
    ${({ isCurrentLocation, colour }) => (isCurrentLocation ? FALLBACK_LIGHT_COLOUR : colour)};
  background-color: ${({ isCurrentLocation }) => (isCurrentLocation ? 'var(--ion-color-location)' : FALLBACK_LIGHT_COLOUR)};
  font-size: 18px;
  color: ${({ colour }) => (colour ? getColourName(colour) : 'var(--ion-color-dark)')};

  margin-top: 10px;

  display: flex;
  align-items: center;
  justify-content: center;

  outline: ${({ isCurrentLocation }) => (isCurrentLocation ? '1px solid var(--ion-color-medium)' : null)};
`;

const StyledTakeShuttleLabel = styled(Text)`
  margin-top: 30px;
`;

export default RoutePoint;
