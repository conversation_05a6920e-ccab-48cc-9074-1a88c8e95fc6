import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useWindowWidth } from '@react-hook/window-size';
import { FixedRoute } from '@liftango/ops-client';
import Text from '../../../../elements/Text/Text';
import { BREAKPOINT_MD } from '../../../../../constants/breakpoints';
import { IonIcon } from '@ionic/react';
import { informationCircle } from 'ionicons/icons';
import InfoLabel from '../../../InfoLabel/InfoLabel';

type RouteInfoProps = {
  route: FixedRoute.ServiceRoutesAndStopsPayload;
  isRouteActive: boolean;
};

const NEW_LINE_CHARACTER: string = '$n';

const RouteInfo = (props: RouteInfoProps) => {
  const { t } = useTranslation<['trip', 'common']>(['trip', 'common']);

  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  return (
    <StyledRouteInfo>
      <StyledOperatingTimesLabel
        fontFamily={'body'}
        color={isMobile ? 'secondary' : 'primary'}
        size={isMobile ? 0.6 : 1}
        weight={isMobile ? 300 : 500}
      >
        {t('trip:operatingTimes')}
      </StyledOperatingTimesLabel>
      {!props.isRouteActive && <InfoLabel>{t('common:serviceInactive')}</InfoLabel>}
      {props.route.description &&
        props.route.description.split(NEW_LINE_CHARACTER).map<JSX.Element>((textLine: string) =>
          textLine ? (
            <StyledRouteInfoRowText weight={300} size={0.8} key={textLine}>
              {textLine}
            </StyledRouteInfoRowText>
          ) : (
            <br />
          ),
        )}
    </StyledRouteInfo>
  );
};

const StyledRouteInfo = styled.div`
  margin-left: 20px;
  margin-top: 15px;
  margin-right: 15px;
  margin-bottom: 15px;
`;

const StyledOperatingTimesLabel = styled(Text)`
  margin-bottom: 15px;
  text-transform: uppercase;
`;

const StyledRouteInfoRowText = styled(Text)`
  margin-bottom: 5px;
`;

export default RouteInfo;
