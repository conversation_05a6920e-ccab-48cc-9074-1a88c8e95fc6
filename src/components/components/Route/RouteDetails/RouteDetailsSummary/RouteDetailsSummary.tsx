import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useWindowWidth } from '@react-hook/window-size';
import { FixedRoute } from '@liftango/ops-client';
import Text from '../../../../elements/Text/Text';
import SelectWithDropdown, {
  StyledToggleIcon,
  StyledDropdown,
  StyledSelectLabelContent,
  StyledSelectedLabel,
} from '../../../SelectWithDropdown/SelectWithDropdown';
import {
  StyledContentWrappper,
  StyledDropdownOptionIconWrapper,
  StyledTitle,
  StyledDropdownOption,
  DropdownOptions,
} from '../../../Dropdown/DropdownOption';
import { useAppSelector } from '../../../../../hooks/storeHooks';
import { selectRoutes, selectRouteById } from '../../../../../store/reducers/routesReducer';
import HubIcon from '../../../../elements/Icon/HubIcon';
import { BREAKPOINT_MD } from '../../../../../constants/breakpoints';
import { scrollbarStyle } from '../../../../../containers/Planners/RoutePlanner/RoutePlanner';

type RouteDetailsSummaryProps = {
  selectedRouteId: string;
  handleRouteSelect: (routeVersionId: string) => void;
  dropDownSize: number;
};

const RouteDetailsSummary = (props: RouteDetailsSummaryProps) => {
  const { t } = useTranslation<['trip']>(['trip']);

  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  const routesAndStops: FixedRoute.ServiceRoutesAndStopsPayload[] = useAppSelector(selectRoutes);

  const selectedRoute: FixedRoute.ServiceRoutesAndStopsPayload | null = useAppSelector(selectRouteById(props.selectedRouteId));

  const options: DropdownOptions<string, string>[] = routesAndStops.reduce<DropdownOptions<string, string>[]>(
    (acc: DropdownOptions<string, string>[], current: FixedRoute.ServiceRoutesAndStopsPayload) => {
      if (current.id !== selectedRoute?.id) {
        acc.push({
          id: current.id,
          title: current.label,
          icon: 'circle',
          iconColour: current.colour,
        });
      }
      return acc;
    },
    [],
  );

  return selectedRoute ? (
    <>
      <StyledShuttleRoutesText fontFamily={'body'} color={'secondary'} size={isMobile ? 0.6 : 0.8} weight={isMobile ? 300 : 500}>
        {t('trip:shuttleRoutes')}
      </StyledShuttleRoutesText>
      <StyledSelectWithDropdown
        defaultLabel={selectedRoute.label}
        options={options}
        onSelect={props.handleRouteSelect}
        selectedOption={props.selectedRouteId}
        labelIcon={<HubIcon icon="circle" color={selectedRoute.colour} />}
        fontFamily={'header'}
        size={props.dropDownSize}
        isMobile={isMobile}
      />
    </>
  ) : null;
};

const StyledShuttleRoutesText = styled(Text)`
  display: flex;
  align-items: center;
  width: 100%;
  flex-direction: column;
  margin-bottom: 15px;
  text-transform: uppercase;
`;

const StyledSelectWithDropdown = styled(SelectWithDropdown)<{ size: number; isMobile: boolean }>`
  ${StyledToggleIcon} {
    margin-right: 22px;
  }

  ${StyledDropdown} {
    border: ${({ isMobile }) => (isMobile ? '1px solid black' : 'none')};
    max-height: ${({ size }) => size}px;
    margin-top: 7px;
    left: ${({ isMobile }) => (isMobile ? '30' : '16')}px;
    right: ${({ isMobile }) => (isMobile ? '30' : '16')}px;

    ${({ isMobile }) => !isMobile && scrollbarStyle}
  }

  ${StyledSelectLabelContent} {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.29);
    justify-content: space-between;
    border-radius: 21px;
  }

  ${StyledSelectedLabel} {
    margin-left: 15px;
    margin-top: 11px;
    margin-bottom: 11px;
  }

  ${StyledContentWrappper} {
    align-items: center;
  }

  ${StyledDropdownOptionIconWrapper} {
    margin-left: 8px;
  }

  ${StyledTitle} {
    text-transform: uppercase;
  }

  ${StyledDropdownOption} {
    padding: 0px 6px 0px 5px;
  }
`;

export default RouteDetailsSummary;
