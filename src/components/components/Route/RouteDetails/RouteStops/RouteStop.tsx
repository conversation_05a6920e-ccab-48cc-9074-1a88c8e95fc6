import React from 'react';
import styled from 'styled-components';
import { IonCard, IonIcon } from '@ionic/react';
import { locationSharp, chevronForward } from 'ionicons/icons';
import { useWindowWidth } from '@react-hook/window-size';
import Text from '../../../../elements/Text/Text';
import { BREAKPOINT_MD } from '../../../../../constants/breakpoints';

export type RouteStopItem = {
  id: string;
  label: string;
  description: string;
  address: string;
};

type RouteStopProps = {
  stop: RouteStopItem;
  routeColour: string; // hex code
  handleRouteStopPress: (stopId: string) => void;
  isLastStop?: boolean;
  className?: string;
};

const RouteStop = (props: RouteStopProps) => {
  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  return (
    <StyledRouteStop className={props.className} isLastStop={props.isLastStop} onClick={() => props.handleRouteStopPress(props.stop.id)}>
      <StyledRouteStopLeftColumn>
        <StyledRouteStopIconWrapper colour={props.routeColour}>
          <IonIcon icon={locationSharp} size={'large'} />
        </StyledRouteStopIconWrapper>
      </StyledRouteStopLeftColumn>

      <StyledRouteStopRightColumn isLastStop={props.isLastStop} routeColour={props.routeColour}>
        <StyledRouteStopHeaderRow>
          <Text weight={isMobile ? 700 : 500} size={1}>
            {props.stop.label}
          </Text>
          <StyledChevronForwardIcon icon={chevronForward} size={'small'} color={'primary'} />
        </StyledRouteStopHeaderRow>

        <StyledRouteStopAddressLabel weight={500} size={isMobile ? 0.8 : 0.7}>
          {props.stop.address}
        </StyledRouteStopAddressLabel>
      </StyledRouteStopRightColumn>
    </StyledRouteStop>
  );
};

const StyledRouteStop = styled.div<{ isLastStop?: boolean }>`
  display: flex;
  min-height: ${(props) => (props.isLastStop ? 0 : 80)}px;
  flex-direction: row;
`;

const StyledRouteStopLeftColumn = styled.div`
  width: 50px;
  margin-top: -10px;
  margin-right: -32px;
  z-index: 1;
`;

const StyledRouteStopIconWrapper = styled(IonCard)<{ colour: string }>`
  margin: 0;
  padding: 10px;
  width: 40px;
  height: 40px;
  border-radius: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.29);
  color: ${(props) => props.colour};
`;

const StyledChevronForwardIcon = styled(IonIcon)`
  margin-left: 4px;
`;

const StyledRouteStopRightColumn = styled.div<{ routeColour: string; isLastStop?: boolean }>`
  flex: 1;
  padding-left: 40px;
  padding-bottom: ${(props) => (props.isLastStop ? 0 : 40)}px;
  border-left-width: ${(props) => (props.isLastStop ? 0 : 4)}px;
  border-left-style: solid;
  border-left-color: ${(props) => props.routeColour};
`;

const StyledRouteStopHeaderRow = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
`;

const StyledRouteStopAddressLabel = styled(Text)`
  margin: 5px 0 8px;
`;

export default RouteStop;
