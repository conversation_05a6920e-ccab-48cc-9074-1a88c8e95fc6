import React from 'react';
import styled from 'styled-components';
import RouteStop, { RouteStopItem } from './RouteStop';

type RouteStopsProps = {
  stops: RouteStopItem[];
  routeColour: string;
  onRouteStopPressed: (id: string) => void;
  className?: string;
};

const RouteStops = (props: RouteStopsProps) => {
  return (
    <StyledRoutePoints className={props.className}>
      {props.stops.map((stop: RouteStopItem, index: number) => (
        <RouteStop
          key={stop.id}
          stop={stop}
          isLastStop={props.stops.length - 1 === index}
          routeColour={props.routeColour}
          handleRouteStopPress={props.onRouteStopPressed}
        />
      ))}
    </StyledRoutePoints>
  );
};

const StyledRoutePoints = styled.div`
  overflow: visible;
`;

export default RouteStops;
