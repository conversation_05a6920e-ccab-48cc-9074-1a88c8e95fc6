import React from 'react';
import styled from 'styled-components';
import EstimateTooltip from '../../elements/ToolTip/EstimateTooltip';
import Avatar from '../../elements/Avatar/Avatar';

type VehicleEstimateProps = {
  label: string;
  src: string;
  className?: string;
};

const VehicleEstimate = (props: VehicleEstimateProps) => {
  return (
    <StyledVehicleEstimate className={props.className}>
      <StyledVehicleEstimateTooltip label={props.label} />
      <Avatar src={props.src} />
    </StyledVehicleEstimate>
  );
};

const StyledVehicleEstimate = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const StyledVehicleEstimateTooltip = styled(EstimateTooltip)`
  min-width: 85px;
`;

export default VehicleEstimate;
