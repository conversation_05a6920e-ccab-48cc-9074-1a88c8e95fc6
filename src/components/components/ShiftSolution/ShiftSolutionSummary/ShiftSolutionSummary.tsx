import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useWindowWidth } from '@react-hook/window-size';
import Text from '../../../elements/Text/Text';
import CapacitySvg from '../../../../assets/chair-icon.svg?react';
import { capacityColor } from '../../../../helpers/capacity.helpers';
import { BREAKPOINT_MD } from '../../../../constants/breakpoints';
import { useAppSelector } from '../../../../hooks/storeHooks';
import { selectTheme, Theme } from '../../../../store/reducers/themeReducer';
import { IonIcon } from '@ionic/react';
import { busOutline } from 'ionicons/icons';

type ShiftSolutionSummaryProps = {
  capacity: number;
  routeName: string;
  routeColour: string;
  driverName: string;
  driverImage: string;
  vehicleName: string;
  registrationPlate: string;
  vehicleImage: string;
  simple?: boolean;
  className?: string;
};

const ShiftSolutionSummary = (props: ShiftSolutionSummaryProps) => {
  const { t } = useTranslation<['trip']>(['trip']);
  const theme: Theme = useAppSelector(selectTheme);

  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  return (
    <StyledShiftSolutionSummary className={props.className}>
      <StyledVehicleAndLabelWrapper>
        {props.vehicleImage ? (
          <StyledVehicleAvatar src={props.vehicleImage} size={56} />
        ) : (
          <StyledShuttleIcon
            icon={theme.icons?.fixedRouteVehicle ?? busOutline}
            iconColor={props.routeColour}
            size={'large'}
            isMobile={isMobile}
          />
        )}
        <div>
          <StyledRouteNameLabel size={1.1} weight={700}>
            {props.routeName}
          </StyledRouteNameLabel>
          <Text size={0.8} weight={500}>
            {props.vehicleName}
          </Text>
          <Text size={0.8} weight={500}>
            {props.registrationPlate}
          </Text>
        </div>
      </StyledVehicleAndLabelWrapper>
      <StyledRouteVehicleCapacityWrapper>
        <CapacitySvg height={25} color={capacityColor(props.capacity).themeValue} />
        <StyledCapacityText size={0.7} weight={isMobile ? 600 : 500} align="center" color={capacityColor(props.capacity).namedValue}>
          {props.capacity ? t('trip:numFree', { count: props.capacity }) : t('trip:full')}
        </StyledCapacityText>
      </StyledRouteVehicleCapacityWrapper>
    </StyledShiftSolutionSummary>
  );
};

ShiftSolutionSummary.displayName = 'ShiftSolutionSummary';

const StyledShiftSolutionSummary = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  padding: 0px 10px;
`;

const StyledRouteNameLabel = styled(Text)`
  text-transform: uppercase;
`;

const StyledVehicleAndLabelWrapper = styled.div`
  display: flex;
  align-items: center;
  margin-left: 4px;
`;

const StyledRouteVehicleCapacityWrapper = styled.div<{ backgroundColour?: string }>`
  display: flex;
  flex-direction: column;
  text-transform: uppercase;
  background-color: ${(props) => `rgba(var(--ion-color-${props.backgroundColour}-rgb), 0.1)` ?? 'transparent'};
  margin-left: 5px;
`;

const StyledVehicleAvatar = styled.img<{ size: number }>`
  width: ${(props) => props.size}px;
  height: ${(props) => props.size}px;
  border: 4px solid white;
  border-radius: ${(props) => props.size / 2}px;
  margin-right: 16px;
`;

const StyledShuttleIcon = styled(IonIcon)<{ iconColor: string; isMobile: boolean }>`
  color: ${(props) => props.iconColor};
  zoom: ${(props) => (props.isMobile ? 1.5 : 2)};
  margin-right: 16px;
`;

const StyledCapacityText = styled(Text)`
  margin-top: 5px;
`;

export default ShiftSolutionSummary;
