import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useWindowWidth } from '@react-hook/window-size';
import { locationSharp, busOutline } from 'ionicons/icons';
import Text from '../../../elements/Text/Text';
import { useAppSelector } from '../../../../hooks/storeHooks';
import { selectTheme, Theme } from '../../../../store/reducers/themeReducer';
import { calculateStopEta } from '../../../../helpers/date-format.helpers';
import RoundedIcon from '../../RoundedIcon/RoundedIcon';
import { getColourName } from '../../../../helpers/colour.helpers';
import { BREAKPOINT_MD } from '../../../../constants/breakpoints';

export type ShiftSolutionStopPointItem = {
  expectedArrivalTime: string;
  label: string;
  labelLong: string;
};

type ShiftSolutionStopPointProps = {
  point: ShiftSolutionStopPointItem;
  routeColour: string; // ion colour var name (eg. primary) or hex code
  isLastPoint?: boolean;
  isFirstPoint?: boolean;
  className?: string;
};

const ShiftSolutionStopPoint = (props: ShiftSolutionStopPointProps) => {
  const theme: Theme = useAppSelector(selectTheme);
  const { t } = useTranslation<['common', 'trip']>(['common', 'trip']);

  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  const stopEta: string = calculateStopEta(new Date(props.point.expectedArrivalTime), t('common:min'), t('common:mins'));

  return (
    <StyledShiftSolutionStopPoint className={props.className} isLastPoint={props.isLastPoint}>
      <StyledShiftSolutionStopPointLeftColumn>
        {props.isFirstPoint ? (
          <RoundedIcon
            icon={theme.icons?.fixedRouteVehicleRounded ?? busOutline}
            colour={theme.icons?.fixedRouteVehicleRounded ? props.routeColour : 'light'}
            backgroundColour={props.routeColour}
            iconWidth={40}
            iconSize={theme.icons?.fixedRouteVehicleRounded ? 'large' : 'small'}
            padding={theme.icons?.fixedRouteVehicleRounded ? 0 : 8}
          />
        ) : (
          <RoundedIcon icon={locationSharp} iconWidth={40} colour={props.routeColour} iconSize={'small'} padding={8} />
        )}
      </StyledShiftSolutionStopPointLeftColumn>

      <StyledShiftSolutionStopPointRightColumn isLastPoint={props.isLastPoint} colour={props.routeColour}>
        <StyledShiftSolutionStopPointHeaderRow>
          <StyledLeftColumn>
            {props.isFirstPoint && (
              <StyledNextStopInfo>
                <StyledNextStopText size={0.85} weight={500} color={'secondary'}>
                  {t('trip:nextStop')}
                </StyledNextStopText>
              </StyledNextStopInfo>
            )}
            <StyledShiftSolutionStopPointLegLabel>
              <StyledShiftSolutionPointLabel>
                <Text weight={isMobile ? 700 : 500} size={isMobile ? 0.9 : 1.0}>
                  {props.point.label}
                </Text>
              </StyledShiftSolutionPointLabel>
              <Text weight={isMobile ? 300 : 500} size={isMobile ? 0.8 : 0.75} color={isMobile ? 'primary' : 'secondary'}>
                {props.point.labelLong}
              </Text>
            </StyledShiftSolutionStopPointLegLabel>
          </StyledLeftColumn>

          <Text weight={isMobile ? 600 : 700} size={isMobile ? 0.8 : 0.85} color={'secondary'}>
            {stopEta}
          </Text>
        </StyledShiftSolutionStopPointHeaderRow>
      </StyledShiftSolutionStopPointRightColumn>
    </StyledShiftSolutionStopPoint>
  );
};

ShiftSolutionStopPoint.displayName = 'ShiftSolutionStopPoint';

const StyledLeftColumn = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
`;

const StyledNextStopText = styled(Text)`
  letter-spacing: 1px;
  margin-right: 5px;
  text-transform: uppercase;
`;

const StyledNextStopInfo = styled.div`
  display: flex;
  flex: 1;
  align-items: center;
  margin-bottom: 5px;
`;

const StyledShiftSolutionStopPoint = styled.div<{ isLastPoint?: boolean }>`
  display: flex;
  min-height: ${(props) => (props.isLastPoint ? 0 : 80)}px;
  padding-bottom: ${(props) => (props.isLastPoint ? 20 : 0)}px;
  flex-direction: row;
`;

const StyledShiftSolutionStopPointLeftColumn = styled.div`
  width: 50px;
  margin-right: -31px;
  z-index: 1;
`;

const StyledShiftSolutionStopPointRightColumn = styled.div<{ isLastPoint?: boolean; colour?: string }>`
  flex: 1;
  padding-left: 40px;
  padding-bottom: ${(props) => (props.isLastPoint ? 0 : 40)}px;
  border-left: ${(props) => (props.isLastPoint ? 0 : 2)}px solid ${(props) => (props.colour ? getColourName(props.colour) : '#0a6064')};
`;

const StyledShiftSolutionStopPointHeaderRow = styled.div`
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
`;

const StyledShiftSolutionStopPointLegLabel = styled.div``;

const StyledShiftSolutionPointLabel = styled.div`
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 4px;
`;

export default ShiftSolutionStopPoint;
