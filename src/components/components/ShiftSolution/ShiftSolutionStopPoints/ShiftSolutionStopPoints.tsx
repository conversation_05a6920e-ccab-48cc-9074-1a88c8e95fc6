import React from 'react';
import styled from 'styled-components';
import ShiftSolutionStopPoint, { ShiftSolutionStopPointItem } from './ShiftSolutionStopPoint';

type ShiftSolutionStopPointsProps = {
  points: ShiftSolutionStopPointItem[];
  routeColour: string;
  className?: string;
};

const ShiftSolutionStopPoints = (props: ShiftSolutionStopPointsProps) => {
  return (
    <StyledShiftSolutionStopPoints className={props.className}>
      {props.points.map<JSX.Element>((point: ShiftSolutionStopPointItem, index: number) => (
        <ShiftSolutionStopPoint
          routeColour={props.routeColour}
          isFirstPoint={index === 0}
          isLastPoint={index === props.points.length - 1}
          key={`${point.expectedArrivalTime}_${point.label}`}
          point={point}
        />
      ))}
    </StyledShiftSolutionStopPoints>
  );
};

const StyledShiftSolutionStopPoints = styled.div`
  overflow: visible;
`;

export default ShiftSolutionStopPoints;
