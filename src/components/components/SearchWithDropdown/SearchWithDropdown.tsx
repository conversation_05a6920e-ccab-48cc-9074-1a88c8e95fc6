import React, { ReactNode } from 'react';
import styled from 'styled-components';
import Searchbar, { SearchbarProps } from '../../elements/SearchBar/SearchBar';
import Dropdown from '../Dropdown/Dropdown';

export type SearchWithDropdownProps = SearchbarProps & {
  searchResults: ResultItem[];
  value: string;
  resultItem: ReactNode;
  className?: string;
};

type ResultItem = {
  id: string;
  title: string;
  label: string;
};

const SearchWithDropdown: React.FC<SearchWithDropdownProps> = (props: SearchWithDropdownProps) => {
  const { className, searchResults, value, resultItem, ...searchProps } = props;

  return (
    <StyledSearchWithDropdown className={className}>
      <StyledSearchbar value={value} {...searchProps} />

      <StyledDropdown options={searchResults} onSelect={() => {}} />
    </StyledSearchWithDropdown>
  );
};

const StyledSearchWithDropdown = styled.div``;

const StyledSearchbar = styled(Searchbar)`
  ion-searchbar {
    padding-left: 0;
    padding-right: 0;
  }
`;

const StyledDropdown = styled(Dropdown)`
  position: absolute;
  z-index: 100;
`;

export default SearchWithDropdown;
