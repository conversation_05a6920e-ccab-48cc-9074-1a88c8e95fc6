import React from 'react';
import styled from 'styled-components';
import Text from '../../elements/Text/Text';
import { IonIcon } from '@ionic/react';
import PopupWrapper from './PopupWrapper';
import { IonicReactProps } from '@ionic/react/dist/types/components/IonicReactProps';

type PopupButtonProps = {
  label: string;
  onClick: () => void;
  icon: string;
  iconSize?: 'small' | 'large';
  textSize?: number;
  extraIcon?: string;
  extraIconSize?: 'small' | 'large';
  className?: string;
  style?: IonicReactProps['style'];
};

const PopupButton = (props: PopupButtonProps) => {
  const iconSize: string | null = props.iconSize ? props.iconSize : props.style?.['fontSize'] ? '' : 'small';
  const extraIconSize: string | null = props.extraIconSize ? props.extraIconSize : props.style?.['fontsize'] ? '' : 'small';
  return (
    <StyledPopupButton className={props.className} onClick={props.onClick}>
      <IonIcon icon={props.icon} size={iconSize} style={props.style} />
      <StyledPopupButtonText size={props.textSize ?? 0.9} weight={600} align="left">
        {props.label}
      </StyledPopupButtonText>
      {props.extraIcon && <IonIcon icon={props.extraIcon} size={extraIconSize} style={props.style} />}
    </StyledPopupButton>
  );
};

const StyledPopupButton = styled(PopupWrapper)`
  cursor: pointer;
  position: absolute;
`;

const StyledPopupButtonText = styled(Text)`
  letter-spacing: 0;
  margin: 0 8px 0;
  font-family: var(--ion-font-family-header);
  position: relative;
  white-space: nowrap;
`;

export default PopupButton;
