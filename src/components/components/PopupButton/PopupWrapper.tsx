import React, { ReactNode } from 'react';
import styled from 'styled-components';

type PopupWrapperProps = {
  children: ReactNode;
  onClick?: () => void;
  className?: string;
};

const PopupWrapper = (props: PopupWrapperProps) => {
  return (
    <StyledPopupWrapper className={props.className} onClick={props.onClick}>
      {props.children}
    </StyledPopupWrapper>
  );
};

const StyledPopupWrapper = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 41px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.29);
  border-radius: 19px;
  padding: 0 15px;

  &:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.5);
  }

  ion-icon {
    --ionicon-stroke-width: 40px;
  }
`;

export default PopupWrapper;
