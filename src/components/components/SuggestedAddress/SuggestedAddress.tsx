import React from 'react';
import styled from 'styled-components';
import Text from '../../elements/Text/Text';
import HubIcon from '../../elements/Icon/HubIcon';

type SuggestedAddressProps = {
  icon: string;
  colour?: string;
  title: string;
  address: string;
  onClick: () => void;
  className?: string;
};

const SuggestedAddress = (props: SuggestedAddressProps) => {
  return (
    <StyledSuggestedAddress className={props.className} onMouseDown={props.onClick}>
      <HubIcon icon={props.icon} color={props.colour ?? 'primary'} style={{ fontSize: 22 }} />

      <StyledSuggestedAddressDetails>
        <StyledSuggestedAddressTitle size={0.8} weight={700}>
          {props.title}
        </StyledSuggestedAddressTitle>
        <StyledSuggestedAddressValue size={0.7}>{props.address}</StyledSuggestedAddressValue>
      </StyledSuggestedAddressDetails>
    </StyledSuggestedAddress>
  );
};

const StyledSuggestedAddress = styled.div`
  display: flex;
  flex-direction: row;
`;

const StyledSuggestedAddressDetails = styled.div`
  display: flex;
  flex-direction: column;
  margin-left: 5px;
`;

const StyledSuggestedAddressTitle = styled(Text)``;

const StyledSuggestedAddressValue = styled(Text)`
  margin-top: 2px;
`;

export default SuggestedAddress;
