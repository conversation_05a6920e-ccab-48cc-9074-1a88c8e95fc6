import React from 'react';
import MessageBlock from './MessageBlock';
import { warningOutline } from 'ionicons/icons';

type WarningMessageBlockProps = {
  message: string;
  title?: string;
  className?: string;
};

const WarningMessageBlock = (props: WarningMessageBlockProps) => {
  return <MessageBlock title={props.title} message={props.message} className={props.className} icon={warningOutline} color="warning" />;
};

export default WarningMessageBlock;
