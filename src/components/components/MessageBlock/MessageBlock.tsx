import React from 'react';
import { IonIcon } from '@ionic/react';
import styled from 'styled-components';
import Text, { TextProps } from '../../elements/Text/Text';

type MessageBlockProps = {
  message: string;
  icon: string;
  color: string;
  title?: string;
  weight?: TextProps['weight'];
  className?: string;
};

const MessageBlock = (props: MessageBlockProps) => {
  return (
    <StyledMessageBlock className={props.className}>
      <StyledIconContainer>
        <IonIcon color={props.color} icon={props.icon} size="large" />
      </StyledIconContainer>

      <StyledContainer>
        {props.title && (
          <Text weight={500} color={props.color}>
            {props.title}
          </Text>
        )}

        <Text color={props.color} weight={props.weight}>
          {props.message}
        </Text>
      </StyledContainer>
    </StyledMessageBlock>
  );
};

const StyledMessageBlock = styled.div`
  display: grid;
  align-items: center;
  grid-auto-flow: column;
  grid-template-columns: 40px 1fr;
`;

const StyledIconContainer = styled.div`
  padding-top: 10px;
  padding-bottom: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
`;

const StyledContainer = styled.div`
  padding-top: 10px;
  padding-bottom: 10px;
`;

export default MessageBlock;
