import React from 'react';
import MessageBlock from './MessageBlock';
import { alertCircleOutline } from 'ionicons/icons';

type ErrorMessageBlockProps = {
  message: string;
  title?: string;
  className?: string;
};

const ErrorMessageBlock = (props: ErrorMessageBlockProps) => {
  return (
    <MessageBlock
      title={props.title}
      message={props.message}
      className={props.className}
      icon={alertCircleOutline}
      color="danger"
      weight={500}
    />
  );
};

export default ErrorMessageBlock;
