import React from 'react';
import MessageBlock from './MessageBlock';
import { informationCircleOutline } from 'ionicons/icons';

type PrimaryMessageBlockProps = {
  message: string;
  title?: string;
  className?: string;
};

const PrimaryMessageBlock = (props: PrimaryMessageBlockProps) => {
  return (
    <MessageBlock title={props.title} message={props.message} className={props.className} icon={informationCircleOutline} color="primary" />
  );
};

export default PrimaryMessageBlock;
