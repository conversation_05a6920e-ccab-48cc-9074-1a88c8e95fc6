import React from 'react';
import MessageBlock from './MessageBlock';
import { checkmarkCircleOutline } from 'ionicons/icons';

type SuccessMessageBlockProps = {
  message: string;
  title?: string;
  className?: string;
};

const SuccessMessageBlock = (props: SuccessMessageBlockProps) => {
  return (
    <MessageBlock title={props.title} message={props.message} className={props.className} icon={checkmarkCircleOutline} color="success" />
  );
};

export default SuccessMessageBlock;
