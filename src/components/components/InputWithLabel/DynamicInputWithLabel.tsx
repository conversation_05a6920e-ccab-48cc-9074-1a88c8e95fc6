import React, { useState } from 'react';
import styled from 'styled-components';
import Text from '../../elements/Text/Text';
import { InputProps } from '../../elements/Input/Input';
import AddressAutoCompleteInput, { AutoCompleteExtraAddress, AutoCompleteSetAddress } from '../AutoComplete/AddressAutoCompleteInput';

type DynamicInputWithLabelProps = Omit<InputProps, 'onChange'> & {
  value: string;
  label: string;
  onChange: (location: AutoCompleteSetAddress) => void;
  prefix?: string;
  extraAddresses?: AutoCompleteExtraAddress[];
  displayBorder?: boolean;
  className?: string;
  forwardedRef?: React.RefObject<any>;
};

const DynamicInputWithLabel = (props: DynamicInputWithLabelProps) => {
  const { className, label, onChange, ...inputProps } = props;
  const [hasLabel, setHasLabel] = useState<boolean>(!!label);

  return (
    <StyledDynamicInputWithLabel className={className} hasLabel={!!label} displayBorder={props.displayBorder}>
      {hasLabel ? (
        <StyledDynamicInputWithLabelText size={0.8} weight={500}>
          {label}
        </StyledDynamicInputWithLabelText>
      ) : null}
      <AddressAutoCompleteInput {...inputProps} onSetAddress={onChange} hasLabel={hasLabel} setHasLabel={setHasLabel} />
    </StyledDynamicInputWithLabel>
  );
};

const StyledDynamicInputWithLabel = styled.div<{ hasLabel: boolean; displayBorder?: boolean }>`
  width: 100%;
  height: 50px;
  font-size: ${(props) => (props.hasLabel ? 1 : 1.2)}rem;
  border-bottom: ${(props) => (props.displayBorder ? 1 : 0)}px solid #999999;
  text-transform: uppercase;
  justify-content: center;
`;

const StyledDynamicInputWithLabelText = styled(Text)`
  margin-bottom: 5px;
  color: #999999;
  text-transform: capitalize;
`;

export default DynamicInputWithLabel;
