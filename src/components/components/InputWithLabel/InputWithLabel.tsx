import React from 'react';
import styled from 'styled-components';
import Input, { InputProps } from '../../elements/Input/Input';

type InputWithLabelProps = InputProps & {
  label: string;
  prefix?: string;
  className?: string;
};

const InputWithLabel = (props: InputWithLabelProps) => {
  const { className, label, ...inputProps } = props;

  return (
    <StyledInputWithLabel className={props.className}>
      <StyledInputWithLabelText>{props.label}</StyledInputWithLabelText>

      <Input {...inputProps} />
    </StyledInputWithLabel>
  );
};

const StyledInputWithLabel = styled.div`
  width: 100%;
`;

const StyledInputWithLabelText = styled.p`
  margin: 0 0 10px 0;
  font-weight: 700;
  font-size: 0.9rem;
  color: var(--ion-color-secondary);
`;

export default InputWithLabel;
