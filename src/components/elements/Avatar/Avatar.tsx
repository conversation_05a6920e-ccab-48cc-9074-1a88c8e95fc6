import React from 'react';
import { IonAvatar } from '@ionic/react';
import styled from 'styled-components';

export type AvatarProps = {
  src: string;
  className?: string;
};

const Avatar = (props: AvatarProps) => {
  return (
    <StyledAvatar className={props.className}>
      <IonAvatar>
        <img src={props.src} />
      </IonAvatar>
    </StyledAvatar>
  );
};

const StyledAvatar = styled.div`
  ion-avatar {
    width: 80px;
    height: 80px;
    margin-top: 5px;
  }
`;

export default Avatar;
