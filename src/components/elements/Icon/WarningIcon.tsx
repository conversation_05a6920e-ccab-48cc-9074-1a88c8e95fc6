import React from 'react';
import { IonIcon } from '@ionic/react';
import { warning } from 'ionicons/icons';
import styled from 'styled-components';

type WarningIconProps = {
  iconSize?: number;
};

const WarningIcon = (props: WarningIconProps) => <StyledIonIcon icon={warning} iconSize={props.iconSize} />;

const StyledIonIcon = styled(IonIcon)<{ iconSize: number | undefined }>`
  color: var(--ion-color-warning);
  font-size: ${({ iconSize }) => (iconSize ? iconSize : 22)}px;
  padding: 0 8px;
`;

export default WarningIcon;
