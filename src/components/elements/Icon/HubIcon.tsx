import React from 'react';
import { IonIcon } from '@ionic/react';
import {
  airplane,
  basket,
  basketball,
  boat,
  book,
  briefcase,
  bus,
  business,
  ellipse,
  heart,
  informationCircle,
  location,
  locationSharp,
  medkit,
  navigate,
  paw,
  star,
  storefront,
  train,
  umbrella,
  wine,
  chevronBackOutline,
} from 'ionicons/icons';
import styled from 'styled-components';
import { FALLBACK_DARK_COLOUR, getColourName } from '../../../helpers/colour.helpers';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export const HUB_ICON_MAP: { default: string } & Record<string, string> = {
  default: navigate,
  defaultAlt: locationSharp,
  back: chevronBackOutline,
  book: book,
  briefcase: briefcase,
  bus: bus,
  circle: ellipse,
  city: business,
  heart: heart,
  hippo: paw,
  hospital: medkit,
  hotel: informationCircle,
  plane: airplane,
  ship: boat,
  star: star,
  store: storefront,
  train: train,
  location: location,
  'basketball-ball': basketball,
  'glass-martini': wine,
  'shopping-basket': basket,
  'store-alt': storefront,
  'umbrella-beach': umbrella,
  'location-sharp': locationSharp,
};

type IonIconProps = React.ComponentProps<typeof IonIcon>;

type HubIconProps = IonIconProps & {
  // color could be ion colour var name (eg. primary) or hex code
  icon: string;
  isMapIcon?: boolean;
  className?: string;
};

const HubIcon = (props: HubIconProps) => {
  const isLocationHubIcon = props.isMapIcon && props.icon.startsWith('location');
  const icon = HUB_ICON_MAP[props.icon] ?? HUB_ICON_MAP.default;

  return (
    <StyledIcon colour={props.color}>
      {!isLocationHubIcon ? (
        <IonIcon {...props} icon={icon} />
      ) : (
        <FontAwesomeIcon icon="map-marker-alt" style={{ width: '26px', height: '26px' }} />
      )}
      {isLocationHubIcon && (
        <StyledIconBorder>
          <FontAwesomeIcon icon="map-marker" style={{ width: '26px', height: '26px' }} />
        </StyledIconBorder>
      )}
    </StyledIcon>
  );
};

const StyledIcon = styled.div<{ colour?: string }>`
  color: ${({ colour }) => (colour ? getColourName(colour) : FALLBACK_DARK_COLOUR)};
  display: flex;

  svg {
    z-index: 1;
  }
`;

const StyledIconBorder = styled.div`
  color: white;
  position: absolute;

  stroke: white;
  stroke-width: 55px;

  svg {
    z-index: -1;
  }
`;

export default HubIcon;
