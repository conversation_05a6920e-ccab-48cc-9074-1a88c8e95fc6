import React from 'react';
import styled from 'styled-components';
import HubIcon from './HubIcon';

type FloatingIconProps = {
  icon: string;
  size: number;
  action?: () => void;
  iconColour?: string;
  disabled?: boolean;
  className?: string;
};

const FloatingIcon = (props: FloatingIconProps) => {
  return (
    <StyledFloatingIcon className={props.className} size={props.size} onClick={props.action}>
      <HubIcon icon={props.icon} color={props.iconColour} />
    </StyledFloatingIcon>
  );
};

const StyledFloatingIcon = styled.div<{ size: number }>`
  height: ${(props) => props.size}px;
  width: ${(props) => props.size}px;
  border-radius: ${(props) => props.size}px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
`;

export default FloatingIcon;
