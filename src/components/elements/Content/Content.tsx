import React, { ReactNode } from 'react';
import { IonCard } from '@ionic/react';
import styled from 'styled-components';

type IonCardProps = React.ComponentProps<typeof IonCard>;

export type ContentProps = IonCardProps & {
  children?: ReactNode;
  className?: string;
};

const Content: React.FC<ContentProps> = (props: ContentProps) => {
  const { className, children, ...cardProps } = props;

  return (
    <StyledContent className={className}>
      <IonCard {...cardProps}>{children}</IonCard>
    </StyledContent>
  );
};

const StyledContent = styled.div`
  display: inline-block;

  ion-card {
    border-radius: 30px;
  }
`;

export default Content;
