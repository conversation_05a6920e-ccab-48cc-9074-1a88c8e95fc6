import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { IonButton, IonIcon, IonSpinner } from '@ionic/react';

type IonButtonProps = React.ComponentProps<typeof IonButton>;

export type ButtonProps = IonButtonProps & {
  block?: boolean;
  loading?: boolean;
  icon?: string;
  children?: ReactNode;
  className?: string;
};

const Button: React.FC<ButtonProps> = (props: ButtonProps) => {
  const { className, icon, block, children, loading, disabled, ...buttonProps } = props;

  return (
    <StyledButton className={className} block={block}>
      {/* if you've arrived here wanting to change the button colour away from primary, it's needed for Nike
         - so add a button colour to the theme and use that everywhere */}
      <StyledIonButton disabled={loading || disabled} color="primary" {...buttonProps}>
        {loading ? (
          <IonSpinner name="crescent" color={buttonProps.color} />
        ) : (
          <>
            {icon ? <IonIcon icon={icon} size="md" /> : null}
            {children}
          </>
        )}
      </StyledIonButton>
    </StyledButton>
  );
};

const StyledButton = styled.div<{ block?: boolean }>`
  width: ${(props) => (props.block ? '100%' : 'auto')};

  ion-icon {
    margin-right: 5px;
  }

  ion-button {
    width: 100%;
    font-family: var(--ion-font-family-header);
    font-weight: var(--lifty-button--weight);
    text-transform: var(--lifty-button--text-transform);
    --border-radius: 19px;
  }
`;

const StyledIonButton = styled(IonButton)`
  --border-width: 1px;
  --box-shadow: none;

  height: 40px;
  text-transform: none;
  letter-spacing: 0;
`;

export default Button;
