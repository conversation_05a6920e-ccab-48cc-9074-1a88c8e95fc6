import React from 'react';
import styled from 'styled-components';
import { IonIcon } from '@ionic/react';
import { chevronBack, chevronDown, chevronForward, chevronUp } from 'ionicons/icons';

const CHEVRON_ICON_MAP: Record<ChevronDirections, string> = {
  up: chevronUp,
  down: chevronDown,
  left: chevronBack,
  right: chevronForward,
};

type ChevronDirections = 'up' | 'down' | 'left' | 'right';

type ChevronButtonProps = {
  direction: ChevronDirections;
  onClick: () => void;
  size?: string;
  className?: string;
};

const ChevronButton = (props: ChevronButtonProps) => {
  return (
    <StyledChevronButton className={props.className} onClick={props.onClick}>
      <IonIcon icon={CHEVRON_ICON_MAP[props.direction]} size={props.size ?? 'large'} />
    </StyledChevronButton>
  );
};

const StyledChevronButton = styled.div`
  padding: 5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;

  &:active {
    background-color: #f7f7f7;
  }
`;

export default ChevronButton;
