import React from 'react';
import styled from 'styled-components';
import { IonIcon } from '@ionic/react';
import { add, remove } from 'ionicons/icons';

export type CountSelectorProps = {
  className?: string;
  count: number;
  onCountChange: (value: number) => void;
  minCount?: number;
  maxCount?: number;
};

const CountSelector: React.FC<CountSelectorProps> = (props: CountSelectorProps) => {
  const { className, onCountChange, count, minCount, maxCount } = props;

  const handleCount = (value: number): void => {
    if ((!minCount || value >= minCount) && (!maxCount || value <= maxCount)) {
      onCountChange(value);
    }
  };

  const decrement = () => handleCount(count - 1);

  const increment = () => handleCount(count + 1);

  return (
    <StyledWrapper className={className}>
      <StyledActionButtons onClick={decrement} disabled={!!minCount && count <= minCount}>
        <IonIcon icon={remove} size="large" />
      </StyledActionButtons>

      <StyledCountValue>{count}</StyledCountValue>

      <StyledActionButtons onClick={increment} disabled={!!maxCount && count >= maxCount}>
        <IonIcon icon={add} size="large" />
      </StyledActionButtons>
    </StyledWrapper>
  );
};

const StyledCountValue = styled.p`
  text-align: center;
  font: normal normal 600 24px/28px Rubik;
  letter-spacing: 0;
  color: #3a3a48;
  margin-left: 25px;
  margin-right: 25px;
`;

const StyledActionButtons = styled.div<{ disabled: boolean }>`
  cursor: ${(props) => (props.disabled ? 'initial' : 'pointer')};

  ion-icon {
    --ionicon-stroke-width: 50px;
    color: ${(props) => (props.disabled ? '#d8d8d8' : 'black')};
  }
`;

const StyledWrapper = styled.div`
  display: flex;
  align-items: center;
`;

export default CountSelector;
