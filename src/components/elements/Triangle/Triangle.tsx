import React from 'react';
import styled, { css } from 'styled-components';
import { CSSObject } from 'styled-components';

export enum Direction {
  Up = 'bottom',
  Down = 'top',
}

type TriangleProps = {
  size: number;
  direction: Direction;
  backgroundColor: string;
  className?: string;
};

const StyledTriangle = styled.div<{ direction: Direction; styleProps: CSSObject }>`
  width: 0;
  height: 0;
  background: transparent;
  border-style: solid;
  border-width: 5px;
  border-left-color: transparent;
  border-right-color: transparent;

  ${({ direction }) =>
    direction === Direction.Up &&
    css`
      border-top-width: 0;
      border-top-color: transparent;
    `}
  ${({ direction }) =>
    direction === Direction.Down &&
    css`
      border-bottom-width: 0;
      border-bottom-color: transparent;
    `}
  ${({ styleProps }) =>
    css`
      ${styleProps}
    `}
`;

const Triangle = ({ size, direction, backgroundColor, className }: TriangleProps) => {
  const borderColorStyle = `border-${direction}-color`;

  const styleProps = { [borderColorStyle]: backgroundColor, borderWidth: size };
  return <StyledTriangle direction={direction} className={className} styleProps={styleProps} />;
};

Triangle.Direction = Direction;

export default Triangle;
