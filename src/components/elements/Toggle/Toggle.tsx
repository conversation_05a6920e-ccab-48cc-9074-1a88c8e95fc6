import React from 'react';
import styled from 'styled-components';
import { IonToggle } from '@ionic/react';

type IonToggleProps = React.ComponentProps<typeof IonToggle>;

export type ToggleProps = IonToggleProps & {
  handleToggle: () => void;
  isToggled: boolean;
  className?: string;
};

const Toggle: React.FC<ToggleProps> = (props: ToggleProps) => {
  const { handleToggle, isToggled, className, ...toggleProps } = props;

  return (
    <StyledToggle className={className}>
      <IonToggle checked={isToggled} onIonChange={handleToggle} {...toggleProps} />
    </StyledToggle>
  );
};

const StyledToggle = styled.div`
  ion-toggle {
    --handle-width: 15px;

    width: 35px;
    height: 20px;
  }
`;

export default Toggle;
