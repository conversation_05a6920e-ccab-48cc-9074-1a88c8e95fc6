import React from 'react';
import styled from 'styled-components';

export type TextFontFamily = 'header' | 'body' | 'default';

export type TextProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLParagraphElement>, HTMLParagraphElement> & {
  ref?: React.Ref<HTMLParagraphElement>;
  children: string | number; // do we want to allow ReactNode here?
  align?: 'left' | 'right' | 'center' | 'justify';
  size?: number;
  lineHeight?: number;
  color?: string;
  weight?: 300 | 400 | 500 | 600 | 700;
  fontFamily?: TextFontFamily;
  maxWidth?: string;
  noWrap?: boolean;
  className?: string;
};

const Text = (props: TextProps) => {
  const {
    className,
    children,
    color = 'dark',
    size = 1,
    weight = 300,
    align = 'left',
    lineHeight = 1,
    fontFamily = 'default',
    maxWidth,
    noWrap,
    onClick,
    ...restProps
  } = props;

  return (
    <StyledText
      {...restProps}
      className={className}
      align={align}
      size={size}
      lineHeight={lineHeight}
      weight={weight}
      color={color}
      fontFamily={fontFamily}
      maxWidth={maxWidth}
      onClick={onClick}
      noWrap={noWrap}
    >
      {children}
    </StyledText>
  );
};

const StyledText = styled.span<
  Required<Pick<TextProps, 'align' | 'size' | 'weight' | 'fontFamily' | 'color'>> & {
    noWrap?: boolean;
    maxWidth?: string;
    lineHeight?: number;
  }
>`
  margin: 0;
  font-size: ${({ size }) => size}rem;
  font-weight: ${({ weight }) => weight};
  text-align: ${({ align }) => align};
  line-height: ${({ lineHeight }) => lineHeight};
  color: var(--ion-color-${({ color }) => color});
  font-family: var(${({ fontFamily }) => (fontFamily === 'header' ? '--ion-font-family-header' : '--ion-font-family')});
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: ${({ maxWidth }) => maxWidth ?? 'none'};
  white-space: ${({ noWrap }) => (noWrap ? 'nowrap' : 'normal')};
`;

export default Text;
