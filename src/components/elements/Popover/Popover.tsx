import React from 'react';
import styled from 'styled-components';
import { IonPopover } from '@ionic/react';

type IonPopoverProps = React.ComponentProps<typeof IonPopover>;

export type PopoverProps = IonPopoverProps & {
  className?: string;
};

const Popover: React.FC<PopoverProps> = (props: PopoverProps) => {
  const { className, ...popoverProps } = props;

  return <StyledPopover {...popoverProps} className={className} />;
};

const StyledPopover = styled(IonPopover)`
  &::part(content) {
    border-radius: 24px;
  }
`;

export default Popover;
