import React, { useEffect } from 'react';
import styled from 'styled-components';
import { IonIcon, IonToast } from '@ionic/react';
import Text from '../Text/Text';

type IonToastProps = React.ComponentProps<typeof IonToast>;

export type ToastBanner = {
  image: string;
  title: string;
  message?: string;
  messageCollapsed?: string;
};

// IonThumbnail use for image
export type ToastProps = IonToastProps &
  ToastBanner & {
    isOpen: boolean;
    duration?: number;
    ctaCallback?: () => void;
    onDidDismiss?: () => void;
    ctaIcon?: string;
    className?: string;
  };

const Toast: React.FC<ToastProps> = (props: ToastProps) => {
  const { duration, ctaIcon, isOpen, onDidDismiss, image, title, message, messageCollapsed, ctaCallback, className } = props;

  useEffect(() => {
    let timerID: NodeJS.Timeout;
    if (isOpen && duration && duration > 0 && typeof onDidDismiss === 'function') {
      timerID = setTimeout(() => onDidDismiss(), duration);
    }

    return () => {
      clearTimeout(timerID);
    };
  }, [duration]);

  return (
    <StyledToastWrapper className={className}>
      {isOpen || messageCollapsed ? (
        <>
          <StyledImage src={image} alt={title} />
          <StyledTextWrapper isOpen={isOpen}>
            <StyledTitle size={1} weight={500}>
              {title}
            </StyledTitle>
            {isOpen && message ? <StyledMessage size={0.8}>{message}</StyledMessage> : null}
            {!isOpen && messageCollapsed ? <StyledMessage size={0.8}>{messageCollapsed}</StyledMessage> : null}
          </StyledTextWrapper>
        </>
      ) : null}

      {ctaIcon && (
        <CtaWrapperShadow>
          <CtaWrapper onClick={ctaCallback}>
            <IonIcon icon={ctaIcon} color="light" size="large" />
          </CtaWrapper>
        </CtaWrapperShadow>
      )}
    </StyledToastWrapper>
  );
};

const StyledToastWrapper = styled.div`
  z-index: 999;
  position: absolute;
  display: flex;
  border-radius: 28px;
  background-color: white;
  box-shadow: 0 3px 6px #00000029;
  overflow-y: hidden;
`;

const StyledTextWrapper = styled.div<{ isOpen: boolean }>`
  padding: ${(props) => (props.isOpen ? 15 : 10)}px;
`;

const StyledImage = styled.img`
  border-top-left-radius: 28px;
  border-bottom-left-radius: 28px;
  width: 116px;
  margin: 5px;
  object-fit: cover;
  height: intrinsic;
`;

const StyledTitle = styled(Text)`
  overflow: hidden;
  text-overflow: ellipsis;
`;

const StyledMessage = styled(StyledTitle)`
  margin-top: 5px;
  line-height: 1.3rem;
`;

const CtaWrapperShadow = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 12px;
  height: 35px;
  width: 35px;
  min-width: 35px;
  border-radius: 17.5px;
  background-color: rgb(var(--ion-color-success-rgb), 0.3);
`;

const CtaWrapper = styled.div`
  height: 25px;
  width: 25px;
  border-radius: 12.5px;
  background-color: var(--color-lime-green);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  ion-icon {
    --ionicon-stroke-width: 26px;
  }
`;

export default Toast;
