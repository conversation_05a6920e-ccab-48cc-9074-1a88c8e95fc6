import React from 'react';
import styled from 'styled-components';
import { IonSpinner } from '@ionic/react';

type IonSpinnerProps = React.ComponentProps<typeof IonSpinner>;

export type SpinnerProps = IonSpinnerProps & {
  name?: 'bubbles' | 'circles' | 'circular' | 'crescent' | 'dots' | 'lines' | 'lines-small';
};

const Spinner: React.FC<SpinnerProps> = (props: SpinnerProps) => {
  const { className, ...spinnerProps } = props;

  return (
    <StyledSpinner className={className}>
      <IonSpinner {...spinnerProps} />
    </StyledSpinner>
  );
};

const StyledSpinner = styled.div``;

export default Spinner;
