import React from 'react';
import styled from 'styled-components';
import { IonSearchbar } from '@ionic/react';

type IonSearchbarProps = React.ComponentProps<typeof IonSearchbar>;

export type SearchbarProps = IonSearchbarProps & {
  className?: string;
  autocorrect?: 'off' | 'on';
  cancelButtonIcon?: string;
  cancelButtonText?: string;
  clearIcon?: string;
  debounce?: number;
  disabled?: boolean;
  enterkeyhint?: 'done' | 'enter' | 'go' | 'next' | 'previous' | 'search' | 'send';
  inputmode?: 'decimal' | 'email' | 'none' | 'numeric' | 'search' | 'tel' | 'text' | 'url';
  mode?: 'ios' | 'md';
  placeholder?: string;
  searchIcon?: string;
  showCancelButton?: 'always' | 'focus' | 'never';
  showClearButton?: 'always' | 'focus' | 'never';
  type?: 'email' | 'number' | 'password' | 'search' | 'tel' | 'text' | 'url';
  value: string;
};

const Searchbar: React.FC<SearchbarProps> = (props: SearchbarProps) => {
  const { className, showCancelButton = 'never', ...searchProps } = props;

  return (
    <StyledSearchBar className={className}>
      <IonSearchbar showCancelButton={showCancelButton} {...searchProps} />
    </StyledSearchBar>
  );
};

const StyledSearchBar = styled.div`
  ion-searchbar {
    --border-radius: 25px;
  }
`;

export default Searchbar;
