import React from 'react';
import styled from 'styled-components';
import Text from '../Text/Text';

type ToggleSwitchProps = {
  label: string;
  colour: string;
  isActive: boolean;
  onChange: (value: boolean) => void;
};

const SWITCH_DISABLED_COLOUR: string = '#929292';

const ToggleSwitch: React.FC<ToggleSwitchProps> = (props: ToggleSwitchProps) => {
  const backgroundColour: string = props.isActive ? props.colour : SWITCH_DISABLED_COLOUR;

  return (
    <StyledSwitchWrapper onClick={() => props.onChange(!props.isActive)}>
      <StyledSwitchBackground backgroundColour={backgroundColour} />
      <StyledSwitchCircle backgroundColour={backgroundColour} isActive={props.isActive}>
        <Text color="light" weight={700}>
          {props.label}
        </Text>
      </StyledSwitchCircle>
    </StyledSwitchWrapper>
  );
};

const StyledSwitchWrapper = styled.div`
  width: 64px;
  height: 34px;
  position: relative;
  filter: ;
`;

const StyledSwitchBackground = styled.div<{ backgroundColour: string }>`
  background-color: ${({ backgroundColour }) => backgroundColour};
  filter: opacity(0.3);
  border-radius: 50px;
  width: 100%;
  height: 100%;
`;

const StyledSwitchCircle = styled.div<{ backgroundColour: string; isActive: boolean }>`
  background-color: ${({ backgroundColour }) => backgroundColour};
  width: 34px;
  height: 100%;
  border-radius: 50px;
  position: absolute;
  left: ${({ isActive }) => (isActive ? 30 : 0)}px;
  top: 0;
  transition: left 500ms ease, background-color 500ms ease;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export default ToggleSwitch;
