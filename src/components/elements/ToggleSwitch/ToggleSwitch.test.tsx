import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ToggleSwitch from './ToggleSwitch';

describe('ToggleSwitch', () => {
  test('renders ToggleSwitch component', () => {
    render(<ToggleSwitch label="S" colour="#2196f3" isActive={false} onChange={vi.fn()} />);

    expect(screen.getByText('S')).toBeInTheDocument();
  });

  test('displays different label when active', () => {
    const { rerender } = render(<ToggleSwitch label="S" colour="#2196f3" isActive={false} onChange={vi.fn()} />);

    expect(screen.getByText('S')).toBeInTheDocument();

    rerender(<ToggleSwitch label="D" colour="#2196f3" isActive={true} onChange={vi.fn()} />);
    expect(screen.getByText('D')).toBeInTheDocument();
  });

  test('invokes onChange callback with toggled isActive value when clicked', () => {
    const onChangeMock = vi.fn();
    render(<ToggleSwitch label="S" colour="#2196f3" isActive={false} onChange={onChangeMock} />);

    fireEvent.click(screen.getByText('S'));
    expect(onChangeMock).toHaveBeenCalledWith(true);
  });
});
