import React, { ReactNode } from 'react';
import styled, { css } from 'styled-components';
import { getColourName } from '../../../helpers/colour.helpers';

type TooltipProps = {
  children: ReactNode;
  className?: string;
  colour?: string; // ion colour var name (eg. primary) or hex code
  bubblePadding?: string; // e.g "3px 7px"
  bubbleBorderRadius?: number;
  withShadow?: boolean;
};

const Tooltip: React.FC<TooltipProps> = (props: TooltipProps) => {
  return (
    <StyledTooltip className={props.className}>
      <StyledTooltipBubble
        colour={props.colour}
        padding={props.bubblePadding}
        borderRadius={props.bubbleBorderRadius}
        withShadow={props.withShadow}
      >
        {props.children}
      </StyledTooltipBubble>
      <StyledTooltipTail colour={props.colour} />
    </StyledTooltip>
  );
};

const StyledTooltip = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const StyledTooltipBubble = styled.div<{ colour?: string; padding?: string; borderRadius?: number; withShadow: boolean | undefined }>`
  background-color: ${({ colour }) => (colour ? getColourName(colour) : 'var(--ion-color-primary)')};
  border-radius: ${({ borderRadius }) => borderRadius}px;
  padding: ${({ padding }) => `${padding ?? '3px 7px'}`};
  overflow: hidden;

  ${({ withShadow }) => {
    if (withShadow) {
      return css`
        box-shadow: 0 3px 10px rgb(0 0 0 / 0.2);
      `;
    }
  }}
`;

const StyledTooltipTail = styled.div<{ colour?: string }>`
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid ${(props) => (props.colour ? getColourName(props.colour) : 'var(--ion-color-primary)')};
`;

export default Tooltip;
