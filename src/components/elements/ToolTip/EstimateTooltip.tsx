import React from 'react';
import TimeEstimate from '../../components/TimeEstimate/TimeEstimate';
import Tooltip from './Tooltip';

type EstimateTooltipProps = {
  label: string;
  className?: string;
};

const EstimateTooltip = (props: EstimateTooltipProps) => {
  return (
    <Tooltip className={props.className}>
      <TimeEstimate label={props.label} />
    </Tooltip>
  );
};

export default EstimateTooltip;
