import React from 'react';
import styled from 'styled-components';
import { IonInput } from '@ionic/react';

type IonInputProps = React.ComponentProps<typeof IonInput>;

export type InputProps = IonInputProps & {
  prefix?: string;
  className?: string;
};

const Input: React.FC<InputProps> = (props: InputProps) => {
  const { className, prefix, ...inputProps } = props;

  return (
    <StyledInput className={className}>
      {prefix ? <div>{prefix}</div> : null}
      <IonInput {...inputProps} />
    </StyledInput>
  );
};

const StyledInput = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background-color: #efefef;
  border-radius: 19px;
  padding: 0 20px;
  color: #737373;
  height: 40px;
  font-family: var(--lifty-input--font-family);

  ion-input {
    --color: #737373;
  }
`;

export default Input;
