import { FixedRoute } from '@liftango/ops-client';

export const generateMockRouteData = (): FixedRoute.ServiceRoutesAndStopsPayload[] => {
  return [
    {
      id: 'route-1',
      versionId: 'version-1',
      label: 'Test Route 1',
      description: 'Test Route 1',
      colour: '#FF6B6B',
      operatingIntervals: [],
      stops: [
        {
          id: 'stop-1',
          name: 'Stop 1',
          label: 'Stop 1',
          description: 'Test Stop 1',
          addressLabel: 'Test Stop 1',
          addressId: 1,
          placeId: 'place-1',
          latitude: -32.922677,
          longitude: 151.747167,
          icon: 'bus',
          colour: '#FF6B6B',
          snapRadius: 50,
        },
        {
          id: 'stop-2',
          name: 'Stop 2',
          label: 'Stop 2',
          description: 'Test Stop 2',
          addressLabel: 'Test Stop 2',
          addressId: 2,
          placeId: 'place-2',
          latitude: -32.925677,
          longitude: 151.750167,
          icon: 'bus',
          colour: '#FF6B6B',
          snapRadius: 50,
        },
      ],
      routeShape: {
        id: 'shape-1',
        routeVersionId: 'version-1',
        routeName: 'Test Route 1',
        colour: '#FF6B6B',
        orderedCoordinates: [
          { latitude: -32.922677, longitude: 151.747167 },
          { latitude: -32.925677, longitude: 151.750167 },
        ],
      },
    },
    {
      id: 'route-2',
      versionId: 'version-2',
      label: 'Test Route 2',
      description: 'Test Route 2',
      colour: '#4ECDC4',
      operatingIntervals: [],
      stops: [
        {
          id: 'stop-3',
          name: 'Stop 3',
          label: 'Stop 3',
          description: 'Test Stop 3',
          addressLabel: 'Test Stop 3',
          addressId: 3,
          placeId: 'place-3',
          latitude: -32.920677,
          longitude: 151.745167,
          icon: 'bus',
          colour: '#4ECDC4',
          snapRadius: 50,
        },
        {
          id: 'stop-4',
          name: 'Stop 4',
          label: 'Stop 4',
          description: 'Test Stop 4',
          addressLabel: 'Test Stop 4',
          addressId: 4,
          placeId: 'place-4',
          latitude: -32.918677,
          longitude: 151.743167,
          icon: 'bus',
          colour: '#4ECDC4',
          snapRadius: 50,
        },
      ],
      routeShape: {
        id: 'shape-2',
        routeVersionId: 'version-2',
        routeName: 'Test Route 2',
        colour: '#4ECDC4',
        orderedCoordinates: [
          { latitude: -32.920677, longitude: 151.745167 },
          { latitude: -32.918677, longitude: 151.743167 },
        ],
      },
    },
  ];
};
