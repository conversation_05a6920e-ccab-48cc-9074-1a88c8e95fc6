import React, { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { OnDemandServiceHub } from '@liftango/liftango-client';
import MapComponent, { JourneyPointList, MapPadding, Waypoint } from '../../components/features/Map/Map';
import useScheduleLocation, { SchedulePositionsDictionary } from '../../hooks/useScheduleLocation';
import { FixedRoute } from '@liftango/ops-client';

type MapViewProps = {
  initialCenter: {
    latitude: number;
    longitude: number;
  };
  regionCode: string;
  serviceHubs: OnDemandServiceHub[];
  stops: FixedRoute.ServiceRouteStop[];
  hideTripPlanner: () => void;
  showTripPlanner: () => void;
  routeShapes: FixedRoute.ServiceRouteShape[];
  isTripPlanerOpen?: boolean;
  onMapStopPressed?: (stop: FixedRoute.ServiceRouteStop) => void;
  networkId?: string;
  journeyPoints?: JourneyPointList[];
  mapPadding?: MapPadding;
  style?: React.CSSProperties;
  showUserPosition?: boolean;
  watchUserPosition?: boolean;
  schedulesToWatch?: string[];
  waypoints?: Waypoint[];
  center?: {
    latitude: number;
    longitude: number;
  };
  renderDirections?: boolean;
  onMapItemSelected?: Dispatch<SetStateAction<boolean>>;
  firstStopRef?: React.ForwardedRef<HTMLButtonElement>;
};

const MapView: React.FC<MapViewProps> = (props: MapViewProps) => {
  const [userPosition, setUserPosition] = useState<GeolocationPosition | null>(null);

  const schedulePositions: SchedulePositionsDictionary = useScheduleLocation(props.networkId);

  const routeShapes: FixedRoute.ServiceRouteShape[] = props.routeShapes;

  const center = useMemo<
    | {
        lat: number;
        lng: number;
      }
    | undefined
  >(() => {
    return props.center
      ? {
          lat: props.center.latitude,
          lng: props.center.longitude,
        }
      : undefined;
  }, [props.center?.latitude, props.center?.longitude]);

  const initialCenter = useMemo<{
    lat: number;
    lng: number;
  }>(
    () => ({
      lat: props.initialCenter.latitude,
      lng: props.initialCenter.longitude,
    }),
    [props.initialCenter.latitude, props.initialCenter.longitude],
  );

  // @ts-ignore Type 'IterableIterator<ServiceRouteStop>' can only be iterated through when using the '--downlevelIteration' flag or with a '--target' of 'es2015' or higher.
  const stops = [...new Map(props.stops.map((stop: FixedRoute.ServiceRouteStop) => [stop.placeId, stop])).values()];

  useEffect(() => {
    if (props.showUserPosition && navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position: GeolocationPosition) => {
        setUserPosition(position);
      });
    } else {
      setUserPosition(null);
    }
  }, [props.showUserPosition]);

  useEffect(() => {
    if (props.watchUserPosition && navigator.geolocation) {
      navigator.geolocation.watchPosition((position: GeolocationPosition) => {
        setUserPosition(position);
      });
    } else {
      setUserPosition(null);
    }
  }, [props.watchUserPosition]);

  return (
    <MapComponent
      firstStopRef={props.firstStopRef}
      center={center}
      initalCenter={initialCenter}
      regionCode={props.regionCode}
      style={props.style}
      serviceHubs={props.serviceHubs}
      routeShapes={routeShapes}
      stops={stops}
      journeyPoints={props.journeyPoints}
      mapPadding={props.mapPadding}
      userPosition={userPosition}
      schedulePositions={schedulePositions}
      waypoints={props.waypoints}
      onMapStopPressed={props.onMapStopPressed}
      showTripPlanner={props.showTripPlanner}
      hideTripPlanner={props.hideTripPlanner}
      isTripPlanerOpen={props.isTripPlanerOpen}
      renderDirections={props.renderDirections}
      onMapItemSelected={props.onMapItemSelected}
    />
  );
};

export default MapView;
