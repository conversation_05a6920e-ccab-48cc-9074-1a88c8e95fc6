import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import styled, { css } from 'styled-components';
import { useWindowWidth } from '@react-hook/window-size';
import { IonIcon } from '@ionic/react';
import { chevronBackOutline } from 'ionicons/icons';
import { FixedRouteMatch } from '../../../../machines/FixedRoute/routePlanner';
import RouteSummary from '../../../../components/components/Route/RouteSummary/RouteSummary';
import Text from '../../../../components/elements/Text/Text';
import { TIME_FORMAT } from '../../../../constants/formatting';
import Button from '../../../../components/elements/Button/Button';
import RoutePoints from '../../../../components/components/Route/RoutePoints/RoutePoints';
import BottomSwipeUpCard from '../../../../components/components/BottomSwipeUpCard/BottomSwipeUpCard';
import { BREAKPOINT_MD } from '../../../../constants/breakpoints';

type SelectedRouteOverviewProps = {
  trip: FixedRouteMatch;
  unitOfMeasurement: 'imperial' | 'metric';
  isExpandedInitially: boolean;
  onStart: () => void;
  className?: string;
  onBack?: () => void;
};

const SelectedRouteOverview = (props: SelectedRouteOverviewProps) => {
  const { t } = useTranslation<['common', 'trip']>(['common', 'trip']);

  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  const SwipeUpSummary: JSX.Element = (
    <StyledSelectedRouteSummaryWrapper>
      <RouteSummary route={props.trip.journeyData.journey.journeyLegs} unitOfMeasurement={props.unitOfMeasurement} />
    </StyledSelectedRouteSummaryWrapper>
  );

  const SwipeUpContent: JSX.Element = (
    <StyledSelectedRoutePointsWrapper>
      <RoutePoints points={props.trip.journeyData.journey.journeyLegs} unitOfMeasurement={props.unitOfMeasurement} />
    </StyledSelectedRoutePointsWrapper>
  );

  const RouteDetails: JSX.Element = (
    <StyledRouteDetails isMobile={isMobile}>
      <StyledSelectedRouteInformation isMobile={isMobile}>
        <StyledSelectedRouteDestinationLabel weight={700} fontFamily="header" size={1.1}>
          {props.trip.endAddress.label}
        </StyledSelectedRouteDestinationLabel>
        <StyledSelectedRouteTimeLabel weight={500} size={0.7}>{`${format(new Date(props.trip.endTime), TIME_FORMAT)} ${t(
          'common:Arrival',
        )}`}</StyledSelectedRouteTimeLabel>
      </StyledSelectedRouteInformation>
      <StyledStartTripButton isMobile={isMobile} fill="solid" onClick={props.onStart} block={!isMobile}>
        {t('common:Start')}
      </StyledStartTripButton>
    </StyledRouteDetails>
  );

  return isMobile ? (
    <StyledMobileOverviewWrapper>
      <BottomSwipeUpCard isExpandedInitially={props.isExpandedInitially} summary={SwipeUpSummary} details={SwipeUpContent} />
      {RouteDetails}
    </StyledMobileOverviewWrapper>
  ) : (
    <StyledDesktopOverviewWrapper>
      <StyledHeaderSection>
        <StyledBackButton icon={chevronBackOutline} size={'small'} onClick={props.onBack} />
        <StyledHeaderSectionLabel align="center" weight={500} size={isMobile ? 0.8 : 1} color={'secondary'} fontFamily={'body'}>
          {t('trip:selectedJourney')}
        </StyledHeaderSectionLabel>
      </StyledHeaderSection>

      <StyledSummaryDesktopWrapper>
        <RouteSummary route={props.trip.journeyData.journey.journeyLegs} unitOfMeasurement={props.unitOfMeasurement} />
      </StyledSummaryDesktopWrapper>

      <StyledRoutePointsDesktopWrapper>
        <RoutePoints points={props.trip.journeyData.journey.journeyLegs} unitOfMeasurement={props.unitOfMeasurement} />
      </StyledRoutePointsDesktopWrapper>

      {RouteDetails}
    </StyledDesktopOverviewWrapper>
  );
};

const StyledMobileOverviewWrapper = styled.div`
  height: 100%;
  width: 100%;
  padding-bottom: env(safe-area-inset-bottom);
  flex-direction: column;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
`;

const StyledRouteDetails = styled.div<{ isMobile: boolean }>`
  display: flex;
  width: 100%;
  background-color: white;
  flex-direction: ${(props) => (props.isMobile ? 'row' : 'column')};
  padding: ${(props) => (props.isMobile ? '10px 30px 20px 30px' : '23px 0 0 0')};
  justify-content: ${(props) => (props.isMobile ? 'space-between' : 'center')};
  align-items: center;
  ${(props) =>
    props.isMobile &&
    css`
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
    `};
  ${(props) =>
    !props.isMobile &&
    css`
      border-top: 1px solid #eeeeee;
      margin-top: 8px;
    `}
  z-index: 3;
`;

const StyledSelectedRouteInformation = styled.div<{ isMobile: boolean }>`
  display: flex;
  flex-direction: column;

  ${(props) =>
    !props.isMobile &&
    css`
      align-items: center;
    `}
`;

const StyledStartTripButton = styled(Button)<{ isMobile: boolean }>`
  margin: ${(props) => (props.isMobile ? '0' : '16px 0 8px 0')};
`;

const StyledBackButton = styled(IonIcon)`
  border-radius: 36px;
  background-color: #f2f2f2;
  width: 20px;
  height: 20px;
  padding: 0.1em 0.1em;
  cursor: pointer;
`;

const StyledDesktopOverviewWrapper = styled.div`
  flex: 1;
  margin: 20px 16px;
`;

const StyledHeaderSection = styled.div`
  display: flex;
  flex-direction: row;
  column-gap: 80px;
  align-items: center;
  margin-bottom: 24px;
`;

const StyledHeaderSectionLabel = styled(Text)`
  text-transform: uppercase;
`;

const StyledSummaryDesktopWrapper = styled.div`
  padding: 10px;
  border-radius: 20px;
  background-color: #f0f0f0;
  margin-bottom: 24px;
`;

const StyledRoutePointsDesktopWrapper = styled.div`
  padding: 0 16px 10px;
  overflow: auto;
`;

const StyledSelectedRoutePointsWrapper = styled.div`
  padding: 30px 0 0px;
`;

const StyledSelectedRouteSummaryWrapper = styled.div`
  padding: 10px;
  border-radius: 30px;
  background-color: #f0f0f0;
`;

const StyledSelectedRouteDestinationLabel = styled(Text)`
  text-transform: uppercase;
  margin-bottom: 5px;
`;

const StyledSelectedRouteTimeLabel = styled(Text)``;

export default SelectedRouteOverview;
