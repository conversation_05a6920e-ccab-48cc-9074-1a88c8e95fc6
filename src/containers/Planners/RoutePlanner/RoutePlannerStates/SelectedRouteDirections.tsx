import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { IonSlide, IonSlides } from '@ionic/react';
import { OnDemandJourneyLegAddress } from '@liftango/liftango-client';
import { walkOutline } from 'ionicons/icons';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { FixedRouteMatchLeg } from '../../../../machines/FixedRoute/routePlanner';
import DirectionsSlide from '../../../../components/components/DirectionsSlide/DirectionsSlide';
import { DATE_TIME_FORMAT } from '../../../../constants/formatting';
import { DistanceMeasurement, formatDistanceMeasurment, getDistanceSuffix } from '../../../../helpers/journey.helpers';
import { LONG_WALK_WARNING_THRESHOLD_IN_METERS } from '../../../../constants/map';
import { ABOVE_MAP_Z_INDEX } from '../../../../constants/positioning';

type MappedLeg = {
  key: string;
  title: string;
  address: OnDemandJourneyLegAddress;
  travelType: 'foot' | 'bus';
  subtitle?: string;
  icon?: string;
  vehicle?: {
    image: string;
    number: string;
    displayName: string;
  };
  button?: {
    action: () => void;
    actionLabel: string;
  };
  isLongWalk?: boolean;
};

type SelectedRouteDirectionsProps = {
  route: FixedRouteMatchLeg[];
  unitOfMeasurement: 'imperial' | 'metric';
  onComplete: () => void;
  onSwipe: (renderDirections: boolean, address?: OnDemandJourneyLegAddress) => void;
  className?: string;
};

const SelectedRouteDirections = (props: SelectedRouteDirectionsProps) => {
  const { t } = useTranslation<['common', 'trip']>(['common', 'trip']);
  const [legs, setLegs] = useState<MappedLeg[]>([]);
  const [currentLegIndex, setCurrentLeg] = useState<number>(0);

  const truncateLabel = (label: string): string => {
    const firstSection: string = label.split(',')[0];
    return firstSection.length > 25 ? firstSection.substring(0, 22) + '...' : firstSection;
  };

  useEffect(() => {
    if (props.route.length) {
      const legs: MappedLeg[] = [];
      const lastLeg: FixedRouteMatchLeg | undefined = props.route[props.route.length - 1];
      for (const leg of props.route) {
        if (leg.travelType === 'foot') {
          const formattedDistance: DistanceMeasurement = formatDistanceMeasurment(leg.distance, props.unitOfMeasurement);
          const distanceSuffix: string = getDistanceSuffix(formattedDistance.distance, formattedDistance.unit);
          legs.push({
            key: `${leg.travelType}_${leg.startTime}_${leg.endTime}`,
            travelType: leg.travelType,
            icon: walkOutline,
            title: t('trip:walkTo', { address: truncateLabel(leg.endAddress.label) }),
            subtitle: t('trip:footJourneyLeg', {
              distance: `${formattedDistance.distance} ${distanceSuffix}`,
              count: leg.travelTime,
            }),
            address: leg.endAddress,
            isLongWalk: leg.distance > LONG_WALK_WARNING_THRESHOLD_IN_METERS,
          });
        } else {
          legs.push(
            {
              key: `${leg.travelType}_board_${leg.startTime}_${leg.endTime}`,
              travelType: leg.travelType,
              title: t('trip:boardShuttle'),
              subtitle: leg.startAddress.hubInfo?.name || leg.startAddress.label,
              vehicle: leg.vehicle,
              address: leg.startAddress,
            },
            {
              key: `${leg.travelType}_exit_${leg.startTime}_${leg.endTime}`,
              travelType: leg.travelType,
              title: t('trip:exitShuttleAt', { address: leg.endAddress.hubInfo?.name || truncateLabel(leg.endAddress.label) }),
              subtitle: leg.endAddress.hubInfo?.name
                ? truncateLabel(leg.endAddress.label)
                : format(new Date(leg.endTime), DATE_TIME_FORMAT),
              address: leg.endAddress,
            },
          );
        }
      }

      if (lastLeg) {
        legs.push({
          key: `${lastLeg.travelType}_exit_${lastLeg.startTime}_${lastLeg.endTime}`,
          travelType: lastLeg.travelType,
          title: t('trip:arrivedAt', { address: lastLeg.endAddress.hubInfo?.name || truncateLabel(lastLeg.endAddress.label) }),
          button: {
            action: props.onComplete,
            actionLabel: t('trip:completeTrip'),
          },
          address: lastLeg.endAddress,
        });
      }

      setLegs(legs);
    }
  }, [props.route, props.unitOfMeasurement]);

  useEffect(() => {
    if (legs[currentLegIndex]) {
      props.onSwipe(legs[currentLegIndex].travelType === 'foot', legs[currentLegIndex].address);
    }
  }, [currentLegIndex, legs]);

  return (
    <StyledSelectedRouteDirections className={props.className}>
      <IonSlides
        options={{ static: false }}
        pager={true}
        onIonSlidePrevEnd={() => setCurrentLeg((previousLeg) => previousLeg - 1)}
        onIonSlideNextEnd={() => setCurrentLeg((previousLeg) => previousLeg + 1)}
      >
        {legs.map((leg: any) => (
          <IonSlide key={leg.key}>
            <DirectionsSlide
              icon={leg.icon}
              title={leg.title}
              subtitle={leg.subtitle}
              vehicle={leg.vehicle}
              button={leg.button}
              isLongWalk={leg.isLongWalk}
            />
          </IonSlide>
        ))}
      </IonSlides>
    </StyledSelectedRouteDirections>
  );
};

const StyledSelectedRouteDirections = styled.div`
  height: 220px;
  z-index: ${ABOVE_MAP_Z_INDEX};
  .swiper-pagination-fraction,
  .swiper-pagination-custom,
  .swiper-container-horizontal > .swiper-pagination-bullets {
    bottom: 60px;
  }
`;

export default SelectedRouteDirections;
