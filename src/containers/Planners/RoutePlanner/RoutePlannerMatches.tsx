import { FixedRouteMatch } from '../../../machines/FixedRoute/routePlanner';
import Button from '../../../components/elements/Button/Button';
import { reloadOutline } from 'ionicons/icons';
import React from 'react';
import styled from 'styled-components';
import Text from '../../../components/elements/Text/Text';
import RouteSummaryCard from '../../../components/components/Route/RouteCards/RouteSummaryCard';
import { useTranslation } from 'react-i18next';
import { useWindowWidth } from '@react-hook/window-size';
import { BREAKPOINT_MD } from '../../../constants/breakpoints';

type RoutePlannerMatchesProps = {
  recommended: FixedRouteMatch | null;
  alternate: FixedRouteMatch[] | null;
  handleViewTrip: (trip: FixedRouteMatch) => void;
  unitOfMeasurement: 'imperial' | 'metric';
  trip: FixedRouteMatch | null;
  handleStartAgain: () => void;
  routePlannerDepartureDateTime: string | null;
};

const RoutePlannerMatches = (props: RoutePlannerMatchesProps) => {
  const { t } = useTranslation<['common', 'booking', 'trip']>(['common', 'booking', 'trip']);
  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  return (
    props.recommended && (
      <StyledRoutePlannerContent>
        <StyledRoutePlannerOfferedTripLabel align="center" weight={500} size={isMobile ? 0.9 : 1} color={'secondary'}>
          {t('trip:recommended')}
        </StyledRoutePlannerOfferedTripLabel>
        <StyledPlannerRouteSummaryCard
          action={() => {
            if (props.recommended) {
              props.handleViewTrip(props.recommended);
            }
          }}
          trip={props.recommended}
          unitOfMeasurement={props.unitOfMeasurement}
          isSelected={props.trip === props.recommended}
          routePlannerDepartureDateTime={props.routePlannerDepartureDateTime}
        />

        {props.alternate && props.alternate.length > 0 && (
          <StyledRoutePlannerOfferedAlternativeSection>
            <StyledRoutePlannerOfferedTripLabel align="center" weight={600} size={0.8}>
              {t('trip:alternative')}
            </StyledRoutePlannerOfferedTripLabel>
            {props.alternate.map((altTrip: FixedRouteMatch) => (
              <StyledPlannerRouteSummaryCard
                key={`${altTrip.endTime}_${altTrip.startTime}_${altTrip.startAddress.placeId}_${altTrip.endAddress.placeId}`}
                action={() => props.handleViewTrip(altTrip)}
                trip={altTrip}
                unitOfMeasurement={props.unitOfMeasurement}
                isSelected={props.trip === altTrip}
                routePlannerDepartureDateTime={props.routePlannerDepartureDateTime}
              />
            ))}
          </StyledRoutePlannerOfferedAlternativeSection>
        )}

        {!isMobile && (
          <Button block fill="outline" icon={reloadOutline} onClick={props.handleStartAgain}>
            {t('booking:Startover')}
          </Button>
        )}
      </StyledRoutePlannerContent>
    )
  );
};

const StyledRoutePlannerContent = styled.div`
  padding: 15px 30px 30px;
  background-color: white;
`;

const StyledRoutePlannerOfferedTripLabel = styled(Text)`
  margin: 10px 0;
  text-transform: uppercase;
`;

const StyledRoutePlannerOfferedAlternativeSection = styled.div`
  margin-top: 40px;
`;

const StyledPlannerRouteSummaryCard = styled(RouteSummaryCard)<{ isSelected: boolean }>`
  border-radius: 20px;
  margin-bottom: 10px;
  cursor: pointer;
  border: 2px solid ${(props) => (props.isSelected ? '#e3ebec' : 'white')};

  &:hover {
    border: 2px solid #e3ebec;
  }

  &:active {
    opacity: 0.8;
  }
`;

export default RoutePlannerMatches;
