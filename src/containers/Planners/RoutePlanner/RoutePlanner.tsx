import React, { useEffect, createRef, useState } from 'react';
import { useWindowWidth } from '@react-hook/window-size';
import { useTranslation } from 'react-i18next';
import PlacesAutocomplete from 'react-places-autocomplete';
import { useMachine } from '@xstate/react';
import { format } from 'date-fns';
import styled, { css } from 'styled-components';
import { locationSharp } from 'ionicons/icons';
import { OnDemandJourneyLegAddress, OnDemandServiceHub, OnDemandSettingsSolutionDates } from '@liftango/liftango-client';
import { pointsWithinDistance } from '@liftango/polygon-point';
import { HailerOpsClient, FixedRoute } from '@liftango/ops-client';
import LocationsIcons from '../../../components/components/LocationsIcons/LocationsIcons';
import Content from '../../../components/elements/Content/Content';
import { TimeType } from '../../../machines/Kiosk/kioskBooking';
import { BREAKPOINT_MD, BREAKPOINT_SM } from '../../../constants/breakpoints';
import { Network } from '../../../store/reducers/networkReducer';
import { useHailerService } from '../../../services/HailerService';
import ErrorMessageBlock from '../../../components/components/MessageBlock/ErrorMessageBlock';
import LoadingMask from '../../../components/components/LoadingMask/LoadingMask';
import {
  FIXED_ROUTE_MACHINE_CONTEXT,
  FixedRouteMachine,
  FixedRouteMatch,
  FixedRouteMatchLeg,
  Location,
} from '../../../machines/FixedRoute/routePlanner';
import { TIME_FORMAT } from '../../../constants/formatting';
import DateTimeSelector from '../../../components/features/DateTimeSelector/DateTimeSelector';
import SelectedRouteOverview from './RoutePlannerStates/SelectedRouteOverview';
import SelectedRouteDirections from './RoutePlannerStates/SelectedRouteDirections';
import PassengerIncrementor from '../../../components/components/Passenger/PassengerIncrementor';
import PopupWrapper from '../../../components/components/PopupButton/PopupWrapper';
import DynamicInputWithLabel from '../../../components/components/InputWithLabel/DynamicInputWithLabel';
import { AutoCompleteExtraAddress, AutoCompleteSetAddress } from '../../../components/components/AutoComplete/AddressAutoCompleteInput';
import RoutePlannerMatches from './RoutePlannerMatches';
import { FALLBACK_DARK_COLOUR } from '../../../helpers/colour.helpers';
import { dateRoundedDownToNearestMinute, dateRoundedUpToNearestMinutesInterval } from '../../../helpers/date-format.helpers';
import { ABOVE_MAP_Z_INDEX } from '../../../constants/positioning';
import { MAP_DARKENING_OVERLAY_COLOUR } from '../../../constants/map';

type RoutePlannerProps = {
  network: Network;
  stops: FixedRoute.ServiceRouteStop[];
  serviceHubs: OnDemandServiceHub[];
  serviceLockTime: number;
  solutionDates: OnDemandSettingsSolutionDates[];
  goBackPressedAt: number;
  onTripStateChange: (context: FIXED_ROUTE_MACHINE_CONTEXT) => void;
  onRecenterMap: (center: { latitude: number; longitude: number } | undefined) => void;
  tripMachine: FixedRouteMachine;
  isTripPlannerOpen: boolean;
  renderMapDirections: (render: boolean) => void;
  setMapDeparture: FixedRoute.ServiceRouteStop | null;
  setIsTripPlannerOpen: (isOpen: boolean) => void;
  setIsTripStarted: (isOpen: boolean) => void;
  isFloating?: boolean;
  hiddenByOverlay?: boolean;
  className?: string;
};

const RoutePlanner = (props: RoutePlannerProps) => {
  const hailerService = useHailerService();
  const { t } = useTranslation<['common', 'booking', 'trip']>(['common', 'booking', 'trip']);

  const isMobile: boolean = useWindowWidth() < BREAKPOINT_MD;

  const [routePlannerState, dispatchRoutePlannerUpdate] = useMachine(props.tripMachine);
  // @ts-ignore Type 'IterableIterator<ServiceRouteStop>' can only be iterated through when using the '--downlevelIteration' flag or with a '--target' of 'es2015' or higher.
  const stops = [...new Map(props.stops.map((stop: FixedRoute.ServiceRouteStop) => [stop.placeId, stop])).values()];
  const serviceHubsAndStops: (OnDemandServiceHub | FixedRoute.ServiceRouteStop)[] = [...props.serviceHubs, ...stops];
  const [isPassengersUpdate, setIsPassengersUpdate] = useState<boolean>(false);
  const [isRecommendedUpdate, setIsRecommendedUpdate] = useState<boolean>(false);

  const fromRef = createRef<PlacesAutocomplete>();
  const toRef = createRef<PlacesAutocomplete>();

  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position: GeolocationPosition) => {
        dispatchRoutePlannerUpdate({
          type: 'SET_DEPARTURE',
          departureLocation: {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            placeId: '',
            label: '',
          },
        });
      });
    }
  }, [routePlannerState.context.resetKey]);

  useEffect(() => {
    if (isPassengersUpdate) {
      setIsPassengersUpdate(false);
    } else {
      props.onTripStateChange(routePlannerState.context);
    }
  }, [routePlannerState.context]);

  useEffect(() => {
    if (props.setMapDeparture) {
      dispatchRoutePlannerUpdate({
        type: 'SET_ARRIVAL',
        arrivalLocation: {
          label: props.setMapDeparture.label,
          latitude: props.setMapDeparture.latitude,
          longitude: props.setMapDeparture.longitude,
          placeId: props.setMapDeparture.placeId,
        },
      });
    }
  }, [props.setMapDeparture]);

  useEffect(() => {
    if (isRecommendedUpdate) {
      setIsRecommendedUpdate(false);
    }
  }, [routePlannerState.context.match?.recommended]);

  useEffect(() => {
    if (
      routePlannerState.context.arrivalLocation &&
      routePlannerState.context.departureLocation &&
      routePlannerState.context.arrivalLocation.placeId === routePlannerState.context.departureLocation.placeId
    ) {
      dispatchRoutePlannerUpdate({ type: 'SET_ARRIVAL', arrivalLocation: null });
    }
  }, [routePlannerState.context.arrivalLocation, routePlannerState.context.departureLocation]);

  useEffect(() => {
    // snap to the closest up on every reset
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position: GeolocationPosition) => {
        for (const hubOrStop of serviceHubsAndStops) {
          if (pointsWithinDistance(hubOrStop, position.coords, hubOrStop.snapRadius)) {
            return dispatchRoutePlannerUpdate({ type: 'SET_DEPARTURE', departureLocation: { ...hubOrStop, label: hubOrStop.name } });
          }
        }
      });
    }
  }, [routePlannerState.context.resetKey]);

  useEffect(() => {
    if (props.goBackPressedAt) {
      switch (routePlannerState.value) {
        case 'start_trip':
          props.setIsTripPlannerOpen(true);
          props.setIsTripStarted(false);
          break;
        case 'ready':
          props.setIsTripPlannerOpen(false);
          break;
      }

      props.onRecenterMap(undefined);
      dispatchRoutePlannerUpdate('BACK');
    }
  }, [props.goBackPressedAt]);

  const handleStartAgain = () => {
    dispatchRoutePlannerUpdate('START_AGAIN');
    props.onRecenterMap(undefined);
  };

  const handleChangePassengers = (passengers: number) => {
    setIsPassengersUpdate(true);
    dispatchRoutePlannerUpdate({ type: 'SET_PASSENGERS', passengers });
  };

  const handleViewTrip = (trip: FixedRouteMatch) => {
    dispatchRoutePlannerUpdate({ type: 'VIEW_TRIP', trip });
  };

  const handleStartTrip = () => {
    props.setIsTripPlannerOpen(false);
    props.setIsTripStarted(true);
    dispatchRoutePlannerUpdate('START_TRIP');
  };

  const handleSwapLocations = () => {
    dispatchRoutePlannerUpdate('SWAP_ADDRESSES');
  };

  const handleBack = () => {
    dispatchRoutePlannerUpdate('BACK');
  };

  useEffect(() => {
    handleFindMatch();
  }, [
    routePlannerState.context.departureDateTime,
    routePlannerState.context.departureLocation,
    routePlannerState.context.arrivalLocation,
    routePlannerState.context.passengers,
  ]);

  const handleFindMatch = () => {
    if (
      routePlannerState.context.departureLocation &&
      routePlannerState.context.arrivalLocation &&
      (routePlannerState.context.timeType === 'leaveNow' || routePlannerState.context.departureDateTime)
    ) {
      dispatchRoutePlannerUpdate('FIND_MATCH_START');

      hailerService.FixedRoute.Journies.calculateJourney({
        startAddress: {
          latitude: routePlannerState.context.departureLocation.latitude,
          longitude: routePlannerState.context.departureLocation.longitude,
        },
        endAddress: {
          latitude: routePlannerState.context.arrivalLocation.latitude,
          longitude: routePlannerState.context.arrivalLocation.longitude,
        },
        timeType: routePlannerState.context.timeType === 'leaveNow' ? 'leaveAt' : routePlannerState.context.timeType,
        time: dateRoundedDownToNearestMinute(
          routePlannerState.context.departureDateTime ?? dateRoundedUpToNearestMinutesInterval(new Date(), 5),
        ),
        passengers: Array(routePlannerState.context.passengers).fill({ accessibilityRequirement: false }),
        networkId: props.network.id,
      })
        .then((result: FixedRoute.CalculateJourneyResult) => {
          if (result.success && result.payload) {
            if (routePlannerState.context.departureLocation && routePlannerState.context.arrivalLocation) {
              const startAddress: Location = routePlannerState.context.departureLocation;
              const endAddress: Location = routePlannerState.context.arrivalLocation;

              const computeAddress = (
                journeyLeg: FixedRoute.BusJourneyLeg | FixedRoute.WalkingJourneyLeg,
                position: 'Start' | 'End',
              ): OnDemandJourneyLegAddress => {
                if (position === 'Start') {
                  if (journeyLeg.startAddress.label === 'Start') {
                    return {
                      label: startAddress.label,
                      addressId: { value: 0 },
                      placeId: startAddress.placeId,
                      lat: startAddress.latitude,
                      lng: startAddress.longitude,
                    };
                  } else {
                    return {
                      label: journeyLeg.startAddress.label,
                      addressId: { value: Number(journeyLeg.startAddress.addressId) },
                      placeId: journeyLeg.startAddress.placeId,
                      lat: journeyLeg.startAddress.latitude,
                      lng: journeyLeg.startAddress.longitude,
                    };
                  }
                } else {
                  if (journeyLeg.endAddress.label === 'End') {
                    return {
                      label: endAddress.label,
                      addressId: { value: 0 },
                      placeId: endAddress.placeId,
                      lat: endAddress.latitude,
                      lng: endAddress.longitude,
                    };
                  } else {
                    return {
                      label: journeyLeg.endAddress.label,
                      addressId: { value: Number(journeyLeg.endAddress.addressId) },
                      placeId: journeyLeg.endAddress.placeId,
                      lat: journeyLeg.endAddress.latitude,
                      lng: journeyLeg.endAddress.longitude,
                    };
                  }
                }
              };
              const recommended: FixedRouteMatch = {
                journeyData: {
                  journey: {
                    journeyLegs: result.payload.journeyLegs.map<FixedRouteMatchLeg>((journeyLeg) => ({
                      travelType: journeyLeg.travelType,
                      startTime: journeyLeg.startTime.toString(),
                      endTime: journeyLeg.endTime.toString(),
                      travelTime: journeyLeg.duration,
                      distance: journeyLeg.distance,
                      startAddress: computeAddress(journeyLeg, 'Start'),
                      endAddress: computeAddress(journeyLeg, 'End'),
                      availableCapacities: (journeyLeg as FixedRoute.BusJourneyLeg).availableCapacities,
                      vehicle: {
                        number: (journeyLeg as FixedRoute.BusJourneyLeg).vehicle?.rego,
                        displayName: (journeyLeg as FixedRoute.BusJourneyLeg).vehicle?.name,
                        image: (journeyLeg as FixedRoute.BusJourneyLeg).vehicle?.picture,
                        capacity: (journeyLeg as FixedRoute.BusJourneyLeg).vehicle?.capacities,
                        route: {
                          id: (journeyLeg as FixedRoute.BusJourneyLeg).vehicle?.route.id,
                          label: (journeyLeg as FixedRoute.BusJourneyLeg).vehicle?.route.label,
                          colour: (journeyLeg as FixedRoute.BusJourneyLeg).vehicle?.route.colour,
                        },
                      },
                    })),
                  },
                },
                startTime: result.payload.journeyLegs[0].startTime.toString(),
                endTime: result.payload.journeyLegs[result.payload.journeyLegs.length - 1].endTime.toString(),
                startAddress: {
                  label: startAddress.label,
                  lat: startAddress.latitude,
                  lng: startAddress.longitude,
                  placeId: startAddress.placeId,
                },
                endAddress: {
                  label: endAddress.label,
                  lat: endAddress.latitude,
                  lng: endAddress.longitude,
                  placeId: endAddress.placeId,
                },
              };

              setIsRecommendedUpdate(true);
              dispatchRoutePlannerUpdate({
                type: 'FIND_MATCH_SUCCESSFUL',
                match: { recommended, alternate: [] },
              });
            }
          } else {
            dispatchRoutePlannerUpdate({ type: 'FIND_MATCH_ERROR', error: result.error || t('booking:matchBookingError') });
          }
        })
        .catch((error) => {
          dispatchRoutePlannerUpdate({ type: 'FIND_MATCH_ERROR', error: error.message });
        });
    }
  };

  const handleSetBookingDateTime = (departureDateTime: Date, timeType: TimeType) => {
    dispatchRoutePlannerUpdate({ type: 'SET_DEPARTURE_TIME', departureDateTime: departureDateTime.toISOString(), timeType });
  };

  const handleDepartureChange = (address: AutoCompleteSetAddress | AutoCompleteExtraAddress) => {
    dispatchRoutePlannerUpdate({
      type: 'SET_DEPARTURE',
      departureLocation: address,
    });
  };

  const handleArrivalChange = (address: AutoCompleteSetAddress | AutoCompleteExtraAddress) => {
    dispatchRoutePlannerUpdate({
      type: 'SET_ARRIVAL',
      arrivalLocation: address,
    });
  };

  const clearAutocompleteSuggestions = (autoCompleteRef: React.RefObject<PlacesAutocomplete>) => {
    // @ts-ignore - clearSuggestions exists in the PlacesAutocomplete
    // https://github.com/hibiken/react-places-autocomplete/blob/dbb297486d21740dd1f15ec35fb3b45f7eebcb61/src/PlacesAutocomplete.js#L117
    if (autoCompleteRef.current && typeof autoCompleteRef.current.clearSuggestions === 'function') {
      // @ts-ignore - clearSuggestions exists
      autoCompleteRef.current.clearSuggestions();
    }
  };

  const renderSearchBubble = () => (
    <StyledLocationsIcons
      size="lg"
      colors={[FALLBACK_DARK_COLOUR, FALLBACK_DARK_COLOUR]}
      swapAction={handleSwapLocations}
      locations={[
        {
          id: 'departure',
          icon: '',
          content: (
            <DynamicInputWithLabel
              data-testid="input-current-location"
              onFocus={() => {
                clearAutocompleteSuggestions(toRef);
              }}
              displayBorder
              placeholder={t('common:currentLocation')}
              label={t('common:From')}
              value={routePlannerState.context.departureLocation?.label ?? ''}
              onChange={handleDepartureChange}
              extraAddresses={serviceHubsAndStops}
              forwardedRef={fromRef}
            />
          ),
        },
        {
          id: 'arrival',
          icon: locationSharp,
          content: (
            <DynamicInputWithLabel
              data-testid="input-where-to"
              onFocus={() => {
                clearAutocompleteSuggestions(fromRef);
              }}
              placeholder={t('trip:whereTo')}
              label={t('common:To')}
              value={routePlannerState.context.arrivalLocation?.label ?? ''}
              onChange={handleArrivalChange}
              extraAddresses={serviceHubsAndStops}
              forwardedRef={toRef}
            />
          ),
        },
      ]}
    />
  );

  const renderTimeAndPassengersSelector = () => (
    <StyledTimeAndPassengersSelectorContainer>
      <StyledDateTimeSelector
        defaultOpen={false}
        solutionDates={props.solutionDates}
        selectedDateTime={routePlannerState.context.departureDateTime ?? new Date().toISOString()}
        timeType={routePlannerState.context.timeType === 'leaveNow' ? 'leaveAt' : routePlannerState.context.timeType}
        onSubmit={handleSetBookingDateTime}
        label={
          routePlannerState.context.departureDateTime && routePlannerState.context.timeType !== 'leaveNow'
            ? format(new Date(routePlannerState.context.departureDateTime), `MMM do, ${TIME_FORMAT}`)
            : undefined
        }
      />

      <StyledRoutePlannerPassengerWrapper isLeaveNowOptionSelected={routePlannerState.context.timeType === 'leaveNow'}>
        <PassengerIncrementor value={routePlannerState.context.passengers} onChange={handleChangePassengers} min={1} max={7} step={1} />
      </StyledRoutePlannerPassengerWrapper>
    </StyledTimeAndPassengersSelectorContainer>
  );

  const renderViewTripState = () =>
    routePlannerState.matches('view_trip') ? (
      <SelectedRouteOverview
        trip={routePlannerState.context.trip}
        unitOfMeasurement={props.network.formatting.unitOfMeasurement}
        isExpandedInitially={!isMobile}
        onStart={handleStartTrip}
        onBack={handleBack}
      />
    ) : null;

  const renderStartTripState = () =>
    routePlannerState.matches('start_trip') ? (
      <StyledContainer className={props.className} alignFlexEnd={true}>
        <StyledSelectedRouteDirections
          route={routePlannerState.context.trip.journeyData.journey.journeyLegs}
          unitOfMeasurement={props.network.formatting.unitOfMeasurement}
          onComplete={handleStartAgain}
          onSwipe={(renderDirections: boolean, address: OnDemandJourneyLegAddress | undefined) => {
            props.renderMapDirections(renderDirections);
            props.onRecenterMap(address ? { latitude: address.lat, longitude: address.lng } : address);
          }}
        />
      </StyledContainer>
    ) : null;

  const renderResultsState = () => (
    <RoutePlannerMatches
      recommended={routePlannerState.context.match?.recommended ?? null}
      alternate={routePlannerState.context.match?.alternate ?? null}
      handleStartAgain={handleStartAgain}
      handleViewTrip={handleViewTrip}
      trip={routePlannerState.context.trip}
      unitOfMeasurement={props.network.formatting.unitOfMeasurement}
      routePlannerDepartureDateTime={routePlannerState.context.departureDateTime}
    />
  );

  const renderFindMatchState = () =>
    isMobile ? (
      <LoadingMask />
    ) : (
      <StyledBottomPart isFullScreen={true}>
        <LoadingMask />
      </StyledBottomPart>
    );

  const renderMobileBottomPart = () => (
    <StyledBottomPart isFullScreen={!!routePlannerState.context.match}>
      <StyledAutoCompleteSuggestionsRoot id="autocomplete-suggestions-root" isMobile={false} />
      {routePlannerState.matches('results') && renderResultsState()}
    </StyledBottomPart>
  );

  const renderDesktop = () => (
    <StyledContainer className={props.className} alignFlexEnd={!props.isTripPlannerOpen}>
      <StyledSearchBubble as={Content} overlay={props.hiddenByOverlay}>
        {routePlannerState.context.error ? <StyledErrorMessage message={routePlannerState.context.error} /> : null}
        {routePlannerState.matches('find_match') && renderFindMatchState()}
        {!routePlannerState.matches('find_match') && renderSearchBubble()}
        {!routePlannerState.matches('find_match') &&
        routePlannerState.context.departureLocation &&
        routePlannerState.context.arrivalLocation
          ? renderTimeAndPassengersSelector()
          : null}
      </StyledSearchBubble>

      <StyledRoutePlannerContentContainer>
        <StyledAutoCompleteSuggestionsRoot id="autocomplete-suggestions-root" isMobile={false} data-testid="autocomplete-desktop" />
      </StyledRoutePlannerContentContainer>

      {(routePlannerState.matches('view_trip') || routePlannerState.matches('results')) && (
        <StyledRouteMatchesWrapper as={Content}>
          {routePlannerState.matches('view_trip') && renderViewTripState()}
          {routePlannerState.matches('results') && renderResultsState()}
        </StyledRouteMatchesWrapper>
      )}
    </StyledContainer>
  );

  const renderMobile = () =>
    routePlannerState.matches('view_trip') ? (
      renderViewTripState()
    ) : (
      <StyledContainer className={props.className} alignFlexEnd={!props.isTripPlannerOpen}>
        <StyledSearchBubble as={StyledRoutePlannerFlat}>
          {routePlannerState.context.error ? <StyledErrorMessage message={routePlannerState.context.error} /> : null}
          {renderSearchBubble()}
        </StyledSearchBubble>
        {props.isTripPlannerOpen ? renderTimeAndPassengersSelector() : null}
        {routePlannerState.matches('find_match') && renderFindMatchState()}
        {renderMobileBottomPart()}
      </StyledContainer>
    );

  return routePlannerState.matches('start_trip') ? renderStartTripState() : isMobile ? renderMobile() : renderDesktop();
};

const StyledContainer = styled.div<{ alignFlexEnd: boolean }>`
  width: 100%;
  @media all and (max-width: ${BREAKPOINT_MD}px) {
    ${(props) =>
      props.alignFlexEnd &&
      css`
        display: flex;
        align-items: flex-end;
      `}
  }
`;

const StyledTimeAndPassengersSelectorContainer = styled.div`
  padding: 10px 0 30px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  z-index: ${ABOVE_MAP_Z_INDEX};
  max-width: 400px;
  width: 100%;

  @media all and (min-width: ${BREAKPOINT_MD}px) {
    padding: 0 25px 20px 25px;
  }
`;

const StyledLocationsIcons = styled(LocationsIcons)`
  background-color: white;
  padding: 15px;
  z-index: ${ABOVE_MAP_Z_INDEX};

  @media all and (min-width: ${BREAKPOINT_MD}px) {
    padding: 20px;
  }
`;

const StyledRouteMatchesWrapper = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 85%;
  z-index: ${ABOVE_MAP_Z_INDEX};
  position: relative;
  > ion-card {
    width: 100%;
    max-width: 400px;
    border-radius: 30px;
    margin: 10px;
    display: flex;
    flex-direction: column;
    overflow: auto;

    @media all and (min-width: ${BREAKPOINT_MD}px) {
      box-shadow: 0 2px 10px rgb(0 0 0 / 0.2);
    }
  }
`;

const StyledSearchBubble = styled.div<{ overlay?: boolean }>`
  display: flex;
  flex-direction: column;
  width: 100%;
  z-index: ${ABOVE_MAP_Z_INDEX};
  position: relative;

  > ion-card {
    width: 100%;
    max-width: 400px;
    border-radius: 30px;
    margin: 10px;
    display: flex;
    flex-direction: column;
    @media all and (min-width: ${BREAKPOINT_MD}px) {
      box-shadow: 0 2px 10px rgb(0 0 0 / 0.2);
    }

    ${({ overlay }) =>
      overlay
        ? css`
            &:after {
              content: '';
              position: absolute;
              background-color: ${MAP_DARKENING_OVERLAY_COLOUR};
              z-index: 2;
              width: 100%;
              height: 100%;
            }
          `
        : ''}
  }
`;

const StyledRoutePlannerFlat = styled.div`
  z-index: ${ABOVE_MAP_Z_INDEX};
  position: relative;
  margin: 0;
  overflow: hidden;
  width: 100%;
  max-width: 400px;
  border-radius: 30px;
  background-color: white;
  box-shadow: 0 3px 10px rgb(0 0 0 / 0.2);
`;

const StyledRoutePlannerContentContainer = styled.div`
  display: flex;
  position: relative;
  z-index: ${ABOVE_MAP_Z_INDEX};
  max-width: 400px;
  max-height: 60vh;
  flex: 1;
  margin: 0 10px;
  background-color: white;
  border-radius: 30px;
  box-shadow: 0 5px 10px rgb(0 0 0 / 0.2);
`;

const StyledAutoCompleteSuggestionsRoot = styled.div<{ isMobile: boolean }>`
  z-index: ${ABOVE_MAP_Z_INDEX};
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;

  ${({ isMobile }) =>
    !isMobile &&
    css`
      > div {
        ${scrollbarStyle}
      }
    `}
`;

const StyledErrorMessage = styled(ErrorMessageBlock)`
  padding: 20px 25px 0px 25px;
`;

const StyledDateTimeSelector = styled(DateTimeSelector)`
  margin-right: 5px;
  flex-grow: 1;
`;

const StyledRoutePlannerPassengerWrapper = styled(PopupWrapper)<{ isLeaveNowOptionSelected: boolean }>`
  flex: 0;
  position: relative;
  z-index: ${ABOVE_MAP_Z_INDEX};
  padding: 0 ${(props) => (props.isLeaveNowOptionSelected ? 15 : 10)}px !important;

  @media all and (max-width: ${BREAKPOINT_SM}px) {
    padding: 0 ${(props) => (props.isLeaveNowOptionSelected ? 10 : 5)}px !important;
  }
`;

const StyledBottomPart = styled.div<{ isFullScreen: boolean }>`
  background-color: white;
  flex: 1;
  margin: 0 -30px;
  position: relative;
  z-index: ${ABOVE_MAP_Z_INDEX};
  ${(props) =>
    props.isFullScreen &&
    css`
      height: 100%;
    `}
`;

const StyledSelectedRouteDirections = styled(SelectedRouteDirections)`
  width: 100%;
  max-width: 400px;
  border-radius: 30px;
  overflow: visible;
  margin: 10px 0 0;
  z-index: ${ABOVE_MAP_Z_INDEX};
  position: relative;
`;

export const scrollbarStyle = css`
  &::-webkit-scrollbar {
    width: 38px;
    height: 0;
    background-clip: padding-box;
    background-color: rgba(0, 0, 0, 0.1);
    border: 15px solid rgba(0, 0, 0, 0);
    border-radius: 18px;
  }
  &::-webkit-scrollbar-thumb {
    width: 8px;
    border: 15px solid rgba(0, 0, 0, 0);
    background-clip: padding-box;
    border-radius: 18px;
    background-color: rgba(0, 0, 0, 0.2);
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.025);
  }
`;

export default React.memo(RoutePlanner);
