import React from 'react';
import { Kiosk } from '@liftango/ops-client';
import styled from 'styled-components';
import ChevronButton from '../../../../components/elements/Button/ChevronButton';
import InputWithLabel from '../../../../components/components/InputWithLabel/InputWithLabel';
import { reloadOutline } from 'ionicons/icons';
import { useTranslation } from 'react-i18next';
import Button from '../../../../components/elements/Button/Button';
import MatchCard from '../../../../components/features/RideCard/MatchCard';
import Text from '../../../../components/elements/Text/Text';

type BookingSummaryProps = {
  match: Kiosk.RideMatch;
  firstName: string;
  lastName: string;
  passengerCount: number;
  handleBack: () => void;
  handleConfirm: (
    rideId: string,
    matchId: string,
    {
      firstName,
      lastName,
    }: {
      firstName: string;
      lastName: string;
    },
  ) => void;
  handleRestart: () => void;
  handleChangeFirstName: (value: string) => void;
  handleChangeLastName: (value: string) => void;
  isStateBooking: boolean;
  className?: string;
};

const BookingSummary = (props: BookingSummaryProps) => {
  const { t } = useTranslation<['booking']>(['booking']);

  const handleAcceptMatch = () => {
    props.handleConfirm(props.match.rideId, props.match.matchId, {
      firstName: props.firstName,
      lastName: props.lastName,
    });
  };

  return (
    <StyledBookingSummary className={props.className}>
      <ChevronButton onClick={props.handleBack} direction="left" />

      <StyledSummaryTitle align="center" color="secondary" weight={700} size={1.2}>
        {t('booking:Bookingsummary')}
      </StyledSummaryTitle>
      <StyledSummaryMatchCard
        passengerCount={props.passengerCount}
        ride={{
          departureTime: props.match.pickUpTime,
          arrivalTime: props.match.dropOffTime,
          departure: {
            label: props.match.pickUpAddress.label,
            hubName: props.match.pickUpAddress.hub?.name,
          },
          arrival: {
            label: props.match.arrivalAddress.label,
            hubName: props.match.arrivalAddress.hub?.name,
          },
        }}
      />

      <InputWithLabel
        label={t('booking:FirstName')}
        value={props.firstName}
        disabled={props.isStateBooking}
        onIonChange={(e) => props.handleChangeFirstName(e.detail.value!)}
      />

      <StyledSpacer />

      <InputWithLabel
        label={t('booking:LastName')}
        value={props.lastName}
        disabled={props.isStateBooking}
        onIonChange={(e) => props.handleChangeLastName(e.detail.value!)}
      />

      <StyledButtonWrapper>
        <MainButton block onClick={handleAcceptMatch} loading={props.isStateBooking} disabled={props.firstName.length < 2}>
          {t('booking:Booknow')}
        </MainButton>

        <StyledSecondButton block fill="outline" onClick={props.handleRestart} icon={reloadOutline} disabled={props.isStateBooking}>
          {t('booking:Startover')}
        </StyledSecondButton>
      </StyledButtonWrapper>
    </StyledBookingSummary>
  );
};

const StyledBookingSummary = styled.div``;

const StyledButtonWrapper = styled.div`
  padding-top: 20px;
`;

const MainButton = styled(Button)`
  ion-button {
    color: white;
  }
`;

const StyledSummaryTitle = styled(Text)`
  margin-top: 5px;
`;

const StyledSummaryMatchCard = styled(MatchCard)`
  margin: 15px;
`;

const StyledSecondButton = styled(Button)`
  ion-button {
    color: var(--ion-color-primary);
  }
`;

const StyledSpacer = styled.div`
  margin-top: 20px;
`;

export default BookingSummary;
