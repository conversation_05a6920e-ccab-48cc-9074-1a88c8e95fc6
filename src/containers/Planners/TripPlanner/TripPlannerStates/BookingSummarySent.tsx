import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import Text from '../../../../components/elements/Text/Text';
import Button from '../../../../components/elements/Button/Button';
import BookingCodeSvg from '../../../../assets/booking-code.svg?react';
import { selectTheme, Theme } from '../../../../store/reducers/themeReducer';
import { useAppSelector } from '../../../../hooks/storeHooks';

type BookingSummarySentProps = {
  handleStartAgain: () => void;
  className?: string;
};

const BookingSummarySent = (props: BookingSummarySentProps) => {
  const theme: Theme = useAppSelector(selectTheme);
  const { t } = useTranslation<['booking']>(['booking']);

  return (
    <StyledBookingSummarySent className={props.className}>
      <BookingCodeSvg height={120} color={`rgba(${theme.colors.primaryRgb}, 0.2)`} />

      <StyledBookingSummarySentTitle align="center" weight={700} size={1.2}>
        {t('booking:bookingSummarySent')}
      </StyledBookingSummarySentTitle>

      <StyledBookingSummarySentSubtitle align="center" weight={500} size={0.8}>
        {t('booking:bookingSummarySentSubtitle')}
      </StyledBookingSummarySentSubtitle>

      <StyledBookingSummarySentAction block fill="outline" onClick={props.handleStartAgain}>
        {t('booking:Finish')}
      </StyledBookingSummarySentAction>
    </StyledBookingSummarySent>
  );
};

const StyledBookingSummarySent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const StyledBookingSummarySentTitle = styled(Text)`
  margin: 20px 50px 0;
`;

const StyledBookingSummarySentSubtitle = styled(Text)`
  margin: 10px 0;
`;

const StyledBookingSummarySentAction = styled(Button)`
  margin-top: 10px;
`;

export default BookingSummarySent;
