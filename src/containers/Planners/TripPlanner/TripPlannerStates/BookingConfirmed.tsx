import React from 'react';
import { OnDemandRidePayload } from '@liftango/liftango-client';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import Text from '../../../../components/elements/Text/Text';
import Button from '../../../../components/elements/Button/Button';
import InputWithLabel from '../../../../components/components/InputWithLabel/InputWithLabel';
import RideCard from '../../../../components/features/RideCard/RideCard';
import BookingCode from '../../../../components/components/BookingCode/BookingCode';
import { useAppSelector } from '../../../../hooks/storeHooks';
import { selectNetworkEnforced } from '../../../../store/reducers/networkReducer';
import { selectApplicationMode } from '../../../../store/reducers/applicationReducer';
import TripBookedSvg from '../../../../assets/trip-booked.svg?react';
import { selectTheme, Theme } from '../../../../store/reducers/themeReducer';

type BookingConfirmedProps = {
  firstName: string;
  mobile: string;
  booking: OnDemandRidePayload;
  handleChangeMobile: (value: string) => void;
  handleStartAgain: () => void;
  handleSendSummary: (rideId: string) => void;
  isStateSendingSummary: boolean;
  className?: string;
};

const BookingConfirmed = (props: BookingConfirmedProps) => {
  const { t } = useTranslation<['booking']>(['booking']);
  const theme: Theme = useAppSelector(selectTheme);
  const network = useAppSelector(selectNetworkEnforced);
  const application = useAppSelector(selectApplicationMode);

  const handleSendBookingSummary = () => {
    props.handleSendSummary(props.booking.refid);
  };

  return (
    <StyledBookingConfirmed className={props.className}>
      <TripBookedSvg height={120} color={`rgba(${theme.colors.primaryRgb}, 0.2)`} />

      <StyledBookingConfirmedTitle align="center" weight={700} size={1.2}>
        {t('booking:bookingConfirmed', { name: props.firstName })}
      </StyledBookingConfirmedTitle>

      {application === 'kiosk' ? (
        <StyledBookingCodeWrapper>
          <Text align="center" weight={400} size={0.8}>
            {t('booking:bookingCodeInformation')}
          </Text>

          <StyledBookingCode code={props.booking.referenceId} />
        </StyledBookingCodeWrapper>
      ) : null}

      <StyledRideCard ride={props.booking} />

      <InputWithLabel
        label={t('booking:smsBookingDetails')}
        value={props.mobile}
        prefix={network.formatting.mobileCountryDialCode}
        disabled={props.isStateSendingSummary}
        onIonChange={(e) => props.handleChangeMobile(e.detail.value!)}
      />

      <StyledBookingConfirmedAction
        block
        onClick={handleSendBookingSummary}
        loading={props.isStateSendingSummary}
        disabled={props.mobile.length < 9}
      >
        {t('booking:Sendsummary')}
      </StyledBookingConfirmedAction>

      <StyledBookingConfirmedSecondaryAction block fill="outline" onClick={props.handleStartAgain}>
        {t('booking:Finish')}
      </StyledBookingConfirmedSecondaryAction>
    </StyledBookingConfirmed>
  );
};

const StyledBookingConfirmed = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const StyledBookingConfirmedTitle = styled(Text)`
  margin-top: 20px;
`;

const StyledBookingCodeWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-top: 10px;
`;

const StyledBookingCode = styled(BookingCode)`
  margin-top: 20px;
`;

const StyledRideCard = styled(RideCard)`
  margin: 20px 0;
`;

const StyledBookingConfirmedAction = styled(Button)`
  margin-top: 5px;

  ion-button {
    color: white;
  }
`;

const StyledBookingConfirmedSecondaryAction = styled(Button)`
  margin-top: 15px;
`;

export default BookingConfirmed;
