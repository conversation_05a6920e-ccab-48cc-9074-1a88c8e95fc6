import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import Text from '../../../../components/elements/Text/Text';
import Button from '../../../../components/elements/Button/Button';
import { reloadOutline } from 'ionicons/icons';
import VehicleUnavailableSvg from '../../../../assets/vehicle-unavailable.svg?react';
import { selectTheme, Theme } from '../../../../store/reducers/themeReducer';
import { useAppSelector } from '../../../../hooks/storeHooks';

type BookingFailedProps = {
  handleStartAgain: () => void;
  className?: string;
};

const BookingFailed = (props: BookingFailedProps) => {
  const theme: Theme = useAppSelector(selectTheme);
  const { t } = useTranslation<['booking']>(['booking']);

  return (
    <StyledBookingFailed className={props.className}>
      <VehicleUnavailableSvg height={120} color={`rgba(${theme.colors.primaryRgb}, 0.2)`} />

      <StyledBookingFailedTitle align="center" weight={700} size={1.2}>
        {t('booking:bookingFailed')}
      </StyledBookingFailedTitle>

      <StyledBookingFailedPrimaryAction block fill="outline" icon={reloadOutline} onClick={props.handleStartAgain}>
        {t('booking:Startover')}
      </StyledBookingFailedPrimaryAction>
    </StyledBookingFailed>
  );
};

const StyledBookingFailed = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const StyledBookingFailedTitle = styled(Text)`
  margin-top: 20px;
`;

const StyledBookingFailedPrimaryAction = styled(Button)`
  margin-top: 40px;
`;

export default BookingFailed;
