import React, { useEffect } from 'react';
import { OnDemandServiceHub, OnDemandSettingsSolutionDates } from '@liftango/liftango-client';
import { pointsWithinDistance } from '@liftango/polygon-point';
import { HailerOpsClient, Kiosk } from '@liftango/ops-client';
import styled from 'styled-components';
import { useMachine } from '@xstate/react';
import { locationSharp, navigateCircle, reloadOutline } from 'ionicons/icons';
import SelectWithDropdown from '../../../components/components/SelectWithDropdown/SelectWithDropdown';
import { DropdownOptions } from '../../../components/components/Dropdown/DropdownOption';
import DateTimeSelector from '../../../components/features/DateTimeSelector/DateTimeSelector';
import PassengerSelector from '../../../components/features/PassengerSelector/PassengerSelector';
import Button from '../../../components/elements/Button/Button';
import RideCard from '../../../components/features/RideCard/RideCard';
import LocationsIcons from '../../../components/components/LocationsIcons/LocationsIcons';
import Content from '../../../components/elements/Content/Content';
import { useTranslation } from 'react-i18next';
import { Companion, KIOSK_BOOKING_MACHINE_CONTEXT, KioskBookingMachine, TimeType } from '../../../machines/Kiosk/kioskBooking';
import BookingFailed from './TripPlannerStates/BookingFailed';
import BookingSummary from './TripPlannerStates/BookingSummary';
import BookingConfirmed from './TripPlannerStates/BookingConfirmed';
import BookingSummarySent from './TripPlannerStates/BookingSummarySent';
import { BREAKPOINT_MD } from '../../../constants/breakpoints';
import { Network } from '../../../store/reducers/networkReducer';
import { useHailerService } from '../../../services/HailerService';
import ErrorMessageBlock from '../../../components/components/MessageBlock/ErrorMessageBlock';
import LoadingMask from '../../../components/components/LoadingMask/LoadingMask';
import { format } from 'date-fns';
import { usePusher } from '@harelpls/use-pusher';
import { Channel } from 'pusher-js';
import { TIME_FORMAT } from '../../../constants/formatting';
import { pusherEvent } from '../../../helpers/pusher.helpers';

type DispatchRideAcceptedComplete = {
  networkId: string;
  userId: string;
  rideId: string;
};

type TripPlannerProps = {
  network: Network;
  serviceHubs: OnDemandServiceHub[];
  serviceLockTime: number;
  solutionDates: OnDemandSettingsSolutionDates[];
  onTripStateChange: (context: KIOSK_BOOKING_MACHINE_CONTEXT) => void;
  tripMachine: KioskBookingMachine;
  isFloating?: boolean;
  className?: string;
};

const TripPlanner = (props: TripPlannerProps) => {
  const hailerService = useHailerService();
  const { t } = useTranslation<['common', 'booking', 'trip']>(['common', 'booking', 'trip']);
  const pusher = usePusher();

  const [tripPlannerState, dispatchTripPlannerUpdate] = useMachine(props.tripMachine);

  useEffect(() => {
    props.onTripStateChange(tripPlannerState.context);
  }, [tripPlannerState.context]);

  useEffect(() => {
    if (
      tripPlannerState.context.arrivalLocation &&
      tripPlannerState.context.departureLocation &&
      tripPlannerState.context.arrivalLocation.placeId === tripPlannerState.context.departureLocation.placeId
    ) {
      dispatchTripPlannerUpdate({ type: 'SET_ARRIVAL', arrivalLocation: null });
    }
  }, [tripPlannerState.context.arrivalLocation, tripPlannerState.context.departureLocation]);

  useEffect(() => {
    // snap to the closest up on every reset
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position: GeolocationPosition) => {
        for (const serviceHub of props.serviceHubs) {
          if (pointsWithinDistance(serviceHub, position.coords, serviceHub.snapRadius)) {
            return dispatchTripPlannerUpdate({ type: 'SET_DEPARTURE', departureLocation: { ...serviceHub, label: serviceHub.name } });
          }
        }
      });
    }
  }, [tripPlannerState.context.resetKey]);

  const handleDepartureHubSelect = (id: string) => {
    const selectedHub: OnDemandServiceHub | undefined = props.serviceHubs.find(
      (hub: OnDemandServiceHub) => hub.placeId === (id as TimeType),
    );
    if (selectedHub) {
      dispatchTripPlannerUpdate({ type: 'SET_DEPARTURE', departureLocation: { ...selectedHub, label: selectedHub.name } });
    }
  };

  const handleArrivalHubSelect = (id: string) => {
    const selectedHub: OnDemandServiceHub | undefined = props.serviceHubs.find(
      (hub: OnDemandServiceHub) => hub.placeId === (id as TimeType),
    );
    if (selectedHub) {
      dispatchTripPlannerUpdate({ type: 'SET_ARRIVAL', arrivalLocation: { ...selectedHub, label: selectedHub.name } });
    }
  };

  const handleUpdateCompanions = (companions: Companion[]) => {
    dispatchTripPlannerUpdate({ type: 'SET_COMPANIONS', companions });
  };

  const handleBack = () => {
    dispatchTripPlannerUpdate('BACK');
  };

  const handleStartAgain = () => {
    dispatchTripPlannerUpdate('START_AGAIN');
  };

  const handleSelectTrip = () => {
    dispatchTripPlannerUpdate('SET_SUMMARY');
  };

  const handleUpdateFirstName = (firstName: string) => {
    dispatchTripPlannerUpdate({ type: 'SET_FIRSTNAME', firstName });
  };

  const handleUpdateLastName = (lastName: string) => {
    dispatchTripPlannerUpdate({ type: 'SET_LASTNAME', lastName });
  };

  const handleUpdateMobile = (mobile: string) => {
    dispatchTripPlannerUpdate({ type: 'SET_MOBILE', mobile });
  };

  const handleSwapLocations = () => {
    dispatchTripPlannerUpdate('SWAP_ADDRESSES');
  };

  const handleFindMatch = (rideId: string, returnRideId: string | null = null) => {
    dispatchTripPlannerUpdate('FIND_MATCH_START');

    return hailerService.Kiosk.Bookings.checkAvailability(rideId, returnRideId)
      .then((matchResponse) => {
        if (matchResponse.success) {
          if (
            matchResponse.payload &&
            matchResponse.payload.ride &&
            matchResponse.payload.ride.matches &&
            matchResponse.payload.ride.matches.length > 0
          ) {
            let matches: Kiosk.RideMatch[] = matchResponse.payload.ride.matches;

            return dispatchTripPlannerUpdate({
              type: 'FIND_MATCH_SUCCESSFUL',
              match: matches[0],
            });
          }
        }

        // @ts-ignore
        dispatchTripPlannerUpdate({ type: 'FIND_MATCH_ERROR', error: matchResponse.error || t('booking:matchBookingError') });
      })
      .catch((error) => {
        console.error(error);
        dispatchTripPlannerUpdate({ type: 'FIND_MATCH_ERROR', error: error.message });
      });
  };

  const handleCreateBooking = () => {
    if (
      tripPlannerState.context.departureLocation &&
      tripPlannerState.context.arrivalLocation &&
      (tripPlannerState.context.timeType === 'leaveNow' || tripPlannerState.context.departureDateTime)
    ) {
      dispatchTripPlannerUpdate('CREATE_BOOKING_START');

      hailerService.Kiosk.Bookings.createRide(
        props.network.id,
        tripPlannerState.context.timeType === 'leaveNow' ? 'leaveAt' : tripPlannerState.context.timeType,
        tripPlannerState.context.departureLocation,
        tripPlannerState.context.arrivalLocation,
        tripPlannerState.context.departureDateTime ?? new Date().toISOString(),
        tripPlannerState.context.companions,
        null,
      )
        .then((result) => {
          if (result.success && result.payload) {
            if (result.payload.rideId) {
              dispatchTripPlannerUpdate('CREATE_BOOKING_SUCCESSFUL');

              return handleFindMatch(result.payload.rideId, result.payload.returnRideId);
            }
          }

          // @ts-ignore
          dispatchTripPlannerUpdate({ type: 'CREATE_BOOKING_ERROR', error: result.error || t('booking:createBookingError') });
        })
        .catch((error) => {
          dispatchTripPlannerUpdate({ type: 'CREATE_BOOKING_ERROR', error: error.message });
        });
    }
  };

  const handleConfirmBooking = (
    rideId: string,
    matchId: string,
    userDetails: {
      firstName: string;
      lastName: string;
    },
  ) => {
    dispatchTripPlannerUpdate('CONFIRM_BOOKING_START');

    hailerService.Kiosk.Bookings.acceptMatch(rideId, matchId, userDetails)
      .then((result) => {
        if (result.success) {
          hailerService.Kiosk.Bookings.getBookingDetailed(rideId).then((ride) => {
            dispatchTripPlannerUpdate({ type: 'CONFIRM_BOOKING_SUCCESSFUL', booking: ride.entry });
          });
        } else {
          // @ts-ignore
          dispatchTripPlannerUpdate({ type: 'CONFIRM_BOOKING_ERROR', error: result.error || t('booking:confirmBookingError') });
        }
      })
      .catch((error) => {
        // @ts-ignore
        dispatchTripPlannerUpdate({ type: 'CONFIRM_BOOKING_ERROR', error: error.message });
      });
  };

  const handleSendSummary = (rideId: string) => {
    dispatchTripPlannerUpdate('SUMMARY_SEND_START');

    hailerService.Kiosk.Bookings.sendSummary(rideId, tripPlannerState.context.details.mobile)
      .then((result) => {
        if (result.success) {
          dispatchTripPlannerUpdate('SUMMARY_SEND_SUCCESSFUL');
        } else {
          dispatchTripPlannerUpdate({ type: 'SUMMARY_SEND_ERROR', error: result.error || t('booking:sendBookingSummaryError') });
        }
      })
      .catch((error) => {
        dispatchTripPlannerUpdate({ type: 'SUMMARY_SEND_ERROR', error: error.message });
      });
  };

  const handleSetBookingDateTime = (departureDateTime: Date, timeType: TimeType) => {
    dispatchTripPlannerUpdate({ type: 'SET_DEPARTURE_TIME', departureDateTime: departureDateTime.toISOString(), timeType });
  };

  useEffect(() => {
    let rideChannel: Channel | undefined = undefined;
    if (tripPlannerState.context.booking?.refid) {
      rideChannel = pusher.client?.subscribe(tripPlannerState.context.booking?.refid);
    }
    if (rideChannel) {
      rideChannel.bind(pusherEvent.DispatchRideAcceptedComplete, (data: DispatchRideAcceptedComplete | undefined) => {
        if (!!data?.rideId && tripPlannerState.context.booking?.refid && data.rideId === tripPlannerState.context.booking.refid) {
          hailerService.Kiosk.Bookings.getBookingDetailed(data.rideId).then((ride) => {
            dispatchTripPlannerUpdate({ type: 'UPDATE_BOOKING', booking: ride.entry });
          });
        }
      });
    }

    return () => {
      if (rideChannel) {
        rideChannel.unbind();
      }
    };
  }, [tripPlannerState.context.booking?.refid]);

  return (
    <StyledTripPlanner className={props.className} as={props.isFloating ? Content : undefined}>
      {tripPlannerState.context.error ? <StyledErrorMessage message={tripPlannerState.context.error} /> : null}

      {tripPlannerState.matches({ match: 'creating' }) || tripPlannerState.matches({ match: 'finding' }) ? (
        <LoadingMask />
      ) : tripPlannerState.matches('summary') ? (
        <StyledTripPlannerContent>
          <BookingSummary
            match={tripPlannerState.context.match}
            firstName={tripPlannerState.context.details.firstName}
            lastName={tripPlannerState.context.details.lastName}
            passengerCount={tripPlannerState.context.companions.length}
            handleChangeFirstName={handleUpdateFirstName}
            handleChangeLastName={handleUpdateLastName}
            handleBack={handleBack}
            handleConfirm={handleConfirmBooking}
            handleRestart={handleStartAgain}
            isStateBooking={tripPlannerState.matches({ summary: 'booking' })}
          />
        </StyledTripPlannerContent>
      ) : tripPlannerState.matches('no_booking') ? (
        <StyledTripPlannerContent>
          <BookingFailed handleStartAgain={handleStartAgain} />
        </StyledTripPlannerContent>
      ) : tripPlannerState.matches('confirmed') ? (
        <StyledTripPlannerContent>
          <BookingConfirmed
            firstName={tripPlannerState.context.details.firstName}
            mobile={tripPlannerState.context.details.mobile}
            booking={tripPlannerState.context.booking}
            handleChangeMobile={handleUpdateMobile}
            handleStartAgain={handleStartAgain}
            handleSendSummary={handleSendSummary}
            isStateSendingSummary={tripPlannerState.matches({ confirmed: 'sending' })}
          />
        </StyledTripPlannerContent>
      ) : tripPlannerState.matches('summary_sent') ? (
        <StyledTripPlannerContent>
          <BookingSummarySent handleStartAgain={handleStartAgain} />
        </StyledTripPlannerContent>
      ) : (
        <>
          <StyledLocationsIcons
            size="lg"
            colors={['white', 'black']}
            swapAction={handleSwapLocations}
            locations={[
              {
                id: 'departure',
                icon: navigateCircle,
                content: (
                  <SelectWithDropdown
                    labelSize={1.4}
                    defaultLabel={t('trip:pickUpLocation')}
                    onSelect={handleDepartureHubSelect}
                    selectedOption={tripPlannerState.context.departureLocation?.placeId ?? null}
                    options={props.serviceHubs.map<DropdownOptions<string, string>>((hub: OnDemandServiceHub) => ({
                      id: hub.placeId,
                      title: hub.name,
                      label: hub.label,
                      icon: hub.icon,
                    }))}
                  />
                ),
              },
              {
                id: 'arrival',
                icon: locationSharp,
                content: (
                  <SelectWithDropdown
                    labelSize={1.4}
                    defaultLabel={t('trip:whereTo')}
                    onSelect={handleArrivalHubSelect}
                    selectedOption={tripPlannerState.context.arrivalLocation?.placeId ?? null}
                    options={props.serviceHubs.reduce<DropdownOptions<string, string>[]>(
                      (acc: DropdownOptions<string, string>[], hub: OnDemandServiceHub) => {
                        if (tripPlannerState.context.departureLocation?.placeId !== hub.placeId) {
                          acc.push({
                            id: hub.placeId,
                            title: hub.name,
                            label: hub.label,
                            icon: hub.icon,
                          });
                        }

                        return acc;
                      },
                      [],
                    )}
                  />
                ),
              },
            ]}
          />

          {tripPlannerState.context.departureLocation && tripPlannerState.context.arrivalLocation ? (
            <>
              <StyledTripPopoverContent>
                <StyledDateTimeSelector
                  defaultOpen={tripPlannerState.matches('ready')}
                  solutionDates={props.solutionDates}
                  selectedDateTime={tripPlannerState.context.departureDateTime ?? new Date().toISOString()}
                  timeType={tripPlannerState.context.timeType === 'leaveNow' ? 'leaveAt' : tripPlannerState.context.timeType}
                  onSubmit={handleSetBookingDateTime}
                  label={
                    tripPlannerState.context.departureDateTime && tripPlannerState.context.timeType !== 'leaveNow'
                      ? format(new Date(tripPlannerState.context.departureDateTime), `MMM do, ${TIME_FORMAT}`)
                      : undefined
                  }
                  showInModal
                />
                <PassengerSelector companions={tripPlannerState.context.companions} onSubmit={handleUpdateCompanions} />
              </StyledTripPopoverContent>

              {tripPlannerState.context.booking ? (
                <StyledRideCard ride={tripPlannerState.context.booking} lockedInTime={props.serviceLockTime} />
              ) : null}

              <StyledTripPlannerContent>
                {tripPlannerState.context.booking ? (
                  <>
                    <Button block onClick={handleSelectTrip}>
                      {t('booking:Selecttrip')}
                    </Button>
                    <Button block fill="outline" icon={reloadOutline} onClick={handleStartAgain}>
                      {t('booking:Startover')}
                    </Button>
                  </>
                ) : (
                  <>
                    <Button block onClick={handleCreateBooking} disabled={!tripPlannerState.context.departureDateTime}>
                      {t('booking:Search')}
                    </Button>
                    <Button block fill="outline" icon={reloadOutline} onClick={handleStartAgain}>
                      {t('booking:Startover')}
                    </Button>
                  </>
                )}
              </StyledTripPlannerContent>
            </>
          ) : null}
        </>
      )}
    </StyledTripPlanner>
  );
};

const StyledTripPlannerContent = styled.div`
  padding: 20px;
  border-radius: 30px;
  background-color: white;
`;

const StyledTripPopoverContent = styled.div`
  padding: 20px;
  display: flex;
  flex-direction: row;
`;

const StyledRideCard = styled(RideCard)`
  padding: 15px 30px;
  background-color: rgb(var(--color-lime-green-rgb), 0.2);
`;

const StyledLocationsIcons = styled(LocationsIcons)`
  background-color: var(--color-lime-green);
  padding: 30px;

  @media all and (min-width: ${BREAKPOINT_MD}px) {
    padding: 20px;
  }

  .swap-icon {
    color: white;
  }
`;

const StyledTripPlanner = styled.div`
  > ion-card {
    width: 400px;
    border-radius: 30px;
    overflow: visible;
    margin: 10px 0 0;
  }
`;

const StyledErrorMessage = styled(ErrorMessageBlock)`
  padding: 20px;
  margin-bottom: 10px;
`;

const StyledDateTimeSelector = styled(DateTimeSelector)`
  margin-right: 20px;
`;

export default React.memo(TripPlanner);
