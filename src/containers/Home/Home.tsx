import React, { useEffect, useMemo, useRef, useState } from 'react';
import styled, { css } from 'styled-components';
import { addOutline, removeOutline } from 'ionicons/icons';
import { useWindowWidth } from '@react-hook/window-size';
import { IonPage } from '@ionic/react';
import { futureSolutionDates, getFlattenedServiceSettings, ServiceSettingPayload } from '@liftango/service-toolkit';
import {
  FixedRouteServiceSettingsPayload,
  FixedRouteSettingsSolutionDates,
  OnDemandServiceHub,
  OnDemandServiceSettingsPayload,
  OnDemandSettingsSolutionDates,
} from '@liftango/liftango-client';
import { FixedRoute } from '@liftango/ops-client';
import MapView from '../../containers/MapView/MapView';
import Toast, { ToastBanner } from '../../components/elements/Toastbar/Toastbar';
import { Network } from '../../store/reducers/networkReducer';
import TripPlanner from '../Planners/TripPlanner/TripPlanner';
import { BREAKPOINT_MD } from '../../constants/breakpoints';
import { useAppSelector } from '../../hooks/storeHooks';
import useNetworkFeatures from '../../hooks/useNetworkFeatures';
import { selectServices } from '../../store/reducers/serviceReducer';
import { KIOSK_BOOKING_MACHINE_CONTEXT } from '../../machines/Kiosk/kioskBooking';
import { JourneyPoint, JourneyPointList, JourneyPointType, MapPadding } from '../../components/features/Map/Map';
import {
  LARGE_MAP_PADDING,
  LARGE_MAP_PADDING_OPEN_PLANNER,
  SMALL_MAP_ITEM_DETAILS_SHOWING_PADDING,
  SMALL_MAP_TRIP_DETAILS_PADDING,
  SMALL_MAP_PADDING,
  SMALL_MAP_PLANNER_SHOWN_BUT_CLOSED_PADDING,
} from '../../constants/map';
import { ApplicationMode, selectApplicationMode } from '../../store/reducers/applicationReducer';
import RoutePlanner from '../Planners/RoutePlanner/RoutePlanner';
import { FIXED_ROUTE_MACHINE_CONTEXT, FixedRouteMatch } from '../../machines/FixedRoute/routePlanner';
import { selectRouteShapes, selectStops, setRoutes } from '../../store/reducers/routesReducer';
import FloatingIcon from '../../components/elements/Icon/FloatingIcon';
import useQueryParam from '../../hooks/useQueryParam';
import { ABOVE_MAP_Z_INDEX, MAP_Z_INDEX } from '../../constants/positioning';
import RouteFilteringMenu from '../../components/features/RouteFiltering/RouteFilteringMenu';
import RouteFiltering from '../../components/features/RouteFiltering/RouteFiltering';
import { useMapContext } from '../../context/MapProvider';
import { getPointHighlightEffect } from '../../components/features/get-point-highlight-effect';
import { selectTheme, Theme } from '../../store/reducers/themeReducer';
import ProductTour from '../../components/features/Map/ProductTour/ProductTour';
import { ProductTourPage } from '../../components/features/Map/mapReducer';
import Alerts from '../../components/features/Alert/Alert';
import tripMachine from '../../machines/Kiosk/kioskBooking';
import routePlannerMachine from '../../machines/FixedRoute/routePlanner';
import { generateMockRouteData } from '../../utils/mockMapData';
import { useAppDispatch } from '../../hooks/storeHooks';

const mapContainerStyle: React.CSSProperties = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: -1,
};

const DEFAULT_ICON_MARGIN_TOP = 26;

type HomeProps = {
  network: Network;
  banner?: ToastBanner;
};

const isKioskBookingMachine = (
  machine: KIOSK_BOOKING_MACHINE_CONTEXT | FIXED_ROUTE_MACHINE_CONTEXT,
): machine is KIOSK_BOOKING_MACHINE_CONTEXT => {
  return (machine as KIOSK_BOOKING_MACHINE_CONTEXT).booking !== undefined;
};

const isFixedRouteBookingMachine = (
  machine: KIOSK_BOOKING_MACHINE_CONTEXT | FIXED_ROUTE_MACHINE_CONTEXT,
): machine is FIXED_ROUTE_MACHINE_CONTEXT => {
  return (machine as FIXED_ROUTE_MACHINE_CONTEXT).trip !== undefined;
};

const matchLocationToHub = (location: { placeId: string }, hubs: OnDemandServiceHub[]): OnDemandServiceHub | undefined => {
  return hubs.find((hub: OnDemandServiceHub) => hub.placeId === location.placeId);
};

const Home: React.FC<HomeProps> = (props: HomeProps) => {
  const { mapState } = useMapContext();
  const productTourPage: ProductTourPage = mapState.productTourPage;
  const productTourOpen: boolean = !!productTourPage;
  const theme: Theme = useAppSelector(selectTheme);
  const dispatch = useAppDispatch();

  const windowWidth: number = useWindowWidth();
  const isMobile: boolean = windowWidth < BREAKPOINT_MD;

  const application: ApplicationMode = useAppSelector(selectApplicationMode);
  const serviceSettings: (OnDemandServiceSettingsPayload | FixedRouteServiceSettingsPayload)[] = useAppSelector(selectServices);
  const { networkFeatures } = useNetworkFeatures();
  const stops: FixedRoute.ServiceRouteStop[] = useAppSelector(selectStops);
  const routeShapes: FixedRoute.ServiceRouteShape[] = useAppSelector(selectRouteShapes);

  // Development mode: Add simple mock data for testing pinch-to-zoom
  useEffect(() => {
    if (import.meta.env.DEV && routeShapes.length === 0) {
      console.log('🚌 Loading simple mock data...');
      const mockRoutes = generateMockRouteData();
      dispatch(setRoutes(mockRoutes));
      console.log(`✅ Added ${mockRoutes.length} routes with ${mockRoutes.reduce((total, route) => total + route.stops.length, 0)} stops`);
    }
  }, [dispatch, routeShapes.length]);

  const [bannerIsOpen, setBannerIsOpen] = useState<boolean>(!isMobile);
  const [isTripPlannerOpen, setIsTripPlannerOpen] = useState<boolean>(false);
  const [isTripPlannerShown, setIsTripPlannerShown] = useState<boolean>(true);
  const [isTripStarted, setIsTripStarted] = useState<boolean>(false);
  const [tripPlannerState, setTripPlannerState] = useState<KIOSK_BOOKING_MACHINE_CONTEXT | FIXED_ROUTE_MACHINE_CONTEXT | null>(null);
  const [mapCenter, setMapCenter] = useState<
    | {
        latitude: number;
        longitude: number;
      }
    | undefined
  >(undefined);
  const [renderMapDirections, setRenderMapDirections] = useState<boolean>(true);
  const [selectedMapStop, setSelectedMapStop] = useState<FixedRoute.ServiceRouteStop | null>(null);

  const routeFilterRef: React.RefObject<HTMLDivElement> = useRef<HTMLDivElement>(null);
  const firstStopRef: React.RefObject<HTMLButtonElement> = useRef<HTMLButtonElement>(null);
  const routePlannerRef: React.RefObject<HTMLDivElement> = useRef<HTMLDivElement>(null);

  const [backButtonPressedAt, setBackButtonPressedAt] = useState<number>(0);
  const [mapItemSelected, setMapItemSelected] = useState<boolean>(false);
  const [isRouteFilterOpen, setIsRouteFilterOpen] = useState<boolean>(false);

  useEffect(() => {
    setIsTripPlannerOpen(false);
    setIsTripStarted(false);
    setIsTripPlannerShown(true);
  }, [tripPlannerState?.resetKey]);

  useEffect(() => {
    setIsTripPlannerOpen(true);
  }, [selectedMapStop]);

  const topQueryParam: string = useQueryParam('top');
  const marginTop: number = topQueryParam ? Number(topQueryParam) : DEFAULT_ICON_MARGIN_TOP;

  const toggleBanner = () => setBannerIsOpen((prevState: boolean): boolean => !prevState);

  const serviceSetting: ServiceSettingPayload | null = useMemo<ServiceSettingPayload | null>(() => {
    if (!tripPlannerState || !isKioskBookingMachine(tripPlannerState)) {
      return null;
    }

    return getFlattenedServiceSettings(
      serviceSettings,
      tripPlannerState?.departureDateTime ? new Date(tripPlannerState.departureDateTime) : new Date(),
      tripPlannerState?.departureLocation
        ? { latitude: tripPlannerState.departureLocation.latitude, longitude: tripPlannerState.departureLocation.longitude }
        : null,
      tripPlannerState?.arrivalLocation
        ? { latitude: tripPlannerState.arrivalLocation.latitude, longitude: tripPlannerState.arrivalLocation.longitude }
        : null,
    );
  }, [
    serviceSettings,
    tripPlannerState?.departureDateTime,
    tripPlannerState?.departureLocation?.latitude,
    tripPlannerState?.departureLocation?.longitude,
    tripPlannerState?.arrivalLocation?.latitude,
    tripPlannerState?.arrivalLocation?.longitude,
  ]);

  const serviceHubs: OnDemandServiceHub[] = (serviceSetting as OnDemandServiceSettingsPayload)?.hubs || [];
  const solutions: (OnDemandSettingsSolutionDates | FixedRouteSettingsSolutionDates)[] = useMemo<
    (OnDemandSettingsSolutionDates | FixedRouteSettingsSolutionDates)[]
  >(() => futureSolutionDates(serviceSettings), [serviceSettings]);

  const journeyPoints: JourneyPointList[] = useMemo<JourneyPointList[]>((): JourneyPointList[] => {
    if (tripPlannerState) {
      if (isKioskBookingMachine(tripPlannerState) && tripPlannerState.booking) {
        const departureHub = matchLocationToHub(tripPlannerState.booking.departure, serviceHubs);
        const arrivalHub = matchLocationToHub(tripPlannerState.booking.arrival, serviceHubs);

        return [
          [
            {
              type: 'departure',
              icon: departureHub?.icon || 'default',
              latitude: tripPlannerState.booking.departure.latitude,
              longitude: tripPlannerState.booking.departure.longitude,
              placeId: tripPlannerState.booking.departure.placeId,
            },
            {
              type: 'arrival',
              icon: arrivalHub?.icon || 'default',
              latitude: tripPlannerState.booking.arrival.latitude,
              longitude: tripPlannerState.booking.arrival.longitude,
              placeId: tripPlannerState.booking.arrival.placeId,
            },
          ],
        ] as JourneyPointList[];
      } else if (isFixedRouteBookingMachine(tripPlannerState) && (tripPlannerState.trip || tripPlannerState.match?.recommended)) {
        // The points will be segmented so that we don't calculate the directions between bus departure and bus arrival
        const segmentedPointLists: JourneyPoint<JourneyPointType>[][] = [];
        // @ts-ignore TS is complaining that `trip` can be FixedRouteMatch | undefined but we wouldn't enter this block if either tripPlannerState.trip or tripPlannerState.match?.recommended isn't defined
        let trip: FixedRouteMatch = tripPlannerState.trip || tripPlannerState.match?.recommended;

        if (tripPlannerState.departureLocation && tripPlannerState.arrivalLocation) {
          for (const journeyLeg of trip.journeyData.journey.journeyLegs) {
            const departure: JourneyPoint<JourneyPointType> = {
              type: journeyLeg.travelType === 'foot' ? 'foot-departure' : 'bus-departure',
              icon: '',
              latitude: journeyLeg.startAddress.lat,
              longitude: journeyLeg.startAddress.lng,
              placeId: journeyLeg.startAddress.placeId,
              colour: journeyLeg.travelType === 'bus' ? journeyLeg.vehicle.route.colour : undefined,
              routeId: journeyLeg.travelType === 'bus' ? journeyLeg.vehicle.route.id : undefined,
            };

            const arrival: JourneyPoint<JourneyPointType> = {
              type: journeyLeg.travelType === 'foot' ? 'foot-arrival' : 'bus-arrival',
              icon: '',
              latitude: journeyLeg.endAddress.lat,
              longitude: journeyLeg.endAddress.lng,
              placeId: journeyLeg.endAddress.placeId,
              colour: journeyLeg.travelType === 'bus' ? journeyLeg.vehicle.route.colour : undefined,
            };

            segmentedPointLists.push([departure, arrival]);
          }
        }

        return segmentedPointLists as JourneyPointList[];
      } else if (tripPlannerState.arrivalLocation && tripPlannerState.departureLocation) {
        const departureHub = matchLocationToHub(tripPlannerState.departureLocation, serviceHubs);
        const arrivalHub = matchLocationToHub(tripPlannerState.arrivalLocation, serviceHubs);

        return [
          [
            {
              type: 'departure',
              icon: departureHub?.icon || 'default',
              latitude: tripPlannerState.departureLocation.latitude,
              longitude: tripPlannerState.departureLocation.longitude,
              placeId: tripPlannerState.departureLocation.placeId,
            },
            {
              type: 'arrival',
              icon: arrivalHub?.icon || 'default',
              latitude: tripPlannerState.arrivalLocation.latitude,
              longitude: tripPlannerState.arrivalLocation.longitude,
              placeId: tripPlannerState.arrivalLocation.placeId,
            },
          ],
        ] as JourneyPointList[];
      }
    }

    return [];
    // @ts-ignore - the booking key is not always there
  }, [tripPlannerState?.arrivalLocation, tripPlannerState?.departureLocation, tripPlannerState?.booking, serviceHubs]);

  const getMapPadding = (): MapPadding => {
    if (!isMobile) {
      if (tripPlannerState?.departureLocation && tripPlannerState?.arrivalLocation) {
        return LARGE_MAP_PADDING_OPEN_PLANNER;
      }

      return LARGE_MAP_PADDING;
    }

    let mapPadding: MapPadding = SMALL_MAP_PADDING;

    if (application !== 'fixed_route' || isTripStarted) {
      return mapPadding;
    }

    if (isTripPlannerShown && !isTripPlannerOpen) {
      mapPadding = SMALL_MAP_PLANNER_SHOWN_BUT_CLOSED_PADDING;
    }

    if (mapItemSelected) {
      mapPadding = SMALL_MAP_ITEM_DETAILS_SHOWING_PADDING;
    }

    if (tripPlannerState && 'trip' in tripPlannerState && tripPlannerState.trip) {
      mapPadding = SMALL_MAP_TRIP_DETAILS_PADDING;
    }

    return mapPadding;
  };

  const renderTopLeftIcon = (): JSX.Element | null => {
    if (application !== 'fixed_route') {
      return null;
    }

    const routeFilteringMenu: JSX.Element = (
      // Wrapper is to have a DOM element to bind the ref to, for product tour position info
      <StyledRouteFilteringMenuWrapper
        marginTop={marginTop}
        highlightColour={productTourPage === 'route-filter' ? theme.colors.accent : undefined}
        ref={routeFilterRef}
      >
        <RouteFilteringMenu
          routeShapes={routeShapes}
          disabled={(productTourOpen && productTourPage !== 'route-filter') || (isMobile && !isTripPlannerOpen && mapItemSelected)}
          showRouteFilter={() => setIsRouteFilterOpen(true)}
        />
      </StyledRouteFilteringMenuWrapper>
    );

    if (isMobile) {
      if (!isTripPlannerOpen && !isTripStarted) {
        return routeFilteringMenu;
      }
      return <StyledFloatingIcon icon="back" size={36} action={() => setBackButtonPressedAt(new Date().getTime())} marginTop={marginTop} />;
    } else {
      // show Info button if route planning has not started
      if (!isTripStarted && tripPlannerState?.arrivalLocation === null) {
        return routeFilteringMenu;
      }
    }

    return null;
  };

  return (
    <>
      {productTourOpen && <ProductTour routeFilterRef={routeFilterRef} firstStopRef={firstStopRef} routePlannerRef={routePlannerRef} />}
      <IonPage>
        {networkFeatures['urgent_alerts'] ? <Alerts /> : null}
        <StyledHomeContent>
          {props.banner && !isMobile ? (
            <StyledToast
              isOpen={bannerIsOpen}
              title={props.banner.title}
              message={props.banner.message}
              messageCollapsed={props.banner.messageCollapsed}
              image={props.banner.image}
              ctaIcon={bannerIsOpen ? removeOutline : addOutline}
              ctaCallback={toggleBanner}
            />
          ) : null}

          {renderTopLeftIcon()}

          {isTripPlannerShown && !isRouteFilterOpen && (
            <StyledTripPlannerWeb
              isTripPlannerOpen={isTripPlannerOpen}
              isFixedRoute={application === 'fixed_route'}
              isViewTripState={!!tripPlannerState && isFixedRouteBookingMachine(tripPlannerState) && !!tripPlannerState.trip}
              isTripStarted={isTripStarted}
              onClick={() => {
                if (!isTripPlannerOpen && !isTripStarted) {
                  setIsTripPlannerOpen(true);
                }
              }}
              topOffset={marginTop}
              ref={routePlannerRef}
            >
              {application === 'fixed_route' ? (
                <RoutePlanner
                  network={props.network}
                  serviceHubs={serviceHubs}
                  stops={stops}
                  serviceLockTime={(serviceSetting as OnDemandServiceSettingsPayload)?.rideLockTimeOffset ?? 0}
                  isFloating={!isMobile}
                  isTripPlannerOpen={isTripPlannerOpen}
                  onTripStateChange={setTripPlannerState}
                  goBackPressedAt={backButtonPressedAt}
                  setIsTripPlannerOpen={setIsTripPlannerOpen}
                  setIsTripStarted={setIsTripStarted}
                  setMapDeparture={selectedMapStop}
                  solutionDates={solutions}
                  tripMachine={routePlannerMachine}
                  onRecenterMap={setMapCenter}
                  renderMapDirections={setRenderMapDirections}
                  hiddenByOverlay={productTourOpen && productTourPage !== 'plan-journey'}
                />
              ) : (
                <TripPlanner
                  network={props.network}
                  serviceHubs={serviceHubs}
                  serviceLockTime={(serviceSetting as OnDemandServiceSettingsPayload)?.rideLockTimeOffset ?? 0}
                  isFloating={!isMobile}
                  onTripStateChange={setTripPlannerState}
                  solutionDates={solutions}
                  tripMachine={tripMachine}
                />
              )}
            </StyledTripPlannerWeb>
          )}

          {(!isMobile || !tripPlannerState?.departureLocation || !tripPlannerState?.arrivalLocation || application === 'fixed_route') && (
            <StyledHomeMapWrapper>
              <MapView
                onMapItemSelected={setMapItemSelected}
                showUserPosition
                initialCenter={props.network.address}
                center={mapCenter}
                regionCode={props.network.formatting.mobileCountryCode}
                serviceHubs={serviceHubs}
                stops={stops}
                routeShapes={routeShapes}
                style={mapContainerStyle}
                journeyPoints={journeyPoints}
                hideTripPlanner={() => setIsTripPlannerShown(false)}
                showTripPlanner={() => setIsTripPlannerShown(true)}
                isTripPlanerOpen={isTripPlannerOpen}
                onMapStopPressed={(stop: FixedRoute.ServiceRouteStop) => {
                  setSelectedMapStop(stop);
                }}
                mapPadding={getMapPadding()}
                networkId={props.network.id}
                renderDirections={renderMapDirections}
                firstStopRef={firstStopRef}
              />
            </StyledHomeMapWrapper>
          )}

          {isRouteFilterOpen && <StyledRouteFiltering hideRouteFilter={() => setIsRouteFilterOpen(false)} />}
        </StyledHomeContent>
      </IonPage>
    </>
  );
};

const StyledToast = styled(Toast)<{ isOpen: boolean }>`
  top: 70px;
  right: 30px;
  max-width: ${(props) => (props.isOpen ? 600 : 350)}px;
  height: 120px;
  transition: max-width 0.2s ease;

  @media all and (max-width: 1100px) {
    max-width: ${(props) => (props.isOpen ? 550 : 350)}px;
    height: 130px;
  }

  @media all and (max-width: 900px) {
    max-width: ${(props) => (props.isOpen ? 400 : 350)}px;
    height: 210px;
  }

  @media all and (max-width: 850px) {
    max-width: 350px;
    height: 290px;
  }

  @media all and (max-width: ${BREAKPOINT_MD}px) {
    max-width: 350px;
    height: 290px;
  }
`;

const StyledHomeContent = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--ion-border-color);
`;

const StyledTripPlannerWeb = styled.div<{
  isTripPlannerOpen: boolean;
  isFixedRoute: boolean;
  isViewTripState: boolean;
  isTripStarted: boolean;
  topOffset: number;
}>`
  flex: 1;
  margin: 30px;
  border-radius: 30px;
  display: flex;
  position: relative;

  // desktop style
  @media all and (min-width: ${BREAKPOINT_MD}px) {
    ${(props) =>
      !props.isFixedRoute &&
      css`
        margin: 70px 30px;
        padding: 30px;
      `}

    max-height: calc(100% - 200px);
  }

  // mobile style
  @media all and (max-width: ${BREAKPOINT_MD}px) {
    ${(props) =>
      props.isTripPlannerOpen &&
      props.isFixedRoute &&
      css`
        margin: 0;
        padding: ${props.topOffset + 58}px 30px 0;
        border-radius: 0;
        max-height: 100%;
      `}

    ${(props) =>
      props.isFixedRoute &&
      props.isViewTripState &&
      css`
        padding: 0;
      `};

    ${(props) =>
      props.isTripPlannerOpen &&
      !props.isViewTripState &&
      css`
        z-index: ${ABOVE_MAP_Z_INDEX};
        background-color: #edeeee;
      `};
  }
`;

const StyledHomeMapWrapper = styled.div`
  z-index: ${MAP_Z_INDEX};

  @media all and (min-width: ${BREAKPOINT_MD}px) {
    position: initial;
  }
`;

const StyledFloatingIcon = styled(FloatingIcon)<{ marginTop: number }>`
  position: absolute;
  left: 0;
  z-index: 3;
  margin-top: ${({ marginTop }) => marginTop}px;
  margin-left: 20px;
`;

const StyledRouteFilteringMenuWrapper = styled.div<{ marginTop: number; highlightColour?: string }>`
  position: absolute;
  z-index: 3;
  margin-top: ${({ marginTop }) => marginTop}px;
  cursor: pointer;

  @media all and (max-width: ${BREAKPOINT_MD}px) {
    left: 0;
    margin-left: 20px;
  }

  @media all and (min-width: ${BREAKPOINT_MD}px) {
    right: 40px;
    left: unset;
    margin-right: 20px;
  }

  ${({ highlightColour }) => (highlightColour ? getPointHighlightEffect(highlightColour, -6, -6) : '')}
`;

const StyledRouteFiltering = styled(RouteFiltering)``;

export default Home;
