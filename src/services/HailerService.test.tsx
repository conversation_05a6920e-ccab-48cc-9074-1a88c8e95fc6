import { useContext } from 'react';
import { HailerOpsClient } from '@liftango/ops-client';
import { useHailerService } from './HailerService';
import { HailerContext } from '../context/HailerProvider';

vi.mock('react', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useContext: vi.fn(),
  };
});

describe('useHailerService hook', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('retrieves the HailerOpsClient from the correct context', () => {
    const mockHailerOpsClient: Partial<HailerOpsClient> = {};

    (useContext as vi.Mock).mockReturnValue(mockHailerOpsClient);

    const result = useHailerService();

    expect(useContext).toHaveBeenCalledWith(HailerContext);
    expect(result).toBe(mockHailerOpsClient);
  });
});
