import { FixedRoute } from '@liftango/ops-client';
import { useHailerService } from './HailerService';

const useAlertsApi = (networkId: string) => {
  const hailerService = useHailerService();

  const fetchAllAlerts = async (): Promise<FixedRoute.GetAlertsAlertAttributes[]> => {
    const response: FixedRoute.GetAlertsAllResult = await hailerService.FixedRoute.Alerts.getAllAlerts(networkId);

    if (!response.success) {
      throw new Error('Network response was not ok');
    }
    return response.entries;
  };

  return {
    fetchAllAlerts,
  };
};

export { useAlertsApi };
