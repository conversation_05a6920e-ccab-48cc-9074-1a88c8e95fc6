import { useAlertsApi } from './AlertsApi';
import { useHailerService } from './HailerService';
import { mockAlertsData } from '../__mocks__/mockStore';

vi.mock('./HailerService');

interface MockHailerService {
  FixedRoute: {
    Alerts: {
      getAllAlerts: vi.Mock;
    };
  };
}

describe('useAlertsApi hook', () => {
  let mockHailerService: MockHailerService;

  beforeEach(() => {
    mockHailerService = {
      FixedRoute: {
        Alerts: {
          getAllAlerts: vi.fn(),
        },
      },
    };

    (useHailerService as vi.Mock).mockReturnValue(mockHailerService);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('fetches all alerts successfully from the service', async () => {
    const mockResponse = {
      success: true,
      entries: mockAlertsData,
    };

    mockHailerService.FixedRoute.Alerts.getAllAlerts.mockResolvedValue(mockResponse);

    const { fetchAllAlerts } = useAlertsApi();
    const result = await fetchAllAlerts();

    expect(mockHailerService.FixedRoute.Alerts.getAllAlerts).toHaveBeenCalled();
    expect(result).toEqual(mockAlertsData);
  });

  it('throws an error when the service fails to fetch alerts', async () => {
    const errorMessage = 'Network response was not ok';
    mockHailerService.FixedRoute.Alerts.getAllAlerts.mockRejectedValue(new Error(errorMessage));

    const { fetchAllAlerts } = useAlertsApi();
    await expect(fetchAllAlerts()).rejects.toThrow(errorMessage);
  });
});
