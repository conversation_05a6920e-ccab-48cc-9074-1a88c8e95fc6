/* Ionic Variables and Theming. For more info, please see:
http://ionicframework.com/docs/theming/ */

/** Ionic CSS Variables **/
:root {
  /** 'react-timekeeper' **/
  --main-box-shadow: none;
  --main-font-family: 'Rubik', sans-serif;

  /** font overrides **/
  --ion-font-family: 'Rubik', sans-serif;
  --ion-font-family-header: 'Rubik', sans-serif;

  /** component overrides **/
  --lifty-button--weight: inherit;
  --lifty-button--text-transform: inherit;
  --lifty-loading--background-color: var(--ion-color-primary);
  --lifty-input--font-family: var(--ion-font-family);

  /** theme colours **/
  --ion-color-primary: #000000;
  --ion-color-primary-rgb: 0, 0, 0;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #000000;
  --ion-color-primary-tint: #1a1a1a;

  --ion-color-secondary: #75787b;
  --ion-color-secondary-rgb: 117, 120, 123;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #676a6c;
  --ion-color-secondary-tint: #838688;

  --ion-color-tertiary: #fc4c02;
  --ion-color-tertiary-rgb: 252, 76, 2;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #de4302;
  --ion-color-tertiary-tint: #fc5e1b;

  --ion-color-success: #2dd36f;
  --ion-color-success-rgb: 45, 211, 111;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #28ba62;
  --ion-color-success-tint: #42d77d;

  --ion-color-warning: #fda601;
  --ion-color-warning-rgb: 255, 196, 9;
  --ion-color-warning-contrast: #fff6e7;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;

  --ion-color-danger: #eb445a;
  --ion-color-danger-rgb: 235, 68, 90;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #cf3c4f;
  --ion-color-danger-tint: #ed576b;

  --ion-color-dark: #222428;
  --ion-color-dark-rgb: 34, 36, 40;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;

  --ion-color-medium: #92949c;
  --ion-color-medium-rgb: 146, 148, 156;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #808289;
  --ion-color-medium-tint: #9d9fa6;

  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb: 244, 245, 248;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;

  --ion-border-color: #edeeee;
}

@font-face {
  font-family: 'Rubik';
  src: url('fonts/Rubik-VariableFont_wght.ttf') format('woff2 supports variations'),
    url('fonts/Rubik-VariableFont_wght.ttf') format('woff2-variations');
  font-weight: 300 700;
  font-style: normal;
}

@font-face {
  font-family: 'Rubik';
  src: url('fonts/Rubik-Italic-VariableFont_wght.ttf') format('woff2 supports variations'),
    url('fonts/Rubik-Italic-VariableFont_wght.ttf') format('woff2-variations');
  font-weight: 300 700;
  font-style: italic;
}
