:root {
  /** 'react-timekeeper' **/
  --main-box-shadow: none;
  --main-font-family: 'DMSans', 'Rubik', sans-serif;

  /** font overrides **/
  --ion-font-family: 'DMSans', 'Rubik', sans-serif;
  --ion-font-family-header: 'DMSans', 'Rubik', sans-serif;

  /** component overrides **/
  --lifty-button--weight: 500;
  --lifty-button--text-transform: uppercase;
  --lifty-loading--background-color: #fff;
  --lifty-input--font-family: var(--ion-font-family-header);
}

@font-face {
  font-family: 'DMSans';
  src: url('fonts/DMSans-Regular.otf');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'DMSans';
  src: url('fonts/DMSans-Medium.otf');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'DMSans';
  src: url('fonts/DMSans-Bold.otf');
  font-weight: 700;
  font-style: normal;
}
