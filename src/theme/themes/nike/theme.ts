import { Theme } from '../../../store/reducers/themeReducer';
import nikeLoadingAnimation from '../../themes/nike/loading.json';
import NikeShuttleRoundedSvg from '../../../assets/nike/nike-shuttle-rounded.svg?url';
import NikeShuttleSvg from '../../../assets/nike/nike-shuttle.svg?url';
import NikeLogoPng from '../../../assets/nike/nike-logo-dark.png?url';

export const THEME: Partial<Theme> = {
  name: 'nike',
  loadingMask: JSON.stringify(nikeLoadingAnimation),
  icons: {
    fixedRouteVehicleRounded: NikeShuttleRoundedSvg,
    fixedRouteVehicle: NikeShuttleSvg,
  },
  logo: NikeLogoPng,
  favicon: 'nike-favicon.png',
  cssFilePath: 'nike',
  longWalkTranslationKey: 'trip:longWalkWarningTooltip',
};
