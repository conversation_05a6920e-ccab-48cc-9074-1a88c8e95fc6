:root {
  /** 'react-timekeeper' **/
  --main-box-shadow: none;
  --main-font-family: '<PERSON>lam<PERSON>', 'Rubik', sans-serif;

  /** font overrides **/
  --ion-font-family: '<PERSON>lama', 'Rubik', sans-serif;
  --ion-font-family-header: 'Trade Gothic', 'Flama', 'Rubik', sans-serif;

  /** component overrides **/
  --lifty-button--weight: 500;
  --lifty-button--text-transform: uppercase;
  --lifty-loading--background-color: #fff;
  --lifty-input--font-family: var(--ion-font-family-header);
}

@font-face {
  font-family: 'Flama';
  src: url('fonts/FlamaBook.otf');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Flama';
  src: url('fonts/Flama-Regular.otf');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Flama';
  src: url('fonts/Flama-Bold.otf');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Trade Gothic';
  src: url('fonts/TradeGothicforNike365-BdCn.ttf');
  font-style: normal;
}
