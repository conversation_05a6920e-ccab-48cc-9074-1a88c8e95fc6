#!/bin/bash

SOURCE_BRANCH=$1
TARGET_BRANCH=$2

# Bitbucket variables (update these as per your Bitbucket workspace)
BITBUCKET_API="https://api.bitbucket.org/2.0/repositories/liftango/lifty-lite"
AUTH_HEADER="Authorization: Bearer $BITBUCKET_TOKEN"

# Handle pattern matching e.g. release/* branches
branches=$(git branch --all --list "origin/$TARGET_BRANCH")

branch_array=($branches)

echo "$branch_array"

for branch in "${branch_array[@]}"; do
  TARGET_BRANCH=${branch#remotes/origin/}
  echo "Processing branch $TARGET_BRANCH"

  git checkout "$TARGET_BRANCH"
  git merge "$SOURCE_BRANCH"
  CONFLICT_COUNT=$(git status -s | grep -c -E '^(UU|AA|DU|UA|UD|AU|DD)')
  CONFLICT_ON_VERSION_FILE=$(git status -s | grep -q -E '^(UU|AA|DU|UA|UD|AU|DD) version.json' && echo "yes" || echo "no")

  echo "STATUS: ${STATUS}"
  echo ""

  if [ "$CONFLICT_COUNT" -eq 1 ] && [ "$CONFLICT_ON_VERSION_FILE" = "yes" ]; then
    echo "Only version.json is conflicting. Choosing $TARGET_BRANCH";
    git checkout HEAD ./version.json
    git commit -m "[RELEASE] Auto-merge of $SOURCE_BRANCH into $TARGET_BRANCH after conflict on version.json"
    git push
  elif [ "$CONFLICT_COUNT" -eq 0 ]; then
    echo "Status is clear to complete merge and push";
    git push
  else
    echo "More files conflict than just version.json. Opening PR";

    # Find the owner of the latest commit on the source branch

    LATEST_COMMIT_INFO=$(curl -s -H "$AUTH_HEADER" \
    "$BITBUCKET_API/commits/$SOURCE_BRANCH?pagelen=1")

    echo "$LATEST_COMMIT_INFO" | jq -r '.values[0].author.user.uuid'

    # Extract the author's UUID
    REVIEWER_UUID=$(echo "$LATEST_COMMIT_INFO" | jq -r '.values[0].author.user.uuid | select(. != null)')

    # Check if a UUID was found
    if [ -z "$REVIEWER_UUID" ]; then
      echo "No author UUID found for the latest commit on branch: $SOURCE_BRANCH"
    fi

    SHORT_SHA=$(git rev-parse --short HEAD)
    CONFLICT_BRANCH="conflict/${SOURCE_BRANCH}_into_${TARGET_BRANCH}_${SHORT_SHA}"

    echo "Checking out new branch to handle conflict '${CONFLICT_BRANCH}'"

    git reset --hard HEAD
    git checkout $SOURCE_BRANCH
    git checkout -b "$CONFLICT_BRANCH"
    git push -u origin "$CONFLICT_BRANCH"

    ./pipelines/create-pr.sh "$CONFLICT_BRANCH" "$TARGET_BRANCH" "$REVIEWER_UUID"
  fi;

  git reset --hard HEAD
  git checkout $SOURCE_BRANCH

done