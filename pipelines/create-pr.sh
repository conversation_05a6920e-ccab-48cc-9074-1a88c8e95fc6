#!/bin/bash

SOURCE_BRANCH=$1
TARGET_BRANCH=$2
REVIEWER_ID=$3

# Bitbucket variables (update these as per your Bitbucket workspace)
BITBUCKET_API="https://api.bitbucket.org/2.0/repositories/liftango/lifty-lite"
AUTH_HEADER="Authorization: Bearer $BITBUCKET_TOKEN"

# Fetch the default reviewers from Bitbucket
DEFAULT_REVIEWERS=$(curl -s -H "$AUTH_HEADER" "${BITBUCKET_API}/default-reviewers" \
                  | jq -c '[.values[] | {uuid: .uuid}]')

REVIEWERS="[]"

# Check for both REVIEWER_ID and DEFAULT_REVIEWERS
if [ -n "$REVIEWER_ID" ] && [ -n "$DEFAULT_REVIEWERS" ] && [ "$DEFAULT_REVIEWERS" != "[]" ]; then
  # Combine both REVIEWER_ID and DEFAULT_REVIEWERS into one array
  REVIEWERS=$(echo "$DEFAULT_REVIEWERS" | jq -c --arg reviewer_id "$REVIEWER_ID" '. + [{uuid: $reviewer_id}]')
elif [ -n "$DEFAULT_REVIEWERS" ] && [ "$DEFAULT_REVIEWERS" != "[]" ]; then
  # Use only DEFAULT_REVIEWERS if it's available
  REVIEWERS="$DEFAULT_REVIEWERS"
elif [ -n "$REVIEWER_ID" ]; then
  # Use only REVIEWER_ID if it's available
  REVIEWERS="[ { \"uuid\": \"${REVIEWER_ID}\" } ]"
fi

# Create the pull request
curl -X POST \
-H "Content-Type: application/json" \
-H "$AUTH_HEADER" \
"$BITBUCKET_API/pullrequests" \
-d '{
  "title": "Auto-merge of '"$SOURCE_BRANCH"' into '"$TARGET_BRANCH"' failed",
  "source": {
    "branch": {
      "name": "'"$SOURCE_BRANCH"'"
    }
  },
  "destination": {
    "branch": {
      "name": "'"$TARGET_BRANCH"'"
    }
  },
  "close_source_branch": false,
  "reviewers": '"$REVIEWERS"'
}'