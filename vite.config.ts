/// <reference types="vitest" />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import viteTsconfigPaths from 'vite-tsconfig-paths';
import svgr from 'vite-plugin-svgr';
import dynamicImport from 'vite-plugin-dynamic-import';

export default defineConfig({
  base: '/',
  root: 'src',
  envDir: '../',
  publicDir: '../public',
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['vitest-setup.js'],
  },
  build: {
    emptyOutDir: true,
    outDir: '../build',
  },
  appType: 'spa',
  plugins: [dynamicImport(), svgr(), react(), viteTsconfigPaths()],
  optimizeDeps: {
    esbuildOptions: {
      define: {
        global: 'globalThis',
      },
    },
  },
  server: {
    open: true,
    port: 8080,
  },
});
