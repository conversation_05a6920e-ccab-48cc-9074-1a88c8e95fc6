# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
/cypress/reports/
/cypress/cucumber-json/
/cypress/videos/
.yarn/*
!.yarn/cache
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.vscode
.idea

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Optional eslint cache
.eslintcache

storybook-static/

.env
.terraform*